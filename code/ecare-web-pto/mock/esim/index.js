export default {
  // 'POST /ecare/webs/order/esim-replacement/pre-check': {
  //   code: '200',
  //   resultCode: null,
  //   resultMsg: null,
  //   data: { result: 0 },
  // },
  // 'POST /ecare/webs/order/esim-replacement/support-device-list': (req, res) => {
  //   setTimeout(() => {
  //     res.send({
  //       code: '200',
  //       resultCode: null,
  //       resultMsg: null,
  //       data: [
  //         {
  //           brandName: 'Apple',
  //           brandCode: 'apple',
  //           modelList: [
  //             {
  //               modelCode: 'apple-iphone-14',
  //               modelName: 'Apple iPhone 14',
  //             },
  //             {
  //               modelCode: 'apple-iphone-14-plus',
  //               modelName: 'Apple iPhone 14 Plus',
  //             },
  //             {
  //               modelCode: 'apple-iphone-14-pro',
  //               modelName: 'Apple iPhone 14 Pro',
  //             },
  //             {
  //               modelCode: 'apple-iphone-14-pro-max',
  //               modelName: 'Apple iPhone 14 Pro Max',
  //             },
  //           ],
  //         },
  //         {
  //           brandName: 'Google',
  //           brandCode: 'google',
  //           modelList: [
  //             {
  //               modelCode: 'google-pixel-6',
  //               modelName: 'Google Pixel 6',
  //             },
  //             {
  //               modelCode: 'google-pixel-6-pro',
  //               modelName: 'Google Pixel 6 Pro',
  //             },
  //           ],
  //         },
  //         {
  //           brandName: 'Huawei',
  //           brandCode: 'huawei',
  //           modelList: [
  //             {
  //               modelCode: 'huawei-p40',
  //               modelName: 'Huawei P40',
  //             },
  //             {
  //               modelCode: 'huawei-p40-pro',
  //               modelName: 'Huawei P40 Pro',
  //             },
  //             {
  //               modelCode: 'huawei-p40-pro-max',
  //               modelName: 'Huawei P40 Pro Max',
  //             },
  //           ],
  //         },
  //         {
  //           brandName: 'Oppo',
  //           brandCode: 'oppo',
  //           modelList: [
  //             {
  //               modelCode: 'oppo-find-x3',
  //               modelName: 'Oppo Find X3',
  //             },
  //             {
  //               modelCode: 'oppo-find-x3-pro',
  //               modelName: 'Oppo Find X3 Pro',
  //             },
  //           ],
  //         },
  //       ],
  //     });
  //   }, 2 * 1000);
  // },
  // 'POST /ecare/webs/order/esim-replacement/order': {
  //   code: '200',
  //   resultCode: null,
  //   resultMsg: null,
  //   data: {
  //     orderDetail: {
  //       custOrderId: '34243242432423',
  //       newCustOrderId: 0,
  //       custOrderNbr: '',
  //       orderAuditFlag: false,
  //       custId: '',
  //       custNbr: '',
  //       eventTypeId: {},
  //       eventTypeName: '',
  //       state: '300000',
  //       stateName: '',
  //       totalCharge: '99',
  //       displayTotalCharge: '99.00',
  //       stateDate: '',
  //       acceptDate: '20250808121212',
  //       dispatchedDate: '',
  //       paymentDate: '',
  //       receiveDate: '',
  //       channelId: '',
  //       channelName: '',
  //       partyType: '',
  //       partyCode: '',
  //       trackingNumber: '',
  //       trackingCompany: '',
  //       undoReason: '',
  //       undoReasonName: '',
  //       eorFlag: {},
  //       preferredCallTime: '',
  //       paidFlag: '',
  //       auditReasonList: {
  //         custOrderAuditReasonId: 0,
  //         custOrderAuditId: 0,
  //         auditReasonId: 0,
  //         auditReasonName: '',
  //       },
  //       resReceiveInfo: {
  //         receiveStdAddrId: '',
  //         receiveId: '',
  //         receiveMethod: '',
  //         receiveAddrDetail: '',
  //         receiveName: '',
  //         receiveMobileNo: '',
  //         receiveFullAddr: '',
  //       },
  //       orderFeeInfo: {
  //         custOrderId: '',
  //         totalCharge: '9900',
  //         displayTotalCharge: '99.00',
  //         currencySymbol: '',
  //         orderFeeSumList: {
  //           charge: '',
  //           currencySymbol: '',
  //           displayCharge: '',
  //           tax: '',
  //           chargeWithTax: '',
  //           displayTax: '',
  //           displayChargeWithTax: '',
  //           currencyCode: '',
  //         },
  //         benefitInfoList: {
  //           acctResId: '',
  //           acctResCode: '',
  //           acctResName: '',
  //           accNbr: '',
  //           amount: '',
  //           currencySymbol: '',
  //           displayAmount: '',
  //           offerId: '',
  //           offerName: '',
  //           offerInstId: '',
  //           orderItemId: '',
  //           precision: '',
  //           unitType: '',
  //         },
  //         orderFeeDetailList: {
  //           accNbr: '',
  //           priceId: '',
  //           acctItemTypeId: '',
  //           acctItemTypeName: '',
  //           adjsutRemarks: '',
  //           adjustAmount: '',
  //           adjustDate: '',
  //           adjustPartyCode: '',
  //           adjustPartyName: '',
  //           adjustPartyType: '',
  //           amount: '',
  //           amountWithTax: '',
  //           currencySymbol: '',
  //           custOrderId: '',
  //           dispayReceivableAmount: '',
  //           displayAdjustAmount: '',
  //           displayAmount: '',
  //           displayAmountWithTax: '',
  //           displayTax: '',
  //           offerId: '',
  //           offerCode: '',
  //           offerName: '',
  //           orderItemId: '',
  //           paymentState: '',
  //           paymentType: '',
  //           precision: '',
  //           receivableAmount: '',
  //           tax: '',
  //         },
  //         depositInfoList: {
  //           depositId: '',
  //           priceId: '',
  //           depositTypeId: '',
  //           depositTypeName: '',
  //           applyObj: '',
  //           amount: '',
  //           displayAmount: '',
  //           currencyTypeId: '',
  //           currencyCode: '',
  //           currencySymbol: '',
  //           displayScale: '',
  //           savePrecision: '',
  //           roundMethod: '',
  //           offerId: '',
  //           offerCode: '',
  //           offerName: '',
  //           accNbr: '',
  //           remarks: '',
  //           subsId: '',
  //           depositClass: '',
  //         },
  //       },
  //       orderItemList: [
  //         {
  //           orderItemId: '',
  //           orderItemNbr: '',
  //           custOrderId: '',
  //           subsEventId: '',
  //           subsEventName: '',
  //           custId: '',
  //           acctId: '',
  //           newCustId: '',
  //           newAcctId: '',
  //           acctNbr: '',
  //           accNbrFee: 0,
  //           displayAccNbrFee: '',
  //           subsId: '',
  //           prefix: '',
  //           accNbr: '',
  //           primaryFlag: '',
  //           primaryAccNbr: '',
  //           paymentAcctId: '',
  //           paymentAcctNbr: '',
  //           mainOfferId: '',
  //           mainOfferCode: '',
  //           mainOfferType: '主套餐类型',
  //           mainOfferName: '',
  //           mainOfferBrief: '',
  //           mainOfferThumbImageUrl: '',
  //           newMainOfferId: '',
  //           newMainOfferCode: '',
  //           newMainOfferName: '',
  //           newMainOfferBrief: '',
  //           newMainOfferThumbImageUrl: '',
  //           state: '',
  //           stateName: '',
  //           createdDate: '',
  //           stateDate: '',
  //           closedDate: '',
  //           acceptDate: '',
  //           reason: '',
  //           partyType: '',
  //           partyCode: '',
  //           partyName: '',
  //           remark: '',
  //           offerInstList: [
  //             {
  //               offerInstId: '',
  //               offerId: '',
  //               offerCode: '',
  //               offerName: '',
  //               brief: '',
  //               thumbImageUrl: '',
  //               createDate: '',
  //               state: '',
  //               stateName: '',
  //               stateDate: '',
  //               effDate: '',
  //               expDate: '',
  //               offerType: '',
  //               offerCategory: '',
  //               remarks: '',
  //               actType: '',
  //               effType: '',
  //               rentPrice: 0,
  //               salePrice: 0,
  //               guidingRentPrice: 0,
  //               displaySalesPrice: 0,
  //               displayRentPrice: '',
  //               displayGuidingRentPrice: '',
  //               currencySymbol: '',
  //               discountTimes: 0,
  //             },
  //           ],
  //           prodInstList: [
  //             {
  //               prodInstId: '',
  //               prodId: '',
  //               prodCode: '',
  //               prodName: '',
  //               brief: '',
  //               thumbImageUrl: '',
  //               state: '',
  //               stateName: '',
  //               createDate: '',
  //               stateDate: '',
  //               effDate: '',
  //               expDate: '',
  //               prodType: '',
  //               actType: '',
  //             },
  //           ],
  //           resInstList: [
  //             {
  //               resInstId: '',
  //               resInstNbr: '',
  //               resType: '',
  //               resId: '',
  //               resCode: '',
  //               resName: '',
  //               offerCode: '',
  //               brief: '',
  //               thumbImageUrl: '',
  //               qty: '',
  //               actType: '',
  //               salePrice: 0,
  //               displaySalesPrice: '',
  //               currencySymbol: '',
  //             },
  //           ],
  //           attrValueList: [
  //             {
  //               attrId: '',
  //               attrCode: '',
  //               attrName: '',
  //               attrValueId: '',
  //               value: '',
  //               valueName: '',
  //             },
  //           ],
  //           undoReason: '',
  //           undoReasonName: '',
  //         },
  //       ],
  //       attrValueList: [
  //         {
  //           attrId: '',
  //           attrCode: '',
  //           attrName: '',
  //           attrValueId: '',
  //           value: '',
  //           valueName: '',
  //         },
  //       ],
  //       recommendOfferList: [
  //         {
  //           offerInstId: '',
  //           offerId: '',
  //           offerCode: '',
  //           offerName: '',
  //           brief: '',
  //           thumbImageUrl: '',
  //           createDate: '',
  //           state: '',
  //           stateName: '',
  //           stateDate: '',
  //           effDate: '',
  //           expDate: '',
  //           offerType: '',
  //           offerCategory: '',
  //           remarks: '',
  //           actType: '',
  //           effType: '',
  //           rentPrice: 0,
  //           salePrice: 0,
  //           guidingRentPrice: 0,
  //           displaySalesPrice: 0,
  //           displayRentPrice: '',
  //           displayGuidingRentPrice: '',
  //           currencySymbol: '',
  //           discountTimes: 0,
  //         },
  //       ],
  //       businessScene: '',
  //       needSelectHandset: '',
  //     },
  //   },
  // },
  // 'POST /ecare/webs/message/sms/captcha/send': {
  //   code: '200',
  //   resultCode: null,
  //   resultMsg: null,
  //   data: { result: 1 },
  // },
  // 'POST /ecare/webs/message/sms/20/111111/9202106404': {
  //   code: '200',
  //   resultCode: null,
  //   resultMsg: null,
  // },
  // 'POST /ecare/webs/order/esim-replacement/save-order': (req, res) => {
  //   setTimeout(() => {
  //     res.send({
  //       code: '200',
  //       resultCode: '',
  //       resultMsg: '',
  //       data: {
  //         custOrderId: '1242131321',
  //         transactionId: 'DAD12-DSFDS',
  //         orderItemList: [
  //           {
  //             accNbr: '9202106404',
  //             prodTypeCode: 'ESIM',
  //           },
  //         ],
  //         feeInfo: {
  //           totalFee: 990000,
  //           displayTotalFee: '99.00',
  //         },
  //       },
  //     });
  //   }, 3e3);
  // },
  // 'POST /ecare/webs/ijoin/payment': (req, res) => {
  //   setTimeout(() => {
  //     res.send({
  //       code: '200',
  //       data: {
  //         custOrderId: '1242131321',
  //         transactionId: 'DAD12-DSFDS',
  //       },
  //     });
  //   }, 3e3);
  // },
  // 'POST /ecare/webs/inventory/simCard': (req, res) => {
  //   setTimeout(() => {
  //     res.send({
  //       code: '200',
  //       resultCode: '',
  //       resultMsg: '',
  //       data: {
  //         simCard: {
  //           eSimProfileURI: 'https://dito.ph/',
  //         },
  //       },
  //     });
  //   }, 3e3);
  // },
};
