export default {
  // 'POST /ecare/webs/point/detail': {
  //   pointCount: '663471',
  //   usablePoint: '2302.65',
  //   pointDue: '0',
  //   usedPoint: '0',
  //   pointLastMonthAdd: '118499',
  //   expiringByMonthEnd: '0.00',
  //   pageInfo: null,
  //   transferablePoint: '2302.65',
  //   freezFlag: 'Y',
  // },
  // 'POST /ecare/webs/cust/detail': {
  //   custId: 204414,
  //   custNbr: '1731002662830000',
  //   firstName: 'zhang',
  //   middleName: 'weng',
  //   lastName: 'wen',
  //   custType: '1198',
  //   custName: 'zhang weng wen',
  //   certTypeId: null,
  //   certTypeCode: null,
  //   certTypeName: null,
  //   certNbr: null,
  //   stdAddrId: '106718',
  //   address: 'Ambago,CITY OF BUTUAN (Capital),AGUSAN DEL NORTE,R<PERSON><PERSON> XIII (Caraga),MIN,Philippines',
  //   birthday: null,
  //   gender: '2',
  //   occupation: null,
  //   attachList: [
  //     {
  //       attachId: 102195,
  //       attachType: '2',
  //       fileName: 'FamilyNumberdfs170065458670447625.png',
  //       url: '/custcFile/2023/11/22/20',
  //       fileType: null,
  //       fileSize: '30148',
  //       actType: null,
  //     },
  //     {
  //       attachId: 102196,
  //       attachType: '3',
  //       fileName: 'FamilyNumberdfs170065459443372288.png',
  //       url: '/custcFile/2023/11/22/20',
  //       fileType: null,
  //       fileSize: '30148',
  //       actType: null,
  //     },
  //     {
  //       attachId: 102041,
  //       attachType: '1',
  //       fileName: 'id1169951345673041543.jpeg',
  //       url: '/custcFile/2023/11/09/15',
  //       fileType: null,
  //       fileSize: '10105',
  //       actType: null,
  //     },
  //   ],
  //   contactList: [],
  //   custAttrList: [
  //     {
  //       attrId: 61187,
  //       attrCode: 'SALUTATION',
  //       attrName: 'Salutation',
  //       attrValueId: 710761,
  //       value: '1',
  //       oldValue: null,
  //       valueName: 'Mr.',
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 60006,
  //       attrCode: 'FIRST_NAME',
  //       attrName: 'First Name',
  //       attrValueId: null,
  //       value: 'zhang',
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 60007,
  //       attrCode: 'MIDDLE_NAME',
  //       attrName: 'Middle Name',
  //       attrValueId: null,
  //       value: 'weng',
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 60008,
  //       attrCode: 'SUR_NAME',
  //       attrName: 'Surname',
  //       attrValueId: null,
  //       value: 'wen',
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 61188,
  //       attrCode: 'SUFFIX',
  //       attrName: 'Suffix',
  //       attrValueId: null,
  //       value: null,
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 230000078,
  //       attrCode: 'PTY-0008',
  //       attrName: 'Gender',
  //       attrValueId: 240000365,
  //       value: '2',
  //       oldValue: null,
  //       valueName: 'Female',
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 230000075,
  //       attrCode: 'PTY-0005',
  //       attrName: 'Nationality',
  //       attrValueId: 240000345,
  //       value: 'P',
  //       oldValue: null,
  //       valueName: 'Philippines',
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 61191,
  //       attrCode: 'TIN',
  //       attrName: 'TIN',
  //       attrValueId: null,
  //       value: '123-123-123-123',
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 60433,
  //       attrCode: 'MOTHERS_MAIDEN_NAME',
  //       attrName: "Mother's Maiden Name",
  //       attrValueId: null,
  //       value: 'water',
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 230000121,
  //       attrCode: 'CUS-0004',
  //       attrName: 'Customer level',
  //       attrValueId: 240000547,
  //       value: '1000',
  //       oldValue: null,
  //       valueName: 'Gold',
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 61192,
  //       attrCode: 'RECEIVE_UPDATES',
  //       attrName: 'Receive Updates',
  //       attrValueId: 710778,
  //       value: '0',
  //       oldValue: null,
  //       valueName: 'Yes, please send me updates',
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 60549,
  //       attrCode: 'PTY-0029',
  //       attrName: 'Civil Status',
  //       attrValueId: 240100219,
  //       value: '120000',
  //       oldValue: null,
  //       valueName: 'Married',
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 60542,
  //       attrCode: 'MAX_MONTHLY_FEE',
  //       attrName: 'Max Monthly Fee',
  //       attrValueId: null,
  //       value: null,
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 60541,
  //       attrCode: 'MAX_CREDIT_LIMIT',
  //       attrName: 'Max Credit Limit',
  //       attrValueId: null,
  //       value: null,
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 61405,
  //       attrCode: 'REMAINING_MONTHLY_FEE',
  //       attrName: 'Remaining Allowable MSF',
  //       attrValueId: null,
  //       value: null,
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 61406,
  //       attrCode: 'REMAINING_CREDIT_LIMIT',
  //       attrName: 'Remaining Allowable Credit Limit',
  //       attrValueId: null,
  //       value: null,
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 60545,
  //       attrCode: 'BUSINESS_STYLE',
  //       attrName: 'Business style',
  //       attrValueId: null,
  //       value: 'individual',
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 60047,
  //       attrCode: 'CC-CUST-PROFILE',
  //       attrName: 'Customer Profile',
  //       attrValueId: 201811049108,
  //       value: '1770',
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 60060,
  //       attrCode: 'EXP_TAX_FREE',
  //       attrName: 'VAT Free',
  //       attrValueId: null,
  //       value: 'N',
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 1900204281,
  //       attrCode: 'MASK',
  //       attrName: 'Customer Mask',
  //       attrValueId: 201811049039,
  //       value: '6656',
  //       oldValue: null,
  //       valueName: 'Individual person',
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 1900218377,
  //       attrCode: 'ACCEPT_STATEMENT_DATE',
  //       attrName: 'Privacy Statement Date',
  //       attrValueId: null,
  //       value: '2024-04-17 15:33:31',
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 1900218475,
  //       attrCode: 'CUSTC_FITNESS_AND_WELLNESS',
  //       attrName: 'Fitness and Wellness',
  //       attrValueId: null,
  //       value: '4,5',
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 1900218474,
  //       attrCode: 'CUSTC_SHOPPING_AND_FASHION',
  //       attrName: 'Shopping and Fashion',
  //       attrValueId: 1000002680,
  //       value: '3',
  //       oldValue: null,
  //       valueName: 'Fashion and Accessories',
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 1900218478,
  //       attrCode: 'CUSTC_JOB_LEVEL',
  //       attrName: 'Job Level',
  //       attrValueId: 1000002690,
  //       value: '3',
  //       oldValue: null,
  //       valueName: 'Mid-level',
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 1900218477,
  //       attrCode: 'CUSTC_BIRTH_YEAR',
  //       attrName: 'Birth Year',
  //       attrValueId: null,
  //       value: '1991',
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 230000081,
  //       attrCode: 'PTY-0011',
  //       attrName: 'Occupation',
  //       attrValueId: null,
  //       value: '*********',
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 1900218473,
  //       attrCode: 'CUSTC_HOBBIES',
  //       attrName: 'Hobbies',
  //       attrValueId: null,
  //       value: '1,2,4',
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 60544,
  //       attrCode: 'MONTHLY_INCOME',
  //       attrName: 'Monthly Income',
  //       attrValueId: 1000002523,
  //       value: '104',
  //       oldValue: null,
  //       valueName: 'P21,000 - P23,999.99',
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 1900218472,
  //       attrCode: 'CUST_FOOD_AND_DRINK',
  //       attrName: 'Food & Drink',
  //       attrValueId: null,
  //       value: '2,3',
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 1900218471,
  //       attrCode: 'CUST_ENTERTAINMENT',
  //       attrName: 'Entertainment',
  //       attrValueId: null,
  //       value: '2,3,6',
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 61403,
  //       attrCode: 'CUST_IMPROVE_FLAG',
  //       attrName: '',
  //       attrValueId: 718104,
  //       value: 'Y',
  //       oldValue: null,
  //       valueName: 'Yes',
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 1900218521,
  //       attrCode: 'CUST_PROFILE_MOD_FLAG',
  //       attrName: 'Customer Profile Modification flag',
  //       attrValueId: null,
  //       value: 'Y',
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 61412,
  //       attrCode: 'CUST_CHECK_MIDDLE_NAME',
  //       attrName: 'Check Middle Name',
  //       attrValueId: null,
  //       value: 'T',
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 61411,
  //       attrCode: 'CUST_CREDIT_LABEL',
  //       attrName: 'Customer Credit Label',
  //       attrValueId: 718146,
  //       value: '1',
  //       oldValue: null,
  //       valueName: 'Individual Taxpayers',
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 61421,
  //       attrCode: 'APPLICATION_TYPE',
  //       attrName: 'Application Type',
  //       attrValueId: null,
  //       value: null,
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 60543,
  //       attrCode: 'EMPLOYMENT_STATUS',
  //       attrName: 'Employment Status',
  //       attrValueId: null,
  //       value: null,
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 61400,
  //       attrCode: 'RECEIVE_UPDATES_REASON',
  //       attrName: 'Opt-out Reason',
  //       attrValueId: null,
  //       value: '',
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 61401,
  //       attrCode: 'RECEIVE_UPDATES_REASON_OTHERS',
  //       attrName: 'Other Reason',
  //       attrValueId: null,
  //       value: null,
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 61413,
  //       attrCode: 'CUST_IDENTITY',
  //       attrName: 'Identity',
  //       attrValueId: 718151,
  //       value: '3',
  //       oldValue: null,
  //       valueName: 'Foreigner',
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 61414,
  //       attrCode: 'CUST_REGIST_CONTRACT_NO',
  //       attrName: 'Customer Registration Contract No',
  //       attrValueId: null,
  //       value: '92311090483967',
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 61458,
  //       attrCode: 'VALIDITY_DATE',
  //       attrName: 'validity Date',
  //       attrValueId: null,
  //       value: '2099-12-31 00:00:00',
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //     {
  //       attrId: 230000119,
  //       attrCode: 'CUS-0002',
  //       attrName: 'Customer strategy subordinate grouping',
  //       attrValueId: null,
  //       value: '9901',
  //       oldValue: null,
  //       valueName: null,
  //       oldValueName: null,
  //       attrValueSort: null,
  //     },
  //   ],
  //   acctList: [],
  //   partyCertList: [
  //     {
  //       partyCertId: 151968,
  //       certTypeId: null,
  //       certTypeCode: '40',
  //       certTypeName: null,
  //       certNbr: '346464646434',
  //       certOrg: null,
  //       effDate: null,
  //       expDate: '19700101',
  //       certAddress: null,
  //       primaryFlag: 'Y',
  //       certIssueDate: null,
  //       certIssueCountry: null,
  //     },
  //     {
  //       partyCertId: 151969,
  //       certTypeId: null,
  //       certTypeCode: '1561',
  //       certTypeName: null,
  //       certNbr: '97347994646',
  //       certOrg: null,
  //       effDate: null,
  //       expDate: '19700101',
  //       certAddress: null,
  //       primaryFlag: 'N',
  //       certIssueDate: null,
  //       certIssueCountry: null,
  //     },
  //   ],
  //   custAddrList: [
  //     {
  //       stdAddrId: 106718,
  //       stdAddrNbr: null,
  //       stdAddrName: null,
  //       addrDetail: null,
  //       postCode: '6463',
  //       address1: null,
  //       address2: null,
  //       contactNbr: null,
  //       contactEmail: null,
  //       contactName: null,
  //       displayAddr: null,
  //       longitude: null,
  //       latitude: null,
  //       landmark: null,
  //       addrExtMap: null,
  //       custAddressId: null,
  //       defaultFlag: null,
  //       deliveryMethodCode: '',
  //       deliveryMethodName: null,
  //       deliveryCycle: null,
  //       postAddr: null,
  //       postFullAddr: 'hdjdjd,rhudjc,jrjdjf11',
  //       postFullAddr1: 'hdjdjd,rhudjc,jrjdjf11',
  //       postFullAddr2: null,
  //       postName: null,
  //       email: '<EMAIL>',
  //       ccEmail: null,
  //       smsNbr: '638*********',
  //       faxNbr: null,
  //       actType: null,
  //       stdAddr: null,
  //       billDeliveryInfoMap: {
  //         officeProvinceAddrId: null,
  //         provinceAddrId: '106607',
  //         occupation: '*********',
  //         officeTelNbr: null,
  //         officeDistrictAddrId: null,
  //         officePostFullAddr: null,
  //         officePostCode: null,
  //         jobTitle: null,
  //         officeCityAddrId: null,
  //         districtAddrId: 106715,
  //         yearsInCompany: null,
  //         officeAltOfficeNbr: null,
  //         officeAddrId: null,
  //         employer: null,
  //         cityAddrId: 106608,
  //         position: null,
  //       },
  //     },
  //   ],
  //   custExtMap: {
  //     dateofBirth: '2000-01-01 00:00:00',
  //     idType: '40',
  //     contactInfo: {
  //       status_date: '',
  //       action_type: 'M',
  //       remark: '',
  //       alternative_email: '',
  //       update_staff: '-1',
  //       contact_id: '230583',
  //       yx_code: '',
  //       cust_order_id: '82404180522813',
  //       update_name: '',
  //       contact_type: '',
  //       contact_addr: '',
  //       contact_gender: '',
  //       status_cd: '1000',
  //       mobile_phone: '08*********',
  //       e_mail: '<EMAIL>',
  //       fax: '',
  //       create_date: '2022-06-10 15:07:46',
  //       home_phone: '',
  //       record_exp_date: '2048-12-31 00:00:00',
  //       contact_name: '',
  //       ext_contact_id: '',
  //       postcode: '',
  //       record_eff_date: '2022-06-10 15:07:46',
  //       post_addr: '',
  //       create_staff: '1',
  //       home_phone_area_leng: '',
  //       update_date: '2024-04-18 09:54:37',
  //       office_phone: '',
  //       order_item_id: '7312404186688229',
  //       unsuitable_contact_time: '',
  //       qq_code: '',
  //       contact_desc: '',
  //       seq_billing: '',
  //       party_id: '83994',
  //       alternative_mobile_phone: '639202106699',
  //       contact_employer: '',
  //       wx_code: '',
  //       create_name: 'Administrator',
  //     },
  //     documents: [
  //       {
  //         docId: 102195,
  //         docNumber: null,
  //         docName: 'FamilyNumberdfs170065458670447625.png',
  //         docDesc: null,
  //         docSize: 30148,
  //         docLocation: '/custcFile/2023/11/22/20',
  //         status: 'A',
  //         createdDate: '2023-11-22 20:03:19',
  //         createBy: 1,
  //         updateDate: '2023-11-22 20:03:19',
  //         docType: '2',
  //         custOrderId: null,
  //         orderItemId: null,
  //       },
  //       {
  //         docId: 102196,
  //         docNumber: null,
  //         docName: 'FamilyNumberdfs170065459443372288.png',
  //         docDesc: null,
  //         docSize: 30148,
  //         docLocation: '/custcFile/2023/11/22/20',
  //         status: 'A',
  //         createdDate: '2023-11-22 20:03:19',
  //         createBy: 1,
  //         updateDate: '2023-11-22 20:03:19',
  //         docType: '3',
  //         custOrderId: null,
  //         orderItemId: null,
  //       },
  //       {
  //         docId: 102041,
  //         docNumber: null,
  //         docName: 'id1169951345673041543.jpeg',
  //         docDesc: 'id1169951345673041543.jpeg',
  //         docSize: 10105,
  //         docLocation: '/custcFile/2023/11/09/15',
  //         status: 'A',
  //         createdDate: '2023-11-09 15:04:19',
  //         createBy: 1965868,
  //         updateDate: '2023-11-09 15:04:19',
  //         docType: '1',
  //         custOrderId: null,
  //         orderItemId: null,
  //       },
  //     ],
  //     partyCertDoExt: {
  //       statusDate: '2023-05-26 00:00:00',
  //       tableSuffix: null,
  //       updateDate: '2023-05-26 00:00:00',
  //       isRealnameCert: '10',
  //       certType: '1561',
  //       certCheckResult: null,
  //       orderItemId: 7312311226640167,
  //       certAddr: null,
  //       remark: null,
  //       statusCd: '1000',
  //       partyCertId: 151969,
  //       checker: 1,
  //       primaryFlag: 'N',
  //       expDate: '1970-01-01 00:00:00',
  //       createStaff: 1,
  //       actionType: null,
  //       effDate: '2022-06-10 15:07:42',
  //       checkTime: '2022-06-10 15:07:43',
  //       custOrderId: 82311220486225,
  //       partyId: 83994,
  //       certNum: '97347994646',
  //       certOrg: null,
  //       createDate: '2023-05-26 00:00:00',
  //       updateStaff: null,
  //     },
  //     idTypeExt: '1561',
  //     partyCerts: [
  //       {
  //         statusDate: '2023-05-26 00:00:00',
  //         tableSuffix: null,
  //         updateDate: '2023-11-22 20:03:18',
  //         isRealnameCert: '10',
  //         certType: '40',
  //         certCheckResult: null,
  //         orderItemId: 7312311226640167,
  //         certAddr: null,
  //         remark: null,
  //         statusCd: '1000',
  //         partyCertId: 151968,
  //         checker: 1,
  //         primaryFlag: 'Y',
  //         expDate: '1970-01-01 00:00:00',
  //         createStaff: 1,
  //         actionType: null,
  //         effDate: '2022-06-10 15:07:42',
  //         checkTime: '2022-06-10 15:07:43',
  //         custOrderId: 82311220486225,
  //         partyId: 83994,
  //         certNum: '346464646434',
  //         certOrg: null,
  //         createDate: '2023-05-26 00:00:00',
  //         updateStaff: 1,
  //       },
  //       {
  //         statusDate: '2023-05-26 00:00:00',
  //         tableSuffix: null,
  //         updateDate: '2023-05-26 00:00:00',
  //         isRealnameCert: '10',
  //         certType: '1561',
  //         certCheckResult: null,
  //         orderItemId: 7312311226640167,
  //         certAddr: null,
  //         remark: null,
  //         statusCd: '1000',
  //         partyCertId: 151969,
  //         checker: 1,
  //         primaryFlag: 'N',
  //         expDate: '1970-01-01 00:00:00',
  //         createStaff: 1,
  //         actionType: null,
  //         effDate: '2022-06-10 15:07:42',
  //         checkTime: '2022-06-10 15:07:43',
  //         custOrderId: 82311220486225,
  //         partyId: 83994,
  //         certNum: '97347994646',
  //         certOrg: null,
  //         createDate: '2023-05-26 00:00:00',
  //         updateStaff: null,
  //       },
  //     ],
  //     idNumber: '346464646434',
  //     expDate: '1970-01-01 00:00:00',
  //     custContactInfoRel: {
  //       statusDate: '2022-06-10 15:07:46',
  //       tableSuffix: null,
  //       updateDate: '2024-04-18 09:54:37',
  //       recordEffDate: null,
  //       contactId: 230583,
  //       orderItemId: 7312404186688229,
  //       remark: null,
  //       statusCd: '1000',
  //       createStaff: 1,
  //       actionType: 'M',
  //       seqBilling: null,
  //       headFlag: 1,
  //       extContactId: null,
  //       custId: 204414,
  //       custConnectId: 229426,
  //       custOrderId: 82404180522813,
  //       seqNbr: null,
  //       createDate: '2022-06-10 15:07:46',
  //       recordExpDate: null,
  //       updateStaff: -1,
  //     },
  //     partyInd: {
  //       statusDate: null,
  //       birthday: '2000-01-01 00:00:00',
  //       tableSuffix: null,
  //       updateDate: null,
  //       education: null,
  //       occupation: null,
  //       gender: '2',
  //       nation: null,
  //       yazc: null,
  //       remark: null,
  //       individualId: 22837,
  //       birthPlace: null,
  //       skill: null,
  //       employer: null,
  //       schoolName: null,
  //       partyId: 83994,
  //       createDate: null,
  //       gradeName: null,
  //       website: null,
  //       race: null,
  //       orderItemId: null,
  //       statusCd: null,
  //       createStaff: null,
  //       religion: null,
  //       collegeName: null,
  //       actionType: null,
  //       nationality: null,
  //       custOrderId: null,
  //       position: null,
  //       maritalStatus: null,
  //       updateStaff: null,
  //     },
  //     partyCertDo: {
  //       statusDate: '2023-05-26 00:00:00',
  //       tableSuffix: null,
  //       updateDate: '2023-11-22 20:03:18',
  //       isRealnameCert: '10',
  //       certType: '40',
  //       certCheckResult: null,
  //       orderItemId: 7312311226640167,
  //       certAddr: null,
  //       remark: null,
  //       statusCd: '1000',
  //       partyCertId: 151968,
  //       checker: 1,
  //       primaryFlag: 'Y',
  //       expDate: '1970-01-01 00:00:00',
  //       createStaff: 1,
  //       actionType: null,
  //       effDate: '2022-06-10 15:07:42',
  //       checkTime: '2022-06-10 15:07:43',
  //       custOrderId: 82311220486225,
  //       partyId: 83994,
  //       certNum: '346464646434',
  //       certOrg: null,
  //       createDate: '2023-05-26 00:00:00',
  //       updateStaff: 1,
  //     },
  //     idNumberExt: '97347994646',
  //     custOrderId: 82404180522813,
  //     lastOrderItemId: 7312404186688229,
  //   },
  //   postCode: null,
  //   title: null,
  //   regDate: null,
  //   isVerified: null,
  //   potentialFlag: null,
  //   nationality: null,
  //   custCatgList: null,
  //   socialList: null,
  //   fullAddress: null,
  //   // loginFlag: 'N',
  // },
};
