// import { data, boostersData, categories, mccmOffers, orders, offerDetailRes, mccmDetail } from './data';

export default {
  // 'POST /ecare/order/userOrder/list': (req, res) => {
  //   setTimeout(() => {
  //     res.send({
  //       code: '200',
  //       data: {
  //         orderList: orders,
  //         pageInfo: {
  //           pageNum: 1,
  //           pageSize: 20,
  //           total: 20,
  //           order: null,
  //           lastRecordGMT: null,
  //           scrollDirection: null,
  //         },
  //       },
  //     });
  //   }, 200);
  // },
  // 'POST /ecare/webs/shop/catg/list': (req, res) => {
  //   setTimeout(() => {
  //     res.send(categories);
  //   }, 1000);
  // },
  // 'POST /ecare/webs/order/buy-promo/coupon': {
  //   code: '200',
  //   resultCode: null,
  //   resultMsg: null,
  //   data: {
  //     couponCode: 'DITOPROMO',
  //     discountInfo: '10% OFF',
  //   },
  // },
  // 'POST /ecare/webs/offer/promo/list': (req, res) => {
  //   const { pageInfo } = req.body;
  //   const { pageSize, currentPage } = pageInfo;
  //   const list = data.slice((currentPage - 1) * pageSize, currentPage * pageSize);
  //   setTimeout(() => {
  //     res.send({
  //       data: {
  //         promoList: list,
  //         pageInfo: {
  //           total: boostersData.length,
  //           pageSize: pageInfo.pageSize,
  //           currentPage: pageInfo.currentPage,
  //         },
  //       },
  //     });
  //   }, 1000);
  // },
  // 'POST /dmc/mccm/market/MarketingController/qryCampInfo': (req, res) => {
  //   setTimeout(() => {
  //     res.send(mccmOffers);
  //   }, 1000);
  // },
  // 'POST /dmc/mccm/market/MarketingController/qryMarketContactDetail': (req, res) => {
  //   setTimeout(() => {
  //     res.send(mccmDetail);
  //   }, 1000);
  // },
  // 'POST /ecare/webs/mccm/creative/list': {
  //   channelCode: null,
  //   h5Url: 'http://*************:8588/download/buyPromoApp',
  //   adviceCode: null,
  //   countdownShowType: null,
  //   acmValue: null,
  //   currentTime: null,
  //   bannerType: null,
  //   order: 0,
  //   creativeList: [
  //     {
  //       subsId: 1135596,
  //       accNbr: '9202106404',
  //       channelCode: 'APP',
  //       adviceCode: 'ECARE_APP_PROMO',
  //       contactId: '-852581',
  //       treatmentInstId: null,
  //       treatmentInstCode: '-21077',
  //       contactOrderTime: null,
  //       campaignId: 16031,
  //       campaignName: 'test_0605_01',
  //       campaignDescription: '',
  //       campaignPriority: null,
  //       startDate: '2024-06-05 14:20:32',
  //       endDate: '2024-08-31 23:59:59',
  //       recommendedWordsType: 11,
  //       recommendedWords: 'Cotton Candy',
  //       thumbUrl:
  //         'http://*************:80/scd_ceeattach/fileupload//cegFiles/20230406/739127da66bb211389e687bd4d63c6ee.jpg',
  //       url:
  //         'http://*************:8588/download/specialpromo?subsId=1135596&treatmentInstCode=-21077&contactId=-852581',
  //       mccmWebSite: 'http://*************:80/cegH5/',
  //       mccmCmsURL: 'DynamicRender',
  //       creativeJson: null,
  //       creativeType: null,
  //       creativeCode: '00100019619###1',
  //       mediaType: '1',
  //       videoUrl: null,
  //       buttonUrl: null,
  //       offerList: null,
  //       offerGroupList: [
  //         {
  //           offerGroupId: 79,
  //           offerGroupCode: null,
  //           offerGroupName: 'test_0606',
  //           offerGroupDescription: null,
  //           offerGroupUrl: null,
  //           offerGroupImgTips: null,
  //           offerList: [
  //             {
  //               offerId: 100500001,
  //               offerName: '100GB CUL 299.00 per Month',
  //               offerType: '1',
  //               offerDescription: '',
  //               offerCode: null,
  //               offerUrl: 'http://*************:9888//CONTROLLED/media/202406/06/20240606111006941jpg.jpg',
  //               price: 299000000,
  //               displayPrice: '299.00',
  //               salePrice: 269100000,
  //               displaySalePrice: '269',
  //               discountRate: '10',
  //               currencySymbol: 'P',
  //               otcPrice: 269100000,
  //               precision: 1000000,
  //               relatedOfferId: 100500001,
  //               ceeOfferNbr: '389623027176550404',
  //               rewardId: 202,
  //             },
  //             {
  //               offerId: 100500003,
  //               offerName: '500GB CUL 555.00 per Month',
  //               offerType: '1',
  //               offerDescription: '',
  //               offerCode: null,
  //               offerUrl: 'http://*************:9888//CONTROLLED/media/202406/06/20240606111222536jpg.jpg',
  //               price: 555000000,
  //               displayPrice: '555.00',
  //               salePrice: 525000000,
  //               displaySalePrice: '525',
  //               discountRate: '5',
  //               currencySymbol: 'P',
  //               otcPrice: 525000000,
  //               precision: 1000000,
  //               relatedOfferId: 100500003,
  //               ceeOfferNbr: '390162459042385922',
  //               rewardId: 204,
  //             },
  //             {
  //               offerId: 100500004,
  //               offerName: 'Super Star-1 779.00 per Month',
  //               offerType: '1',
  //               offerDescription: '',
  //               offerCode: null,
  //               offerUrl: null,
  //               price: 779000000,
  //               displayPrice: '779.00',
  //               salePrice: 720000000,
  //               displaySalePrice: '720',
  //               discountRate: '7',
  //               currencySymbol: 'P',
  //               otcPrice: 720000000,
  //               precision: 1000000,
  //               relatedOfferId: 100500004,
  //               ceeOfferNbr: '390163368883392516',
  //               rewardId: 205,
  //             },
  //           ],
  //         },
  //       ],
  //       paymentMethodList: [],
  //       thirdPayMethodList: [],
  //       application: '-',
  //       adTitle: '',
  //       promotionId: null,
  //       batchId: '34214',
  //       campaignCode: 'DMC20240605016031',
  //     },
  //     {
  //       subsId: 1135596,
  //       accNbr: '9202106404',
  //       channelCode: 'APP',
  //       adviceCode: 'ECARE_APP_PROMO',
  //       contactId: '-852582',
  //       treatmentInstId: null,
  //       treatmentInstCode: '-21078',
  //       contactOrderTime: null,
  //       campaignId: 16031,
  //       campaignName: 'test_0605_01',
  //       campaignDescription: '',
  //       campaignPriority: null,
  //       startDate: '2024-06-05 14:20:32',
  //       endDate: '2024-08-31 23:59:59',
  //       recommendedWordsType: 11,
  //       recommendedWords: 'app_test0429',
  //       thumbUrl:
  //         'http://*************:80/scd_ceeattach/fileupload//cegFiles/202404/cd8d085492529f0d65fa8c68dc8867c5.png',
  //       url: 'http://*************:8588/download/buyPromoApp?subsId=1135596&treatmentInstCode=-21078&contactId=-852582',
  //       mccmWebSite: 'http://*************:80/cegH5/',
  //       mccmCmsURL: 'DynamicRender',
  //       creativeJson: null,
  //       creativeType: '1',
  //       creativeCode: '00100020543###1',
  //       mediaType: '1',
  //       videoUrl: null,
  //       buttonUrl: null,
  //       offerList: [
  //         {
  //           offerId: 100500001,
  //           offerName: '100GB CUL 299.00 per Month',
  //           offerType: '1',
  //           offerDescription: '',
  //           offerCode: null,
  //           offerUrl: null,
  //           price: 299000000,
  //           displayPrice: '299.00',
  //           salePrice: 269100000,
  //           displaySalePrice: '269',
  //           discountRate: '10',
  //           currencySymbol: 'P',
  //           otcPrice: 269100000,
  //           precision: 1000000,
  //           relatedOfferId: 100500001,
  //           ceeOfferNbr: '389623027176550404',
  //           rewardId: 202,
  //         },
  //         {
  //           offerId: 100500003,
  //           offerName: '500GB CUL 555.00 per Month',
  //           offerType: '1',
  //           offerDescription: '',
  //           offerCode: null,
  //           offerUrl: null,
  //           price: 555000000,
  //           displayPrice: '555.00',
  //           salePrice: 525000000,
  //           displaySalePrice: '525',
  //           discountRate: '5',
  //           currencySymbol: 'P',
  //           otcPrice: 525000000,
  //           precision: 1000000,
  //           relatedOfferId: 100500003,
  //           ceeOfferNbr: '390162459042385922',
  //           rewardId: 204,
  //         },
  //         {
  //           offerId: 100500004,
  //           offerName: 'Super Star-1 779.00 per Month',
  //           offerType: '1',
  //           offerDescription: '',
  //           offerCode: null,
  //           offerUrl: null,
  //           price: 779000000,
  //           displayPrice: '779.00',
  //           salePrice: 720000000,
  //           displaySalePrice: '720',
  //           discountRate: '7',
  //           currencySymbol: 'P',
  //           otcPrice: 720000000,
  //           precision: 1000000,
  //           relatedOfferId: 100500004,
  //           ceeOfferNbr: '390163368883392516',
  //           rewardId: 205,
  //         },
  //       ],
  //       offerGroupList: null,
  //       paymentMethodList: [],
  //       thirdPayMethodList: [],
  //       application: '-',
  //       adTitle: '',
  //       promotionId: null,
  //       batchId: '34215',
  //       campaignCode: 'DMC20240605016031',
  //     },
  //     {
  //       subsId: 1135596,
  //       accNbr: '9202106404',
  //       channelCode: 'APP',
  //       adviceCode: 'ECARE_APP_PROMO',
  //       contactId: '-852580',
  //       treatmentInstId: null,
  //       treatmentInstCode: '-21079',
  //       contactOrderTime: null,
  //       campaignId: 16031,
  //       campaignName: 'test_0605_01',
  //       campaignDescription: '',
  //       campaignPriority: null,
  //       startDate: '2024-06-05 14:20:32',
  //       endDate: '2024-08-31 23:59:59',
  //       recommendedWordsType: 11,
  //       recommendedWords: 'app_just for you',
  //       thumbUrl:
  //         'http://*************:80/scd_ceeattach/fileupload//cegFiles/202403/cf2c8cbf45b44555588370eba5d58ed6.png',
  //       url:
  //         'mydito://BuyPromo?tabIndex=0&categoryName=Just+For+You&offerGroupTab=PROMO_MCCM_PREPAID&subsId=1135596&treatmentInstCode=-21079&contactId=-852580',
  //       mccmWebSite: 'http://*************:80/cegH5/',
  //       mccmCmsURL: 'DynamicRender',
  //       creativeJson: null,
  //       creativeType: '1',
  //       creativeCode: '00100020465###1',
  //       mediaType: '1',
  //       videoUrl: null,
  //       buttonUrl: null,
  //       offerList: [
  //         {
  //           offerId: 100500001,
  //           offerName: '100GB CUL 299.00 per Month',
  //           offerType: '1',
  //           offerDescription: '',
  //           offerCode: null,
  //           offerUrl: null,
  //           price: 299000000,
  //           displayPrice: '299.00',
  //           salePrice: 269100000,
  //           displaySalePrice: '269',
  //           discountRate: '10',
  //           currencySymbol: 'P',
  //           otcPrice: 269100000,
  //           precision: 1000000,
  //           relatedOfferId: 100500001,
  //           ceeOfferNbr: '389623027176550404',
  //           rewardId: 202,
  //         },
  //         {
  //           offerId: 100500003,
  //           offerName: '500GB CUL 555.00 per Month',
  //           offerType: '1',
  //           offerDescription: '',
  //           offerCode: null,
  //           offerUrl: null,
  //           price: 555000000,
  //           displayPrice: '555.00',
  //           salePrice: 525000000,
  //           displaySalePrice: '525',
  //           discountRate: '5',
  //           currencySymbol: 'P',
  //           otcPrice: 525000000,
  //           precision: 1000000,
  //           relatedOfferId: 100500003,
  //           ceeOfferNbr: '390162459042385922',
  //           rewardId: 204,
  //         },
  //         {
  //           offerId: 100500004,
  //           offerName: 'Super Star-1 779.00 per Month',
  //           offerType: '1',
  //           offerDescription: '',
  //           offerCode: null,
  //           offerUrl: null,
  //           price: 779000000,
  //           displayPrice: '779.00',
  //           salePrice: 720000000,
  //           displaySalePrice: '720',
  //           discountRate: '7',
  //           currencySymbol: 'P',
  //           otcPrice: 720000000,
  //           precision: 1000000,
  //           relatedOfferId: 100500004,
  //           ceeOfferNbr: '390163368883392516',
  //           rewardId: 205,
  //         },
  //       ],
  //       offerGroupList: null,
  //       paymentMethodList: [],
  //       thirdPayMethodList: [],
  //       application: '-',
  //       adTitle: '',
  //       promotionId: null,
  //       batchId: '34216',
  //       campaignCode: 'DMC20240605016031',
  //     },
  //     {
  //       subsId: 1135596,
  //       accNbr: '9202106404',
  //       channelCode: 'APP',
  //       adviceCode: 'ECARE_APP_PROMO',
  //       contactId: '-852574',
  //       treatmentInstId: null,
  //       treatmentInstCode: '-21073',
  //       contactOrderTime: null,
  //       campaignId: 16035,
  //       campaignName: 'lsm_Whoopie_0607',
  //       campaignDescription: '',
  //       campaignPriority: null,
  //       startDate: '2024-06-06 10:50:21',
  //       endDate: '2025-05-07 10:50:21',
  //       recommendedWordsType: 11,
  //       recommendedWords: null,
  //       thumbUrl:
  //         'http://*************:80/scd_ceeattach/fileupload//cegFiles/202402/6bb969aa0311b6fb58a495af4d3d4a22.jpg',
  //       url: 'http://*************:8588/download/buyPromoApp?subsId=1135596&treatmentInstCode=-21073&contactId=-852574',
  //       mccmWebSite: 'http://*************:80/cegH5/',
  //       mccmCmsURL: 'DynamicRender',
  //       creativeJson: null,
  //       creativeType: '1',
  //       creativeCode: '00100020442###1',
  //       mediaType: '1',
  //       videoUrl: null,
  //       buttonUrl: null,
  //       offerList: [
  //         {
  //           offerId: null,
  //           offerName: 'FREE 3GB Data for DMC',
  //           offerType: '1',
  //           offerDescription: '<p>desc 3g desc</p>',
  //           offerCode: null,
  //           offerUrl: 'http://*************:9888//CONTROLLED/media/202211/20221115110446525.png',
  //           price: 10000000,
  //           displayPrice: '10.00',
  //           salePrice: 10000000,
  //           displaySalePrice: '10.00',
  //           discountRate: null,
  //           currencySymbol: 'P',
  //           otcPrice: 10000000,
  //           precision: 1000000,
  //           relatedOfferId: null,
  //           ceeOfferNbr: '363893539483984703',
  //           rewardId: 85,
  //         },
  //       ],
  //       offerGroupList: null,
  //       paymentMethodList: [],
  //       thirdPayMethodList: [],
  //       application: '-',
  //       adTitle: '',
  //       promotionId: null,
  //       batchId: '34206',
  //       campaignCode: 'DMC20240606016035',
  //     },
  //   ],
  // },
  // 'POST /ecare/webs/offer/validate': (req, res) => {
  //   res.send({ result: '0', orderOfferList: [], cancelOfferList: [], orderProdList: [], cancelProdList: [] });
  // },
  // 'POST /ecare/webs/offer/autopay/available': (req, res) => {
  //   const { pageInfo } = req.body;
  //   const { pageSize, currentPage } = pageInfo;
  //   const list = data.slice((currentPage - 1) * pageSize, currentPage * pageSize);
  //   setTimeout(() => {
  //     res.send({
  //       data: {
  //         promoList: list,
  //         pageInfo: {
  //           total: data.length,
  //           pageSize: pageInfo.pageSize,
  //           currentPage: pageInfo.currentPage,
  //         },
  //       },
  //     });
  //   }, 1000);
  // },
  // '/ecare/webs/transaction/onwayOrder': (req, res) => {
  //   res.send({
  //     orderId: 'null',
  //     transSn: 'null',
  //   });
  // },
  // 'POST /ecare/webs/offer/promo/detail': (req, res) => {
  //   res.send(offerDetailRes);
  // },
  // 'POST /ecare/webs/order/check-for-guest': {
  //   code: '200',
  //   resultCode: null,
  //   resultMsg: null,
  //   data: {
  //     result: 0,
  //     catgCode: 'ECARE_PROMO_POSTPAID1',
  //     transactionId: '618e9e50-c735-4754-a236-0500bbb921cf',
  //   },
  // },
  // '/ecare/webs/transaction/A': [
  //   {
  //     accNbr: '9202106404',
  //   },
  //   {
  //     accNbr: '9202106405',
  //   },
  //   {
  //     accNbr: '9202106406',
  //   },
  //   {
  //     accNbr: '9202106407',
  //   },
  //   {
  //     accNbr: '9202106408',
  //   },
  //   {
  //     accNbr: '9202106409',
  //   },
  // ],
  // 'POST /ecare/webs/roaming/country/list': {
  //   code: '200',
  //   resultCode: null,
  //   resultMsg: null,
  //   data: {
  //     countryList: [
  //       {
  //         code: 'AF',
  //         name: 'Afghanistan',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Afghanistan_1708307261725.png',
  //       },
  //       {
  //         code: 'AM',
  //         name: 'Armenia',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Armenia_1708308583402.png',
  //       },
  //       {
  //         code: 'AU',
  //         name: 'Australia',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Australia_1708310661692.png',
  //       },
  //       {
  //         code: 'AT',
  //         name: 'Austria',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Austria_1708310814324.png',
  //       },
  //       {
  //         code: 'BH',
  //         name: 'Bahrain',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Bahrain_1708310897490.png',
  //       },
  //       {
  //         code: 'BD',
  //         name: 'Bangladesh',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Bangladesh_1708325081151.png',
  //       },
  //       {
  //         code: 'BR',
  //         name: 'Brazil',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Brazil_1708325651164.png',
  //       },
  //       {
  //         code: 'BG',
  //         name: 'Bulgaria',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Bulgaria_1708325846401.png',
  //       },
  //       {
  //         code: 'KH',
  //         name: 'Cambodia',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Cambodia_1708325918903.png',
  //       },
  //       {
  //         code: 'CA',
  //         name: 'Canada',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/canada_1708325950067.png',
  //       },
  //       {
  //         code: 'CN',
  //         name: 'China',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/china_1708326971778.png',
  //       },
  //       {
  //         code: 'HR',
  //         name: 'Croatia',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Croatia_1708327047849.png',
  //       },
  //       {
  //         code: 'CY',
  //         name: 'Cyprus',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Cyprus_1708327075579.png',
  //       },
  //       {
  //         code: 'CZ',
  //         name: 'Czech Republic',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Czech Republic_1708327240095.png',
  //       },
  //       {
  //         code: 'DK',
  //         name: 'Denmark',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Denmark_1708327350449.png',
  //       },
  //       {
  //         code: 'EG',
  //         name: 'Egypt',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Egypt_1708327545027.png',
  //       },
  //       {
  //         code: 'EE',
  //         name: 'Estonia',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Estonia_1708327724045.png',
  //       },
  //       {
  //         code: 'FI',
  //         name: 'Finland',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Finland_1708327782114.png',
  //       },
  //       {
  //         code: 'FR',
  //         name: 'France',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/France_1708327832899.png',
  //       },
  //       {
  //         code: 'DE',
  //         name: 'Germany',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Germany_1708327895174.png',
  //       },
  //       {
  //         code: 'GR',
  //         name: 'Greece',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Greece_1708395031181.png',
  //       },
  //       {
  //         code: 'HK',
  //         name: 'Hong Kong',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Hong Kong_1708395076672.png',
  //       },
  //       {
  //         code: 'HU',
  //         name: 'Hungary',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Hungary_1708395117134.png',
  //       },
  //       {
  //         code: 'ID',
  //         name: 'Indonesia',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Indonesia_1708395156705.png',
  //       },
  //       {
  //         code: 'IQ',
  //         name: 'Iraq',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Iraq_1708395207149.png',
  //       },
  //       {
  //         code: 'IE',
  //         name: 'Ireland',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Ireland_1708397292271.png',
  //       },
  //       {
  //         code: 'IL',
  //         name: 'Israel',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Israel_1708395310273.png',
  //       },
  //       {
  //         code: 'IT',
  //         name: 'Italy',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Italy_1708395377683.png',
  //       },
  //       {
  //         code: 'JP',
  //         name: 'Japan',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Japan_1708395434788.png',
  //       },
  //       {
  //         code: 'JO',
  //         name: 'Jordan',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Jordan_1708395489810.png',
  //       },
  //       {
  //         code: 'KZ',
  //         name: 'Kazakhstan',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Kazakhstan_1708395555229.png',
  //       },
  //       {
  //         code: 'KR',
  //         name: 'Korea (South)',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Korea_1708395593237.png',
  //       },
  //       {
  //         code: 'KW',
  //         name: 'Kuwait',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Kuwait_1708395633651.png',
  //       },
  //       {
  //         code: 'LA',
  //         name: 'Laos',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Laos_1708395680708.png',
  //       },
  //       {
  //         code: 'LV',
  //         name: 'Latvia',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Latvia_1708395735998.png',
  //       },
  //       {
  //         code: 'LI',
  //         name: 'Liechtenstein',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Liechtenstein_1708395779498.png',
  //       },
  //       {
  //         code: 'LT',
  //         name: 'Lithuania',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Lithuania_1708395821396.png',
  //       },
  //       {
  //         code: 'LU',
  //         name: 'Luxembourg',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Luxembourg_1708395858935.png',
  //       },
  //       {
  //         code: 'MK',
  //         name: 'Macedonia',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Macedonia_1708395893543.png',
  //       },
  //       {
  //         code: 'MY',
  //         name: 'Malaysia',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Malaysia_1708395930534.png',
  //       },
  //       {
  //         code: 'MT',
  //         name: 'Malta',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Malta_1708395980562.png',
  //       },
  //       {
  //         code: 'MD',
  //         name: 'Moldova',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Moldova1_1708404042176.png',
  //       },
  //       {
  //         code: 'MN',
  //         name: 'Mongolia',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Mongolia_1708396086389.png',
  //       },
  //       {
  //         code: 'ME',
  //         name: 'Montenegro',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Montenegro_1708396329626.png',
  //       },
  //       {
  //         code: 'MM',
  //         name: 'Myanmar',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Myanmar1_1708404023725.png',
  //       },
  //       {
  //         code: 'NL',
  //         name: 'Netherlands',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Netherlands_1708396457884.png',
  //       },
  //       {
  //         code: 'NO',
  //         name: 'Norway',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Norway_1708396491080.png',
  //       },
  //       {
  //         code: 'PK',
  //         name: 'Pakistan',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Pakistan_1708396541815.png',
  //       },
  //       {
  //         code: 'PL',
  //         name: 'Poland',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Poland_1708396597575.png',
  //       },
  //       {
  //         code: 'PT',
  //         name: 'Portugal',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Portugal_1708396657707.png',
  //       },
  //       {
  //         code: 'QA',
  //         name: 'Qatar',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Qatar_1708396697720.png',
  //       },
  //       {
  //         code: 'RE',
  //         name: 'Reuinion',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Reuinion_1708398696227.png',
  //       },
  //       {
  //         code: 'RO',
  //         name: 'Romania',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Romania_1708398888873.jpg',
  //       },
  //       {
  //         code: 'SA',
  //         name: 'Saudi Arabia',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Saudi Arabia_1708399013131.png',
  //       },
  //       {
  //         code: 'RS',
  //         name: 'Serbia',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Serbia_1708399625309.jpg',
  //       },
  //       {
  //         code: 'SG',
  //         name: 'Singapore',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Singapore_1708399697938.jpg',
  //       },
  //       {
  //         code: 'SK',
  //         name: 'Slovakia',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Slovakia_1708399893863.png',
  //       },
  //       {
  //         code: 'SI',
  //         name: 'Slovenia',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Slovenia_1708399941487.jpg',
  //       },
  //       {
  //         code: 'ES',
  //         name: 'Spain',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/spain_1708404163811.png',
  //       },
  //       {
  //         code: 'SE',
  //         name: 'Sweden',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Sweden_1708400143129.png',
  //       },
  //       {
  //         code: 'CH',
  //         name: 'Switzerland',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Switzerland_1708400191001.png',
  //       },
  //       {
  //         code: 'TW',
  //         name: 'Taiwan',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Taiwan_1708402853154.png',
  //       },
  //       {
  //         code: 'TJ',
  //         name: 'Tajikistan',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Tajikistan_1708402919795.jpg',
  //       },
  //       {
  //         code: 'TH',
  //         name: 'Thailand',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Thailand_1708402957653.png',
  //       },
  //       {
  //         code: 'TR',
  //         name: 'Turkey',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Turkey_1708403000591.png',
  //       },
  //       {
  //         code: 'AE',
  //         name: 'United Arab Emirates (UAE)',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/United Arab Emirates (UAE)_1708403055506.png',
  //       },
  //       {
  //         code: 'GB',
  //         name: 'United Kingdom (UK)',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/United Kingdom (UK)_1708403091509.jpg',
  //       },
  //       {
  //         code: 'US',
  //         name: 'United States of America (USA)',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/United States of America (USA)_1708403140033.png',
  //       },
  //       {
  //         code: 'VU',
  //         name: 'Vanuatu',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Vanuatu_1708403188617.jpg',
  //       },
  //       {
  //         code: 'VN',
  //         name: 'Vietnam',
  //         icon: 'https://test-digital.dito.ph:8088/download/file/Vietnam_1708403229608.png',
  //       },
  //     ],
  //   },
  // },
};
