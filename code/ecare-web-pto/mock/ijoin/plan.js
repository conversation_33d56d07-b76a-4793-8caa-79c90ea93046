export default {
  'POST /ecare/webs/fwa/ijoin/installacion-check': {
    code: '200',
    resultCode: null,
    resultMsg: null,
    data: { result: 0 },
  },
  // 'POST /ecare/webs/ijoin/verify-invite-code': {
  //   code: '200',
  //   resultCode: null,
  //   resultMsg: null,
  //   data: { result: 1 },
  // },
  // 'POST /ecare/webs/ijoin/sim-card/qry': {
  //   code: '200',
  //   resultCode: null,
  //   resultMsg: null,
  //   data: {
  //     iccid: '1001200102000007214',
  //   },
  // },
  // 'POST /ecare/webs/ijoin/draft-order/send-otp': {
  //   code: '200',
  //   resultCode: null,
  //   resultMsg: null,
  //   data: { result: 1 },
  // },
  // 'POST /ecare/webs/ijoin/draft-order/qry-for-login-user': {
  //   code: '200',
  //   resultCode: null,
  //   resultMsg: null,
  //   // data: {
  //   //   referenceId: 'fac89b42-19b0-433d-b8fe-aaf360de0375',
  //   //   businessScene: '2',
  //   //   custInfo: {
  //   //     custId: null,
  //   //     identity: '1',
  //   //     salutation: '1',
  //   //     certType: '1',
  //   //     certNbr: 'xjbbf',
  //   //     custName: null,
  //   //     firstName: 'fjhc',
  //   //     middleName: 'ncnc',
  //   //     lastName: 'fhhc',
  //   //     gender: '1',
  //   //     birthday: '19920710',
  //   //     stdAddrId: null,
  //   //     addrPath: null,
  //   //     displayAddrPath: null,
  //   //     address: null,
  //   //     postCode: null,
  //   //     citizenship: '1',
  //   //     maritalStatus: null,
  //   //     accNbr: null,
  //   //     custSubType: null,
  //   //     tin: null,
  //   //     motherName: null,
  //   //     contactInfo: {
  //   //       primaryContactNbr: '9202106422',
  //   //       alternateContactNbr: null,
  //   //       primaryEmail: '<EMAIL>',
  //   //       primaryContactNbrVerified: null,
  //   //       alternateContactNbrVerified: null,
  //   //       primaryEmailVerified: null,
  //   //     },
  //   //     employmentInfo: null,
  //   //     custAttachList: null,
  //   //   },
  //   //   offerList: [
  //   //     {
  //   //       relatedOfferId: null,
  //   //       relatedOfferCode: null,
  //   //       paidFlag: null,
  //   //       listPrice: null,
  //   //       displayListPrice: null,
  //   //       salesPrice: null,
  //   //       displaySalesPrice: null,
  //   //       offerId: null,
  //   //       offerNbr: '440039493190275074',
  //   //       offerType: null,
  //   //       offerName: null,
  //   //       offerCode: null,
  //   //       thumbImageUrl: null,
  //   //       imageUrl: null,
  //   //       brief: null,
  //   //       description: null,
  //   //       offerAttrList: null,
  //   //       tagList: null,
  //   //       state: null,
  //   //       otcPrice: null,
  //   //       displayOtcPrice: null,
  //   //       rentPrice: null,
  //   //       displayRentPrice: null,
  //   //       guidingRentPrice: null,
  //   //       displayGuidingRentPrice: null,
  //   //       discountTimes: null,
  //   //       rentDuration: null,
  //   //       rentUnitName: null,
  //   //       rentUnitType: null,
  //   //       precision: null,
  //   //       currencySymbol: null,
  //   //       offerGrade: null,
  //   //       salesVolume: null,
  //   //       duplicateFlag: null,
  //   //     },
  //   //   ],
  //   //   crmOfferIdList: null,
  //   //   goodsList: null,
  //   //   deliveryInfo: {
  //   //     stdAddrId: '106610',
  //   //     houseNo: 'djhf',
  //   //     street: 'dnnfjj&',
  //   //     postCode: '4664',
  //   //     landmark: 'dhhfhc',
  //   //     provinceName: 'AGUSAN DEL NORTE',
  //   //     appointmentDate: null,
  //   //     appointmentTime: null,
  //   //     deliveryMethod: null,
  //   //     provinceStdAddrId: null,
  //   //     cityStdAddrId: null,
  //   //     barangayStdAddrId: null,
  //   //   },
  //   //   installationInfo: null,
  //   //   accNbr: null,
  //   //   seller: null,
  //   //   salesPartner: null,
  //   //   orderSource: 'MobilePostIjoin',
  //   //   salesSource: null,
  //   //   simOnlyFlag: null,
  //   //   currentAccNbr: null,
  //   //   codeList: null,
  //   //   channelCode: null,
  //   //   couponCode: 'DITOPROMO',
  //   //   inviteCode: null,
  //   //   draftOrderId: 10010,
  //   //   draftOrderType: '1',
  //   //   custId: 262124,
  //   // },
  // },
  // 'POST /ecare/webs/ijoin/draft-order/qry-for-continue-apply': {
  //   code: '200',
  //   resultCode: null,
  //   resultMsg: null,
  //   data: {
  //     referenceId: '10de37ea-122a-42f4-abcf-df949e57a1d7',
  //     businessScene: '2',
  //     custInfo: {
  //       custId: null,
  //       identity: '1',
  //       salutation: '1',
  //       certType: '1',
  //       certNbr: 'xjbbf',
  //       custName: null,
  //       firstName: 'fjhc',
  //       middleName: 'ncnc',
  //       lastName: 'fhhc',
  //       gender: '1',
  //       birthday: '19920710',
  //       stdAddrId: null,
  //       addrPath: null,
  //       displayAddrPath: null,
  //       address: null,
  //       postCode: null,
  //       citizenship: '1',
  //       maritalStatus: null,
  //       accNbr: null,
  //       custSubType: null,
  //       tin: null,
  //       motherName: null,
  //       contactInfo: {
  //         primaryContactNbr: '9202106422',
  //         alternateContactNbr: null,
  //         primaryEmail: '<EMAIL>',
  //         primaryContactNbrVerified: null,
  //         alternateContactNbrVerified: null,
  //         primaryEmailVerified: null,
  //       },
  //       employmentInfo: null,
  //       custAttachList: null,
  //     },
  //     offerList: [
  //       {
  //         relatedOfferId: null,
  //         relatedOfferCode: null,
  //         paidFlag: null,
  //         listPrice: null,
  //         displayListPrice: null,
  //         salesPrice: null,
  //         displaySalesPrice: null,
  //         offerId: null,
  //         offerNbr: '440039493190275074',
  //         offerType: null,
  //         offerName: null,
  //         offerCode: null,
  //         thumbImageUrl: null,
  //         imageUrl: null,
  //         brief: null,
  //         description: null,
  //         offerAttrList: null,
  //         tagList: null,
  //         state: null,
  //         otcPrice: null,
  //         displayOtcPrice: null,
  //         rentPrice: null,
  //         displayRentPrice: null,
  //         guidingRentPrice: null,
  //         displayGuidingRentPrice: null,
  //         discountTimes: null,
  //         rentDuration: null,
  //         rentUnitName: null,
  //         rentUnitType: null,
  //         precision: null,
  //         currencySymbol: null,
  //         offerGrade: null,
  //         salesVolume: null,
  //         duplicateFlag: null,
  //       },
  //     ],
  //     crmOfferIdList: null,
  //     goodsList: null,
  //     deliveryInfo: {
  //       stdAddrId: '106610',
  //       houseNo: 'djhf',
  //       street: 'dnnfjj&',
  //       postCode: '4664',
  //       landmark: 'dhhfhc',
  //       provinceName: 'AGUSAN DEL NORTE',
  //       appointmentDate: null,
  //       appointmentTime: null,
  //       deliveryMethod: null,
  //       provinceStdAddrId: null,
  //       cityStdAddrId: null,
  //       barangayStdAddrId: null,
  //     },
  //     installationInfo: null,
  //     accNbr: null,
  //     seller: null,
  //     salesPartner: null,
  //     orderSource: 'MobilePostIjoin',
  //     salesSource: null,
  //     simOnlyFlag: null,
  //     currentAccNbr: null,
  //     codeList: null,
  //     channelCode: null,
  //     couponCode: 'DITOPROMO',
  //     inviteCode: null,
  //     draftOrderId: null,
  //     draftOrderType: '1',
  //     custId: 262124,
  //   },
  // },
  // 'POST /ecare/webs/ijoin/draft-order/qry-for-contact': {
  //   code: '200',
  //   resultCode: null,
  //   resultMsg: null,
  //   data: {
  //     referenceId: '10de37ea-122a-42f4-abcf-df949e57a1d7',
  //     businessScene: '2',
  //     custInfo: {
  //       custId: null,
  //       identity: '1',
  //       salutation: '1',
  //       certType: '1',
  //       certNbr: 'xjbbf',
  //       custName: null,
  //       firstName: 'fjhc',
  //       middleName: 'ncnc',
  //       lastName: 'fhhc',
  //       gender: '1',
  //       birthday: '19920710',
  //       stdAddrId: null,
  //       addrPath: null,
  //       displayAddrPath: null,
  //       address: null,
  //       postCode: null,
  //       citizenship: '1',
  //       maritalStatus: null,
  //       accNbr: null,
  //       custSubType: null,
  //       tin: null,
  //       motherName: null,
  //       contactInfo: {
  //         primaryContactNbr: '9202106422',
  //         alternateContactNbr: null,
  //         primaryEmail: '<EMAIL>',
  //         primaryContactNbrVerified: null,
  //         alternateContactNbrVerified: null,
  //         primaryEmailVerified: null,
  //       },
  //       employmentInfo: null,
  //       custAttachList: null,
  //     },
  //     offerList: [
  //       {
  //         relatedOfferId: null,
  //         relatedOfferCode: null,
  //         paidFlag: null,
  //         listPrice: null,
  //         displayListPrice: null,
  //         salesPrice: null,
  //         displaySalesPrice: null,
  //         offerId: null,
  //         offerNbr: '440039493190275074',
  //         offerType: '4',
  //         offerName: null,
  //         offerCode: null,
  //         thumbImageUrl: null,
  //         imageUrl: null,
  //         brief: null,
  //         description: null,
  //         offerAttrList: null,
  //         tagList: null,
  //         state: null,
  //         otcPrice: null,
  //         displayOtcPrice: null,
  //         rentPrice: null,
  //         displayRentPrice: null,
  //         guidingRentPrice: null,
  //         displayGuidingRentPrice: null,
  //         discountTimes: null,
  //         rentDuration: null,
  //         rentUnitName: null,
  //         rentUnitType: null,
  //         precision: null,
  //         currencySymbol: null,
  //         offerGrade: null,
  //         salesVolume: null,
  //         duplicateFlag: null,
  //       },
  //     ],
  //     crmOfferIdList: null,
  //     goodsList: null,
  //     deliveryInfo: {
  //       stdAddrId: '106610',
  //       houseNo: 'djhf',
  //       street: 'dnnfjj&',
  //       postCode: '4664',
  //       landmark: 'dhhfhc',
  //       provinceName: 'AGUSAN DEL NORTE',
  //       appointmentDate: null,
  //       appointmentTime: null,
  //       deliveryMethod: null,
  //       provinceStdAddrId: null,
  //       cityStdAddrId: null,
  //       barangayStdAddrId: null,
  //     },
  //     installationInfo: null,
  //     accNbr: null,
  //     seller: null,
  //     salesPartner: null,
  //     orderSource: 'MobilePostIjoin',
  //     salesSource: null,
  //     simOnlyFlag: null,
  //     currentAccNbr: null,
  //     codeList: null,
  //     channelCode: null,
  //     couponCode: 'DITOPROMO',
  //     inviteCode: null,
  //     draftOrderId: 11111,
  //     draftOrderType: '1',
  //     custId: 262124,
  //   },
  // },
  // 'POST /ecare/webs/ijoin/login-cust-info': {
  //   code: '200',
  //   resultCode: null,
  //   resultMsg: null,
  //   data: {
  //     custId: 264071,
  //     identity: '1',
  //     salutation: '1',
  //     certType: '1',
  //     certNbr: '934890582',
  //     custName: 'ki to',
  //     firstName: 'ki',
  //     middleName: null,
  //     lastName: 'to',
  //     gender: '1',
  //     birthday: '20070101',
  //     stdAddrId: 120355,
  //     addrPath: '120353,120354,120355',
  //     displayAddrPath: 'CEBU,ALCANTARA,Cabadiangan',
  //     address: 'gjnn,gjjn,thjn,Cabadiangan,ALCANTARA,CEBU,REGION VII (CENTRAL VISAYAS),VIS,Philippines',
  //     postCode: '5669',
  //     citizenship: null,
  //     maritalStatus: null,
  //     accNbr: null,
  //     custSubType: null,
  //     tin: null,
  //     motherName: null,
  //     contactInfo: {
  //       primaryContactNbr: '9202106404',
  //       alternateContactNbr: null,
  //       primaryEmail: '<EMAIL>',
  //       primaryContactNbrVerified: null,
  //       alternateContactNbrVerified: null,
  //       primaryEmailVerified: null,
  //     },
  //     employmentInfo: {
  //       employerName: null,
  //       occupation: '12000000',
  //       employerAddr: null,
  //       employerMobile: '9202106404',
  //       jobTitle: 'tth',
  //       position: '1',
  //       provinceStdAddrId: '120353',
  //       cityStdAddrId: '120354',
  //       barangayStdAddrId: '120355',
  //       employerStdAddrId: '120355',
  //       monthlyIncoming: null,
  //     },
  //     custAttachList: null,
  //   },
  // },
  // 'POST /ecare/webs/ijoin/validate-coupon': {
  //   code: '200',
  //   data: {
  //     result: 0,
  //     couponInfo: {
  //       couponCode: 'DITOPROMO',
  //       couponName: 'coupon name',
  //       detail: '<p>coupon detailcoupon detailcoupon detailcoupon detailcoupon detailcoupon detailcoupon detailcoupon detailcoupon detailcoupon detailcoupon detailcoupon detailcoupon detailcoupon detailcoupon detailcoupon detailcoupon detailcoupon detail</p>',
  //       couponType: '',
  //       discountType: '',
  //       tc: '',
  //       label: '',
  //     },
  //   },
  // },
  // 'POST /ecare/webs/ijoin/cust/info': {
  //   code: '200',
  //   resultCode: null,
  //   resultMsg: null,
  //   data: {
  //     custList: [
  //       {
  //         custId: 262058,
  //         identity: '1',
  //         salutation: '1',
  //         certType: '1561',
  //         certNbr: '999999999',
  //         custName: 'li shiman',
  //         firstName: 'li',
  //         middleName: null,
  //         lastName: 'shiman',
  //         gender: '1',
  //         birthday: '20050720000000',
  //         stdAddrId: 136547,
  //         addrPath: '136412,136484,136547',
  //         displayAddrPath: 'NCR, FOURTH DISTRICT (Not a Province),CITY OF MAKATI,San Antonio',
  //         address:
  //           'wnstreet1,wnbiuld1,6-1,sdcae,San Antonio,CITY OF MAKATI,NCR, FOURTH DISTRICT (Not a Province),NATIONAL CAPITAL REGION (NCR),NCR,Philippines',
  //         postCode: '1111',
  //         citizenship: null,
  //         maritalStatus: null,
  //         contactInfo: {
  //           primaryContactNbr: '9999999999',
  //           alternateContactNbr: '1111111111',
  //           primaryEmail: '<EMAIL>',
  //         },
  //         employmentInfo: {
  //           employerName: 'hao jing',
  //           occupation: '14000000',
  //           employerAddr: 'zheng fang zhong lu',
  //           employerMobile: '9999999999',
  //           jobTitle: 'Job title',
  //           position: '2',
  //           provinceStdAddrId: '136412',
  //           cityStdAddrId: '136484',
  //           barangayStdAddrId: '136547',
  //           employerStdAddrId: '136547',
  //           monthlyIncoming: null,
  //         },
  //         custAttachList: null,
  //       },
  //     ],
  //   },
  // },
  // 'POST /ecare/webs/ijoin/qryOrderDetail': {
  //   code: '200',
  //   resultCode: null,
  //   resultMsg: null,
  //   data: {
  //     orderDetail: {
  //       custOrderId: 82407081700762,
  //       custOrderNbr: '82407081700762',
  //       custId: 257965,
  //       custNbr: null,
  //       eventTypeId: null,
  //       eventTypeName: null,
  //       state: '101704',
  //       stateName: 'Waiting for Get Recommended',
  //       totalCharge: null,
  //       displayTotalCharge: null,
  //       stateDate: '20240708143513',
  //       acceptDate: '20240708143122',
  //       paymentDate: null,
  //       channelId: null,
  //       channelName: null,
  //       partyType: null,
  //       partyCode: '700240',
  //       partyName: null,
  //       orderAuditFlag: true,
  //       dispatchedDate: null,
  //       receiveDate: null,
  //       trackingNumber: null,
  //       trackingCompany: null,
  //       undoReason: null,
  //       undoReasonName: null,
  //       businessScene: '2',
  //       resReceiveInfo: {
  //         receiveStdAddrId: '110878',
  //         receiveId: '42719',
  //         receiveMethod: 'DELIVERY',
  //         receiveAddrDetail: 'teyrtew teyrtew',
  //         receiveName: 'fjfjfjjf fjfjfn',
  //         receiveMobileNo: '9906172661',
  //         receiveFullAddr: 'teyrtew teyrtew,  Bagong Nayon, BALIUAG, BULACAN, REGION III (CENTRAL LUZON), 2546 teyrtew',
  //       },
  //       orderFeeInfo: {
  //         custOrderId: null,
  //         totalCharge: '8418000000',
  //         displayTotalCharge: '8,418.00',
  //         currencySymbol: '₱',
  //         orderFeeSumList: [
  //           {
  //             charge: '3368000000',
  //             currencySymbol: '₱',
  //             displayCharge: '3,368.00',
  //             tax: '0',
  //             chargeWithTax: null,
  //             displayTax: '0.00',
  //             currencyCode: null,
  //             displayChargeWithTax: null,
  //           },
  //         ],
  //         benefitInfoList: null,
  //         orderFeeDetailList: null,
  //         depositInfoList: null,
  //       },
  //       orderItemList: [
  //         {
  //           orderItemId: '12407086844508',
  //           orderItemNbr: null,
  //           custOrderId: '82407081700762',
  //           custId: '257965',
  //           acctId: null,
  //           subsId: null,
  //           prefix: null,
  //           accNbr: '8981562473',
  //           state: '101704',
  //           stateDate: '20240708143514',
  //           acceptDate: '20240708143122',
  //           stateName: 'Waiting for Get Recommended',
  //           reason: null,
  //           subsEventId: null,
  //           subsEventName: null,
  //           mainOfferId: '*********',
  //           mainOfferName: null,
  //           mainOfferBrief: null,
  //           mainOfferCode: null,
  //           mainOfferType: '2',
  //           mainOfferThumbImageUrl: null,
  //           newCustId: null,
  //           newAcctId: null,
  //           primaryFlag: null,
  //           primaryAccNbr: null,
  //           paymentAcctId: null,
  //           paymentAcctNbr: null,
  //           acctNbr: null,
  //           newMainOfferId: null,
  //           newMainOfferCode: null,
  //           newMainOfferName: null,
  //           newMainOfferBrief: null,
  //           newMainOfferThumbImageUrl: null,
  //           createdDate: '20240708143122',
  //           closedDate: null,
  //           partyCode: null,
  //           partyName: null,
  //           partyType: null,
  //           remark: null,
  //           offerInstList: [
  //             {
  //               offerNbr: '554134783963959298',
  //               offerCode: 'Galaxy A15 5G_2087',
  //               offerId: 2087,
  //               offerInstId: null,
  //               offerName: 'Galaxy A15 5G',
  //               offerType: '5',
  //               brief: '',
  //               thumbImageUrl:
  //                 'https://co51t-digital.dito.ph:9093/scd_ceeattach/CONTROLLED/media/202403/08/20240308105430340png.png',
  //               createDate: null,
  //               state: null,
  //               stateDate: null,
  //               stateName: null,
  //               effDate: '20240308105332',
  //               expDate: null,
  //               offerCategory: null,
  //               remarks: null,
  //               actType: null,
  //               effType: null,
  //               rentPrice: null,
  //               salesPrice: 2480000000,
  //               displaySalesPrice: '2,480.00',
  //               displayRentPrice: '0.00',
  //               currencySymbol: '₱',
  //             },
  //             {
  //               offerNbr: '414245594794102834',
  //               offerCode: '1000000556',
  //               offerId: 1800,
  //               offerInstId: null,
  //               offerName: 'FLEXPlan 888',
  //               offerType: '4',
  //               brief:
  //                 '<p>1st month Payment<br>25GB High Speed Data<br>25GB Bonus 5G Data<br>Unlimited Mobile Calls to All Networks<br>Unlimited Local Texts to All Networks<br>Bonus Prime Video for 12 months</p>',
  //               thumbImageUrl:
  //                 'https://co51t-digital.dito.ph:9093/scd_ceeattach/CONTROLLED/media/202305/24/20230524144041959.png',
  //               createDate: null,
  //               state: null,
  //               stateDate: null,
  //               stateName: null,
  //               effDate: '20230216102402',
  //               expDate: null,
  //               offerCategory: null,
  //               remarks: null,
  //               actType: null,
  //               effType: null,
  //               rentPrice: 888000000,
  //               salesPrice: 888000000,
  //               displaySalesPrice: '888.00',
  //               displayRentPrice: '888.00',
  //               currencySymbol: '₱',
  //             },
  //           ],
  //           prodInstList: null,
  //           resInstList: [
  //             {
  //               resInstId: null,
  //               resInstNbr: null,
  //               resType: '3',
  //               resId: null,
  //               resCode: null,
  //               resName: 'Galaxy A15 5G',
  //               offerCode: 'Galaxy A15 5G_2087',
  //               brief: '',
  //               thumbImageUrl:
  //                 'https://co51t-digital.dito.ph:9093/scd_ceeattach/CONTROLLED/media/202403/08/20240308105430340png.png',
  //               qty: null,
  //               actType: null,
  //               salesPrice: 5350000000,
  //               currencySymbol: '₱',
  //               relatedOfferId: 700001285,
  //               offerNbr: '554134783963959298',
  //               displaySalesPrice: '2,480.00',
  //             },
  //           ],
  //           undoReason: 'DEFAULT',
  //           undoReasonName:
  //             'Unfortunately, some required document/s and/or information to complete your application were not complied.',
  //           accNbrFee: 0,
  //           displayAccNbrFee: '0',
  //           currencySymbol: '₱',
  //         },
  //       ],
  //       eorFlag: 'N',
  //       preferredCallTime: null,
  //       paidFlag: '1',
  //       auditReasonList: [],
  //       recommendOfferList: [
  //         {
  //           offerNbr: '414245393689808944',
  //           offerId: 1799,
  //           offerCode: '1000000555',
  //           offerName: 'FLEXPlan 588',
  //           brief:
  //             '<p>1st month Payment<br>15GB High Speed Data<br>15GB Bonus 5G Data<br>Unlimited Mobile Calls to All Networks<br>Unlimited Local Texts to All Networks<br>Bonus Prime Video for 12 months</p>',
  //           thumbImageUrl:
  //             'https://co51t-digital.dito.ph:9093/scd_ceeattach/CONTROLLED/media/202305/24/20230524144108236.png',
  //           createDate: '20230216102318',
  //           state: null,
  //           stateName: null,
  //           stateDate: null,
  //           effDate: '20230216102312',
  //           expDate: null,
  //           offerType: '4',
  //           offerCategory: null,
  //           remarks: null,
  //           actType: null,
  //           effType: null,
  //           rentPrice: 588000000,
  //           salePrice: 588000000,
  //           displaySalesPrice: '588.00',
  //           displayRentPrice: '588.00',
  //           currencySymbol: '₱',
  //         },
  //       ],
  //       needSelectHandset: 'N',
  //     },
  //   },
  // },
  // 'POST /ecare/webs/ijoin/credit-score': {
  //   code: '200',
  //   resultCode: null,
  //   resultMsg: null,
  //   data: {
  //     custOrderId: null,
  //     transactionId: null,
  //     orderAuditFlag: false,
  //     orderItemList: null,
  //     feeInfo: null,
  //     transactionTime: null,
  //     preApprovalResult: null,
  //     autoApprovalResult: null,
  //     result: 3,
  //     creditInfo: {
  //       openAmount: 500000000,
  //       displayOpenAmount: '500.00',
  //       currencySymbol: 'P',
  //     },
  //   },
  // },
};
