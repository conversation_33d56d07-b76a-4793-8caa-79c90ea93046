// const oriCode = '40901547';

export default {
  // 'POST /ecare/cust/certTypeList': {
  //   code: '200',
  //   resultCode: null,
  //   resultMsg: null,
  //   data: {
  //     certTypeList: [
  //       {
  //         certTypeId: 2,
  //         certTypeCode: 'MYKAD',
  //         certTypeName: 'MyKadMyKadMyKadMyKadMyKadMyKadMyKadMyKadMyKadMyKadMyKad',
  //         custType: null,
  //         minLength: null,
  //         maxLength: null,
  //         regExp: '^[0-9]{12}[*]{0,1}$',
  //         externalCertType: '00600000001',
  //         tag: 'INSTANT APPROVAL',
  //       },
  //       {
  //         certTypeId: 1,
  //         certTypeCode: 'PASSPORT',
  //         certTypeName: 'Passport',
  //         custType: null,
  //         minLength: null,
  //         maxLength: null,
  //         regExp: '^[A-Za-z0-9]{5,21}$',
  //         externalCertType: '00000001003',
  //       },
  //       {
  //         certTypeId: 3,
  //         certTypeCode: 'MYTENTERA',
  //         certTypeName: 'MyTentera',
  //         custType: null,
  //         minLength: null,
  //         maxLength: null,
  //         regExp: '^[0-9]{12}[*]{0,1}$',
  //         externalCertType: '',
  //       },
  //     ],
  //   },
  // },
  // 'POST /ecare/webs/simreg/ocr/identity': (_, res) => {
  //   res.send({
  //     // oriCode: '40901548',
  //     code: '200',
  //     resultCode: null,
  //     resultMsg: null,
  //     data: {
  //       firstName: 'li',
  //       middleName: '',
  //       lastName: 'shiman',
  //       address: '-PUROK 2.NEW KATIPUNAN.MARAGUSAN (SAN MARIANO)COMPOSTELA VALLEY.REGION 1',
  //       gender: '1',
  //       birthDay: '20050720',
  //       idNumber: 'L07-09-001226',
  //       nationality: null,
  //       nationalityName: null,
  //       goodQuantityFlag: 'Y',
  //       image: Date.now(),
  //       referenceId: 'aea9d535-0a48-4cd0-96af-44cc0b65c4e6',
  //     },
  //   });
  // },
  // 'POST /ecare/webs/simreg/ocr/faceDetection': {
  //   // oriCode,
  //   code: '200',
  //   data: {
  //     result: 1,
  //   },
  // },
  // 'POST /ecare/webs/simreg/ocr/comparison': {
  //   // oriCode,
  //   code: '200',
  //   data: {
  //     result: 1,
  //   },
  // },
  // 'POST /ecare/webs/simreg/uploadPhoto': {
  //   code: '200',
  //   data: {
  //     result: 1,
  //   },
  // },
  // 'POST /ecare/webs/simreg/sendOtp': {
  //   code: '200',
  //   data: {
  //     otpTimeout: 120,
  //     otpRefreshTimeout: 10,
  //   },
  // },
  // 'POST /ecare/webs/simreg/verifyOtp': {
  //   code: '200',
  //   data: {
  //     referenceId: 'test-referenceId',
  //     result: 1,
  //     // isDoubleWrite: 'Y',
  //   },
  // },
  // 'POST /ecare/webs/simreg/check-captcha': {
  //   code: '200',
  //   data: {
  //     referenceId: 'test-referenceId',
  //     result: 1,
  //   },
  // },
  // 'POST /ecare/webs/simreg/travel/active': {
  //   code: '200',
  //   data: {
  //     result: 1,
  //   },
  // },
  // 'POST /ecare/webs/simreg/contact-check': {
  //   code: '200',
  //   data: {
  //     result: 1,
  //   },
  // },
  // 'POST /ecare/pto/fifthGeneration/fwa-network-check': {
  //   code: '200',
  //   data: {
  //     result: 1,
  //   },
  // },
  // 'POST /ecare/webs/simreg/qry-sim-reg-result': {
  //   code: '200',
  //   data: {
  //     simRegDetail: {
  //       accNbr: '',
  //       state: 'E',
  //       createdDate: '202411111111',
  //       simRegNo: '',
  //     },
  //   },
  // },
};
