FROM hub-nj.iwhalecloud.com/zcm9/zcm-openresty:1.15.8.1_20190806
LABEL maintainer="CLP team"

COPY ./dist/ /usr/local/openresty/nginx/html/pto
COPY ./src/assets/apple-app-site-association /usr/local/openresty/nginx/html/pto
COPY ./nginx.conf /usr/local/openresty/nginx/conf/
WORKDIR /root/
VOLUME /root/logs
EXPOSE 80
ENTRYPOINT ["/usr/local/bin/tini", "-g", "--", "docker-entrypoint.sh"]
<PERSON><PERSON> ["start"]
