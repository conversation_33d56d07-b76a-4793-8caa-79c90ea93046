{"create a H5 page base H5Layout": {"prefix": "h5layout", "body": ["import React from 'react';", "import H5Layout from '@/layouts/H5Layout';", "", "export default function ${1:Page}() {", " return (", "<H5Layout headerProps={{ background: \"white\" }}>", "${0}", "</H5Layout>", ");", "}"], "description": "create a H5 page base H5Layout", "isFileTemplate": true}, "create a React Component": {"prefix": "component", "body": ["import React from 'react';", "", "export default function ${1:Component}() {", "  return <div>${0}</div>;", "}", ""], "description": "create a React Component", "isFileTemplate": true}}