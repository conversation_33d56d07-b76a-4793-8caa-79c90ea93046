{
  // umi hook
  "useDispatch": {
    "prefix": "useDispatch",
    "body": ["const dispatch = useDispatch();"],
    "description": "import useDispatch from umi"
  },
  // @/hooks
  "useBoolean": {
    "prefix": "useBoolean",
    "body": ["const [${1:bool}, { setTrue, setFalse }] = useBoolean(${2|false,true|});"],
    "description": "useState for boolean"
  },
  "useModel": {
    "prefix": "useModel",
    "body": ["const { ${1} } = useModel('${1}');"],
    "description": "selector for models"
  },
  "useLoading": {
    "prefix": "useLoading",
    "body": ["const ${1:loading} = useLoading(['${2}']);"],
    "description": "dva loading"
  },
  "useIntl": {
    "prefix": "useIntl",
    "body": ["const { t } = useIntl();"],
    "description": "useIntl"
  }
}
