import React, { useEffect, useState } from 'react';
import { Input } from 'antd';
import { formatNbr } from '@/utils/utils';
import { NUMBER_FORMATTER_3_3_4, NUMBER_SINGLE_PLACEHOLDER } from '@/constants/constants';

export const numberFilter = str => str?.replace(/\D/g, '');
export const limitLength = (maxLength, str) => (maxLength ? str?.slice(0, maxLength) : str);

/**
 * 可以限制长度的数字输入框
 * @param {number} maxLength
 */
export default function InputNumber({ maxLength, value, onInput, onChange, ...rest }) {
  const [_value, setVal] = useState(value);
  useEffect(() => {
    setVal(value);
  }, [value]);

  const commonHandler = e => {
    const rawVal = e?.target?.value;
    const val = limitLength(maxLength, numberFilter(rawVal));
    e.target.value = val;
    setVal(val);
    return val;
  };

  const handleChange = e => {
    const val = commonHandler(e);
    onChange?.(val);
  };
  const handleInput = e => {
    const val = commonHandler(e);
    onInput?.(val);
  };

  return <Input type="tel" value={_value} {...rest} onInput={handleInput} onChange={handleChange} />;
}

const addonCount = {
  [NUMBER_FORMATTER_3_3_4]: 2,
};

export const SplittableInputNumber = ({
  split = NUMBER_FORMATTER_3_3_4,
  maxLength,
  placeholder,
  value,
  onChange,
  ...rest
}) => {
  const handleChange = e => {
    let inputValue = e.target.value.replace(/[^\d]/g, '');
    if (inputValue.length > maxLength) {
      inputValue = inputValue.substring(0, maxLength);
    }
    onChange?.(inputValue);
  };
  const formatValue = () => {
    if (!value) {
      return '';
    }
    let inputValue = formatNbr(value, split);
    if (inputValue[inputValue.length - 1] === ' ') {
      inputValue = inputValue.substring(0, inputValue.length - 1);
    }
    return inputValue;
  };
  const defaultPlaceholder = formatNbr(''.padStart(maxLength, NUMBER_SINGLE_PLACEHOLDER), split);

  return (
    <Input
      {...rest}
      placeholder={placeholder || defaultPlaceholder}
      value={formatValue()}
      onChange={handleChange}
      maxLength={maxLength + (addonCount[split] || 0)}
    />
  );
};
