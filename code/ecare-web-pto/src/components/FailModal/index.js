import React from 'react';
import { Button, Modal } from 'antd';
import classNames from 'classnames';
import imgNoData from '@/assets/img/claimFreebies/img_no_data_found@<EMAIL>';
import styles from './index.less';

const FailModal = ({ title, className, content, visible, icon, showRobotEntry = false, onCancel }) => {
  const handleChat = () => {
    onCancel?.();
    // 打开UCC机器人
    window.uccwebim?.show?.();
  };

  return (
    <Modal className={classNames(styles.modal, className)} visible={visible} closable={false} footer={null}>
      <div className={styles.main}>
        <p className={styles.title}>{title || 'Oops!'}</p>
        <p className={styles.content}>{content}</p>
      </div>
      <div className={styles.footer}>
        {/* 在线机器人入口 */}
        {showRobotEntry && (
          <Button onClick={handleChat} className={styles.robotEntry}>
            Chat with us
          </Button>
        )}
        <Button onClick={onCancel} className={styles.failBtn}>
          Close
        </Button>
      </div>
      <div className={styles.img}>
        {icon || (
          <div className={styles.placeholder}>
            <img src={imgNoData} alt="" />
          </div>
        )}
      </div>
    </Modal>
  );
};
export default FailModal;
