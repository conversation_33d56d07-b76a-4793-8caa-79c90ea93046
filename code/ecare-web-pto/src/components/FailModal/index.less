.modal {
  position: relative;
  min-width: 917px;

  :global {
    .ant-modal-content {
      min-width: 900px;
      min-height: 400px;
      padding: 40px 50px;
      opacity: 1;
      border-radius: 25px;
      background: #fff;
      box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
    }
    .ant-modal-body {
      padding: 0;
      padding-top: 40px;
    }
  }

  .main {
    width: 472px;

    .title {
      font-family: 'Exo 2';
      font-size: 20px;
      font-weight: 600;
      line-height: 45px;
      color: #333;
    }
    .content {
      font-family: Montserrat;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      color: #2F3043;
    }
  }
  .footer {
    position: absolute;
    bottom: 48px;

    .robotEntry,
    .robotEntry:hover,
    .robotEntry:focus, {
      color: #CE1126;
      border: 2px solid #CE1126;
    }
    .robotEntry {
      margin-right: 20px;
    }
    .failBtn,
    .failBtn:hover,
    .failBtn:focus, {
      color: #fff;
      border: 2px solid #CE1126;
      background: #CE1126;
    }
  }
  .img {
    position: absolute;
    top: 50%;
    right: 90px;
    transform: translateY(-50%);

    .placeholder {
      width: 206px;
      height: 174px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
