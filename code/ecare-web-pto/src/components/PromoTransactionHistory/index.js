import React, { useEffect, useState } from 'react';
import { Table, Row, Col, Select, DatePicker, Button, Input, notification, Spin, Tooltip } from 'antd';
import { isEmpty, get, orderBy, head } from 'lodash';
import { connect } from 'dva';
import moment from 'moment';
import { useIntl } from '@/hooks';
import iconSortUndesc from '@/assets/img/home/<USER>';
import iconSortUnascending from '@/assets/img/home/<USER>';
import EORFailModal from '@/components/FailModal';
import styles from '@/components/index.less';
import TransitionIsEnd from '../../pages/Promo/components/TransitionIsEnd';
import { getSubsInfo, previewBlob } from '../../utils/utils';
import {
  VIEW_ITEM,
  STATUS,
  STATUS_STYLE,
  DATA_FORMAT,
  PARAM_FORMAT,
  PROMO_HIS,
  PROMO_HIS_COLUMN,
  START_TIME,
  END_TIME,
  SHOW_FORMAT,
  PROMOSTATUSCODE,
  PAYMENT_RESULT,
  BUYPROMO_FAILD_INFO,
  BUYPROMO_SUCCESS_INFO,
  BACK_ERROR,
} from '../../constants/constants';
import PaymentModal from '../../pages/Promo/components/PaymentModal';

const { Option } = Select;

const PromoTransactionHistory = props => {
  moment.locale('en');
  const { t } = useIntl();
  const {
    downLoadFunc,
    qryRechargeHis,
    rechargeHis = {},
    rechargeHisDataSource,
    updateRechargeHisDataSource,
    updateSelectPromo,
    offerList,
    offerListLimited,
    TransactionCancel,
    TabActKey,
    loading,
    paymentResult,
    paymentCancel,
    TransitionSubmit,
    setRecordPromo,
    downloadEOR,
  } = props;
  // let formatTotalAmount = get(rechargeHis, 'formatTotalAmount', 0);
  // formatTotalAmount =
  //   !isEmpty(formatTotalAmount) && formatTotalAmount.indexOf('.') !== -1
  //     ? formatTotalAmount.substring(0, formatTotalAmount.indexOf('.'))
  //     : 0;

  const totalCount = get(rechargeHis, 'pageInfo.totalNumber', 0);
  const dataSource = rechargeHisDataSource;

  const [startDate, setStartDate] = useState(moment().subtract(6, 'days').format(DATA_FORMAT));
  const [endDate, setEndDate] = useState(moment().format(DATA_FORMAT));
  const [pageSize, setPageSize] = useState(VIEW_ITEM[0].value);
  const [pageIndex, setPageIndex] = useState(1);
  const [desc, setDesc] = useState(true);
  const [visible, setvisible] = useState(false);
  const [HTML, setHTML] = useState('');
  const [intransitInfo] = useState({});
  const [transitionIsEndVisible, settransitionIsEndVisible] = useState(false);
  const [delOrdPrompt, setdelOrdPrompt] = useState(false);
  const [messageInfo, setMessageInfo] = useState('');
  const [AllLoading, setAllLoading] = useState(false);
  const [failEORModalVisible, setFailEORModalVisible] = useState(false);
  const [loadingSet, setLoadingSet] = useState(new Set());
  const [his] = useState('history');

  // const subsInfo = getSubsInfo();
  // const accNbr = !isEmpty(subsInfo) && subsInfo.accNbr;
  const { subsId, accNbr } = getSubsInfo() || {};
  useEffect(() => {
    const currentScroll = document.documentElement.scrollTop || document.body.scrollTop;
    if (currentScroll > 0) {
      window.scrollTo(0, 0);
    }
  });
  const queryHistory = () => {
    const param = {
      prodInstId: subsId,
      startDate: moment(startDate).format(PARAM_FORMAT) + START_TIME,
      endDate: moment(endDate).format(PARAM_FORMAT) + END_TIME,
      pageSize,
      pageIndex,
    };
    if (moment(startDate).format(PARAM_FORMAT) > moment(endDate).format(PARAM_FORMAT)) {
      notification.error({
        message: 'error',
        description: 'start time after end time.',
      });
    } else {
      qryRechargeHis(param).then(res => {
        if (!isEmpty(res)) {
          const data = get(res, 'packageHisList');
          updateRechargeHisDataSource(data);
        }
      });
    }
  };
  useEffect(() => {
    queryHistory();
  }, [startDate, endDate, pageSize, pageIndex]);
  useEffect(() => {
    const param = {
      prodInstId: subsId,
      startDate: moment(startDate).format(PARAM_FORMAT) + START_TIME,
      endDate: moment(endDate).format(PARAM_FORMAT) + END_TIME,
      pageSize,
      pageIndex,
    };
    if (TabActKey === 'TransactionHistory') {
      qryRechargeHis(param).then(res => {
        if (!isEmpty(res)) {
          const data = get(res, 'packageHisList');
          updateRechargeHisDataSource(data);
        }
      });
    }
  }, [TabActKey]);

  const handleDownload = () => {
    const param = {
      fileName: PROMO_HIS,
      columns: PROMO_HIS_COLUMN,
      businessReq: {
        startDate: moment(startDate).format(PARAM_FORMAT) + START_TIME,
        endDate: moment(endDate).format(PARAM_FORMAT) + END_TIME,
        prodInstId: subsId,
      },
    };
    downLoadFunc(param);
  };

  const handleSort = () => {
    setDesc(!desc);
    let newData = dataSource;
    if (desc) {
      newData = orderBy(dataSource, item => Number(item.displayPrice.substr(1)), 'desc'); // 降序
    } else {
      newData = orderBy(dataSource, item => Number(item.displayPrice.substr(1)), 'asc'); // 升序
    }
    updateRechargeHisDataSource(newData);
  };

  const flatten = arr => [].concat(...arr.map(x => (Array.isArray(x) ? flatten(x) : x)));
  // 筛选在途单是哪个包
  const filterInTransit = res => {
    const list = [];
    list.push(offerList, offerListLimited);
    const newList = flatten(list);
    const goodsCode = get(res, 'offerId');
    if (goodsCode) {
      const items = newList.filter(promoItem => get(promoItem, 'relatedOfferId') === parseInt(goodsCode, 10));
      const item = head(items);
      updateSelectPromo(item);
    }
  };

  // 删除订单
  const cancelTansition = record => {
    TransactionCancel({ transSn: get(record, 'transSn') }).then(res => {
      if (Number(get(res, 'result')) === 0) {
        setdelOrdPrompt(true);
        setTimeout(() => {
          setdelOrdPrompt(false);
        }, 5000);
      } else {
        setMessageInfo(res?.message || BACK_ERROR);
        settransitionIsEndVisible(true);
      }
      queryHistory();
    });
    setAllLoading(false);
  };

  const paymentCancelFUN = (record, methodType) => {
    const transSn = get(record, 'transSn');
    if (transSn) {
      paymentCancel({
        paymentReqId: transSn,
      }).then(res => {
        // const paymentState = get(res, 'data.paymentState');
        if (res) {
          setAllLoading(false);
          if (methodType === 'pay') {
            setvisible(true); // 进入支付页面支付
          } else {
            cancelTansition(record); // 取消订单
          }
        } else {
          setMessageInfo(BACK_ERROR);
          settransitionIsEndVisible(true);
        }
      });
    }
  };

  // pac支付成功
  const paySuccess = record => {
    const transSn = get(record, 'transSn');
    setMessageInfo(BUYPROMO_SUCCESS_INFO);
    settransitionIsEndVisible(true);
    TransitionSubmit({ transSn }).then(() => {
      queryHistory();
    }); // 提交订单
    setAllLoading(false);
  };

  const selectResult = (record, paymentState, methodType) => {
    switch (paymentState) {
      case PAYMENT_RESULT.PROCESSING: // 处理中1
        paymentCancelFUN(record, methodType); // 调接口查询回退支付
        break;
      case PAYMENT_RESULT.SUCCESSFUL: // 2成功
        paySuccess(record);
        break;
      case PAYMENT_RESULT.FAILURE: // 3 失败
        if (methodType === 'pay') {
          setvisible(true); // 重新进入支付界面选择支付方式支付订单
          setAllLoading(false);
        } else {
          cancelTansition(record); // 直接删除订单
        }
        break;
      case PAYMENT_RESULT.FALLBACK: // 4已经回退
        if (methodType === 'pay') {
          setvisible(true); // 重新进入支付界面选择支付方式支付订单
          setAllLoading(false);
        } else {
          cancelTansition(record); // 取消订单
        }
        break;
      default:
        break;
    }
  };

  const queryPaymentResult = (record, methodType) => {
    paymentResult({ paymentReqId: get(record, 'transSn') }).then(res => {
      const paymentState = get(res, 'data.paymentState');
      selectResult(record, paymentState, methodType); // 处理中1    2 成功   3失败   4已回退
    });
  };
  const payAndCancel = (record, methodType) => {
    if (isEmpty(get(record, 'transSn'))) {
      setMessageInfo(BUYPROMO_FAILD_INFO);
      settransitionIsEndVisible(true); // 提示信息
    } else {
      setAllLoading(true);
      filterInTransit(record);
      queryPaymentResult(record, methodType); // 查询支付结果
    }
  };

  const payOrder = (record, methodType) => {
    payAndCancel(record, methodType);
    setRecordPromo(record); // 将records存
  };
  const cancelOrder = (record, methodType) => {
    payAndCancel(record, methodType);
    setRecordPromo();
  };
  const onCancel = () => {
    setvisible(false);
    // 查询包历史接口
    queryHistory();
  };

  const Okay = () => {
    settransitionIsEndVisible(false);
  };

  const MethodHTML = PayMethodHTML => {
    setHTML(PayMethodHTML);
    if (!PayMethodHTML.startsWith('http')) {
      setTimeout(() => {
        document.forms[0].target = 'target';
        document.forms[0].submit();
      }, 200);
    }
  };
  const closeInfo = () => {
    setdelOrdPrompt(false);
  };

  const handleViewEOR = record => {
    setLoadingSet(origin => {
      origin.add(record.custOrderId);
      return new Set(origin);
    });
    const { custOrderId } = record;
    downloadEOR({
      transType: 1, // 1为买包
      accNbr: record.accNbr || accNbr,
      transSN: custOrderId,
    })
      ?.then?.(result => {
        if (!result) {
          setFailEORModalVisible(true);
          return;
        }
        previewBlob(result);
      })
      .catch(() => setFailEORModalVisible(true))
      .finally(() =>
        setLoadingSet(origin => {
          origin.delete(record.custOrderId);
          return new Set(origin);
        }),
      );
  };

  const columns = [
    {
      width: 180,
      title: 'Details',
      key: 'acctId',
      dataIndex: 'offerName',
      render: text => <span>{text}</span>,
    },
    {
      width: 187,
      title: 'Transaction Date',
      dataIndex: 'acceptDate',
      render: text => (
        <Tooltip placement="topLeft" title={moment(text).format(SHOW_FORMAT)}>
          <span>{moment(text).format(SHOW_FORMAT)}</span>
        </Tooltip>
      ),
      sorter: {
        compare: (a, b) => moment(a.acceptDate).format('x') - moment(b.acceptDate).format('x'),
      },
    },
    {
      width: 110,
      title: 'Transaction ID',
      dataIndex: 'custOrderId',
      ellipsis: {
        showTitle: false,
      },
      render: custOrderId => (
        <Tooltip placement="topLeft" title={custOrderId}>
          {custOrderId}
        </Tooltip>
      ),
    },
    {
      width: 100,
      title: () => (
        <span>
          Price
          <img
            className={styles.sortIcon}
            src={desc ? iconSortUndesc : iconSortUnascending}
            alt="sort"
            onClick={handleSort}
          />
        </span>
      ),
      dataIndex: 'displayPrice',
    },
    {
      width: 149,
      title: 'Payment Method',
      dataIndex: 'paymentMethodId',
      ellipsis: {
        showTitle: false,
      },
      render: (_, row) => {
        const isComboPay = row.paymentMethodList?.length > 1;
        const paymentMethodId = row.paymentMethodList?.[0]?.paymentMethodId || row.paymentMethodId;
        const isPointPay = paymentMethodId === 9;
        let title = 'Online Payment';
        if (isComboPay) {
          title = t('tips_buy_promo_combo_pay_name');
        } else if (isPointPay) {
          title = 'DITO Points';
        }
        return (
          <Tooltip placement="topLeft" title={title}>
            {title}
          </Tooltip>
        );
      },
    },
    {
      // width: 300,
      title: 'Status',
      dataIndex: 'orderItemStatusCode',
      render: (text, record) => {
        const { autoRenewFlag, autoSignFlag } = record;
        return text === PROMOSTATUSCODE ? (
          <>
            <div style={{ color: STATUS_STYLE[text] }}> {STATUS[text]}</div>
            {autoRenewFlag || autoSignFlag || (
              <div className={styles.payNowWrap}>
                <span
                  style={{ color: '#0038A8', paddingRight: '18px', cursor: 'pointer' }}
                  onClick={() => {
                    payOrder(record, 'pay');
                  }}
                >
                  Pay Now
                </span>
                <span>
                  <span
                    className="icon-x-circle-fill"
                    style={{ color: '#E50000', cursor: 'pointer', fontSize: '16px' }}
                  />

                  <span
                    onClick={() => {
                      cancelOrder(record, 'cancel');
                    }}
                    style={{ color: '#E50000', cursor: 'pointer' }}
                  >
                    Cancel
                  </span>
                </span>
              </div>
            )}
          </>
        ) : (
          <>
            <span style={{ color: STATUS_STYLE[text] || '#4AE100' }}>{STATUS[text] || 'In Progress'}</span>
          </>
        );
      },
      sorter: {
        compare: (a, b) => a.orderItemStatusCode - b.orderItemStatusCode,
      },
    },
    {
      title: null,
      key: 'View',
      width: 80,
      render: (_, record) =>
        // Completed状态才展示
        record.orderItemStatusName === 'Completion' ? (
          <div className={styles.viewBtn}>
            <Button
              type="text"
              loading={!!loadingSet.has(record.transactionId)}
              style={{ color: '#0038A8', cursor: 'pointer' }}
              onClick={() => {
                handleViewEOR(record);
              }}
            >
              View
            </Button>
          </div>
        ) : null,
    },
  ];
  return (
    <Spin spinning={AllLoading} tip="Loading...">
      <div className={styles.HistoryTable}>
        {HTML && HTML.startsWith('http') ? (
          <div />
        ) : (
          // eslint-disable-next-line react/no-danger
          <div style={{ display: 'none' }} dangerouslySetInnerHTML={{ __html: HTML }} />
        )}
        <Row>
          <Col span={18}>
            <div style={{ display: 'inline-block', position: 'relative', top: '-1px' }}>
              <span className={styles.label}>Filter</span>
              <Input
                disabled
                defaultValue="All"
                style={{
                  maxWidth: '170px',
                  borderRadius: '9px',
                  margin: '0 7px',
                  border: '1px solid #CCCCCC',
                  background: '#fff',
                  color: '#333',
                }}
              />
            </div>

            <DatePicker
              format={DATA_FORMAT}
              style={{ minWidth: 156, top: 0 }}
              onChange={date => {
                setStartDate(date);
                setPageIndex(1);
              }}
              disabledDate={current => current && (current > moment(endDate) || current > moment().endOf('day'))}
              defaultValue={moment(startDate, DATA_FORMAT)}
              allowClear={false}
            />
            <DatePicker
              format={DATA_FORMAT}
              style={{ minWidth: 156, top: 0 }}
              onChange={date => {
                setEndDate(date);
                setPageIndex(1);
              }}
              disabledDate={current => current && (current < moment(startDate) || current > moment().endOf('day'))}
              defaultValue={moment(endDate, DATA_FORMAT)}
              allowClear={false}
            />
          </Col>
          <Col span={6} className={styles.View}>
            <span className={styles.label}>View</span>
            <Select
              defaultValue={pageSize}
              style={{ width: 120 }}
              onChange={item => {
                setPageSize(item);
                setPageIndex(1);
              }}
            >
              {VIEW_ITEM.map(item => (
                <Option key={item.value} value={item.value}>
                  {item.label}
                </Option>
              ))}
            </Select>
          </Col>
        </Row>
        {delOrdPrompt && (
          <div>
            <p className={styles.delOrdPrompt}>
              You have successfully cancelled a pending transaction. You can now purchase any of your preferred promo.
              <span
                onClick={closeInfo}
                className="icon-close"
                style={{ color: '#2F3043', fontSize: '18px', paddingRight: '18px', float: 'right' }}
              />
            </p>
          </div>
        )}
        <Table
          loading={loading}
          onChange={pagination => {
            setPageIndex(pagination.current);
          }}
          bordered
          columns={columns}
          dataSource={dataSource}
          pagination={{
            current: pageIndex,
            pageSize,
            total: totalCount,
            showSizeChanger: false,
            showTotal: (total, range) => `${range[1]} of ${total}`,
          }}
          // scroll={{ x: 933 }}
        />
        <div className={`${styles.download} ${isEmpty(dataSource) && styles.downloadPosition}`}>
          <Button onClick={handleDownload} disabled={isEmpty(dataSource)}>
            Download PDF
          </Button>
          {/* <div className={styles.total}>Total of {formatTotalAmount}</div> */}
        </div>
        {visible && (
          <PaymentModal
            visible={visible}
            onCancel={() => {
              onCancel();
            }}
            his={his}
            PayMethodHTML={MethodHTML}
            intransitInfo={intransitInfo}
          />
        )}
        <TransitionIsEnd
          Visible={transitionIsEndVisible}
          messageInfo={messageInfo}
          intransitInfo={intransitInfo}
          Okay={Okay}
        />
        <EORFailModal
          visible={failEORModalVisible}
          showRobotEntry
          content="There seems to be a problem with the data that you are requesting. You can also chat with our helpful customer service representatives for your inquiries."
          onCancel={() => {
            setFailEORModalVisible(false);
          }}
        />
      </div>
    </Spin>
  );
};

const mapDispatchToProps = dispatch => ({
  qryRechargeHis: payload =>
    dispatch({
      type: 'promo/qryRechargeHis',
      payload,
    }),
  downLoadFunc: payload =>
    dispatch({
      type: 'promo/downLoadFunc',
      payload,
    }),
  updateRechargeHisDataSource: payload =>
    dispatch({
      type: 'promo/updateRechargeHisDataSource',
      payload,
    }),
  updateSelectPromo: payload =>
    dispatch({
      type: 'promo/updateSelectPromo',
      payload,
    }),
  TransactionCancel: payload =>
    dispatch({
      type: 'promo/TransactionCancel',
      payload,
    }),
  paymentResult: payload =>
    dispatch({
      type: 'global/paymentResult',
      payload,
    }),
  paymentCancel: payload =>
    dispatch({
      type: 'global/paymentCancel',
      payload,
    }),
  TransitionSubmit: payload =>
    dispatch({
      type: 'promo/TransitionSubmit',
      payload,
    }),
  setRecordPromo: payload =>
    dispatch({
      type: 'promo/setRecordPromo',
      payload,
    }),
  downloadEOR: payload =>
    dispatch({
      type: 'global/downloadEOR',
      payload,
    }),
});

export default connect(
  ({ promo, loading }) => ({
    rechargeHis: promo.rechargeHis,
    offerList: promo.offerList,
    offerListLimited: promo.offerListLimited,
    rechargeHisDataSource: promo.rechargeHisDataSource,
    TabActKey: promo.TabActKey,
    loading: loading.effects['promo/qryRechargeHis'],
  }),
  mapDispatchToProps,
)(PromoTransactionHistory);
