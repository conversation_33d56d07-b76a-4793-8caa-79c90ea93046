import React from 'react';

export function QuestionCircleOutline(props) {
  return (
    <svg width="1em" height="1em" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6 10.875C8.69239 10.875 10.875 8.69239 10.875 6C10.875 3.30761 8.69239 1.125 6 1.125C3.30761 1.125 1.125 3.30761 1.125 6C1.125 8.69239 3.30761 10.875 6 10.875ZM6 12C9.31371 12 12 9.31371 12 6C12 2.68629 9.31371 0 6 0C2.68629 0 0 2.68629 0 6C0 9.31371 2.68629 12 6 12Z"
        fill="currentColor"
      />
      <path
        d="M5.25 9C5.25 8.58579 5.58579 8.25 6 8.25C6.41421 8.25 6.75 8.58579 6.75 9C6.75 9.41421 6.41421 9.75 6 9.75C5.58579 9.75 5.25 9.41421 5.25 9Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.875 4.3125C8.18566 4.3125 8.4375 4.56434 8.4375 4.875V4.99028C8.4375 5.83013 7.92618 6.58536 7.14641 6.89727L6.56247 7.13084C6.55934 7.43881 6.30871 7.6875 6 7.6875C5.68934 7.6875 5.4375 7.43566 5.4375 7.125C5.4375 6.6685 5.71543 6.258 6.13927 6.08846L6.72859 5.85273C7.08125 5.71167 7.3125 5.37011 7.3125 4.99028V4.875C7.3125 4.56434 7.56434 4.3125 7.875 4.3125Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6 3.5625C5.27513 3.5625 4.6875 4.15013 4.6875 4.875C4.6875 5.18566 4.43566 5.4375 4.125 5.4375C3.81434 5.4375 3.5625 5.18566 3.5625 4.875C3.5625 3.52881 4.65381 2.4375 6 2.4375C7.34619 2.4375 8.4375 3.52881 8.4375 4.875C8.4375 5.18566 8.18566 5.4375 7.875 5.4375C7.56434 5.4375 7.3125 5.18566 7.3125 4.875C7.3125 4.15013 6.72487 3.5625 6 3.5625Z"
        fill="currentColor"
      />
    </svg>
  );
}
