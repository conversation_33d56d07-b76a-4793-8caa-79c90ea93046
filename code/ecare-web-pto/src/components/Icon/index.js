import React, { useState } from 'react';
import classNames from 'classnames';
import styles from './index.less';

const Icon = ({
  active = false,
  activeSrc,
  className,
  disabledSrc,
  disabled = false,
  hoverable = true,
  src,
  size,
  width,
  height,
  style = {},
  onClick,
  ...restProps
}) => {
  const [hovering, setHovering] = useState(false);

  let realSrc = (hoverable && hovering) || active ? activeSrc : src;
  if (disabled && disabledSrc) {
    realSrc = disabledSrc;
  }

  const localStyles = {};
  if (size || width) {
    localStyles.width = width || size;
  }
  if (size || height) {
    localStyles.height = height || size;
  }

  return (
    <span className={classNames(styles.iconWrap, className)} style={{ ...localStyles }}>
      <img
        className={styles.icon}
        style={{ ...localStyles, ...style }}
        alt=""
        src={realSrc || src}
        {...restProps}
        onClick={e => {
          if (disabled) return;
          onClick?.(e);
        }}
        onFocus={() => {}}
        onMouseOver={() => setHovering(true)}
        onMouseLeave={() => setHovering(false)}
      />
    </span>
  );
};

export default Icon;
