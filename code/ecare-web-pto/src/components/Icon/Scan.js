import React from 'react';

export function Scan(props) {
  return (
    <svg width="1em" height="1em" viewBox="0 0 16 17" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0 8.5C0 8.08579 0.335786 7.75 0.75 7.75H15.25C15.6642 7.75 16 8.08579 16 8.5C16 8.91421 15.6642 9.25 15.25 9.25H0.75C0.335786 9.25 0 8.91421 0 8.5Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.80545 0.500004C3.82088 0.500005 3.83635 0.500007 3.85186 0.500007H4.88889C5.31845 0.500007 5.66667 0.846625 5.66667 1.2742C5.66667 1.70178 5.31845 2.04839 4.88889 2.04839H3.85186C3.34101 2.04839 3.03611 2.05004 2.81758 2.07928C2.71745 2.09268 2.6673 2.10847 2.64471 2.11772C2.63948 2.11986 2.63604 2.12153 2.63404 2.12257L2.63151 2.12399L2.63008 2.12651C2.62904 2.1285 2.62736 2.13192 2.62521 2.13713C2.61592 2.15961 2.60006 2.20953 2.58659 2.30921C2.55721 2.52673 2.55556 2.83022 2.55556 3.33871V3.85484C2.55556 4.28242 2.20734 4.62904 1.77778 4.62904C1.34823 4.62904 1.00001 4.28242 1.00001 3.85484V3.33871C1.00001 3.32327 1.00001 3.30788 1 3.29252C0.999951 2.84575 0.999903 2.43611 1.04491 2.10289C1.09447 1.73594 1.21109 1.3481 1.53155 1.0291C1.85202 0.710113 2.24166 0.594038 2.61031 0.544704C2.94508 0.499903 3.35661 0.499951 3.80545 0.500004ZM13.1824 2.07928C12.9639 2.05004 12.659 2.04839 12.1481 2.04839H11.1111C10.6816 2.04839 10.3333 1.70178 10.3333 1.2742C10.3333 0.846625 10.6816 0.500007 11.1111 0.500007L12.1945 0.500004C12.6434 0.499951 13.0549 0.499903 13.3897 0.544704C13.7583 0.594038 14.148 0.710113 14.4684 1.0291C14.7889 1.3481 14.9055 1.73594 14.9551 2.10289C15.0001 2.43611 15 2.84575 15 3.29253L15 3.85484C15 4.28242 14.6518 4.62904 14.2222 4.62904C13.7927 4.62904 13.4444 4.28242 13.4444 3.85484V3.33871C13.4444 2.83022 13.4428 2.52673 13.4134 2.30921C13.3999 2.20953 13.3841 2.15961 13.3748 2.13713C13.3726 2.13192 13.371 2.1285 13.3699 2.12651L13.3685 2.12399L13.366 2.12257C13.364 2.12153 13.3605 2.11986 13.3553 2.11772C13.3327 2.10847 13.2826 2.09268 13.1824 2.07928ZM1.77778 12.371C2.20734 12.371 2.55556 12.7176 2.55556 13.1452V13.6613C2.55556 14.1698 2.55721 14.4733 2.58659 14.6908C2.60006 14.7905 2.61592 14.8404 2.62521 14.8629C2.62736 14.8681 2.62904 14.8715 2.63008 14.8735L2.63151 14.876L2.63404 14.8774C2.63604 14.8785 2.63948 14.8801 2.64471 14.8823C2.6673 14.8915 2.71745 14.9073 2.81758 14.9207C3.03611 14.95 3.34101 14.9516 3.85186 14.9516H4.88889C5.31845 14.9516 5.66667 15.2982 5.66667 15.7258C5.66667 16.1534 5.31845 16.5 4.88889 16.5L3.80546 16.5C3.35661 16.5 2.94508 16.5001 2.61031 16.4553C2.24166 16.406 1.85202 16.2899 1.53155 15.9709C1.21109 15.6519 1.09447 15.2641 1.04491 14.8971C0.999903 14.5639 0.999951 14.1543 1 13.7075L1.00001 13.1452C1.00001 12.7176 1.34823 12.371 1.77778 12.371ZM14.2222 12.371C14.6518 12.371 15 12.7176 15 13.1452L15 13.7075C15 14.1543 15.0001 14.5639 14.9551 14.8971C14.9055 15.2641 14.7889 15.6519 14.4684 15.9709C14.148 16.2899 13.7583 16.406 13.3897 16.4553C13.0549 16.5001 12.6434 16.5 12.1945 16.5L11.1111 16.5C10.6816 16.5 10.3333 16.1534 10.3333 15.7258C10.3333 15.2982 10.6816 14.9516 11.1111 14.9516H12.1481C12.659 14.9516 12.9639 14.95 13.1824 14.9207C13.2826 14.9073 13.3327 14.8915 13.3553 14.8823C13.3605 14.8801 13.364 14.8785 13.366 14.8774L13.3685 14.876L13.3699 14.8735C13.371 14.8715 13.3726 14.8681 13.3748 14.8629C13.3841 14.8404 13.3999 14.7905 13.4134 14.6908C13.4428 14.4733 13.4444 14.1698 13.4444 13.6613V13.1452C13.4444 12.7176 13.7927 12.371 14.2222 12.371Z"
        fill="currentColor"
      />
    </svg>
  );
}
