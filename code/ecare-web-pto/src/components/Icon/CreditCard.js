import React from 'react';

export function CreditCard(props) {
  return (
    <svg width="1em" height="1em" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.87408 5L1.87409 15C1.87409 15.2477 1.87417 15.4485 1.87697 15.6221C2.05052 15.6249 2.2512 15.625 2.49878 15.625L17.4915 15.625C17.739 15.625 17.9397 15.6249 18.1133 15.6221C18.1161 15.4485 18.1162 15.2477 18.1162 15V5C18.1162 4.7523 18.1161 4.55152 18.1133 4.37788C17.9397 4.37508 17.739 4.375 17.4915 4.375L2.49878 4.375C2.2512 4.375 2.05052 4.37509 1.87697 4.37789C1.87417 4.55152 1.87408 4.75231 1.87408 5ZM0.365938 17.1339C0.731876 17.5 1.32084 17.5 2.49878 17.5L17.4915 17.5C18.6694 17.5 19.2584 17.5 19.6243 17.1339C19.9902 16.7678 19.9902 16.1785 19.9902 15V5C19.9902 3.82149 19.9902 3.23223 19.6243 2.86612C19.2584 2.5 18.6694 2.5 17.4915 2.5L2.49878 2.5C1.32084 2.5 0.731876 2.5 0.365938 2.86612C-1.11704e-07 3.23223 -5.14529e-08 3.82149 0 5L4.36938e-07 15C4.88427e-07 16.1785 5.21286e-07 16.7678 0.365938 17.1339Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.81113 13.125C2.81113 12.6072 3.23066 12.1875 3.74817 12.1875H8.12103C8.63855 12.1875 9.05808 12.6072 9.05808 13.125C9.05808 13.6428 8.63855 14.0625 8.12103 14.0625H3.74817C3.23066 14.0625 2.81113 13.6428 2.81113 13.125Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.312347 7.5C0.312347 6.98223 0.731876 6.5625 1.24939 6.5625H18.7408C19.2584 6.5625 19.6779 6.98223 19.6779 7.5C19.6779 8.01777 19.2584 8.4375 18.7408 8.4375H1.24939C0.731876 8.4375 0.312347 8.01777 0.312347 7.5Z"
        fill="currentColor"
      />
    </svg>
  );
}
