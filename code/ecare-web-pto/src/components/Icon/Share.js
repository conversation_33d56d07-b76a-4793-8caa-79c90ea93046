import React from 'react';

export default function Share(props) {
  return (
    <svg width="1em" height="1em" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.20738 3.63668C9.40792 3.86242 9.3876 4.20808 9.16199 4.40874L5.95396 7.26207C5.8083 7.39162 5.80703 7.61893 5.95123 7.7501L10.041 11.4703C10.2643 11.6735 10.2808 12.0194 10.0778 12.2429C9.87472 12.4664 9.52906 12.4828 9.30571 12.2797L5.21594 8.55941C4.59108 7.99101 4.59658 7.00598 5.22775 6.44459L8.43579 3.59126C8.66139 3.3906 9.00684 3.41094 9.20738 3.63668Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.5564 10.5625V13.1875H13.1798V10.5625H10.5564ZM10.1191 9.25C9.63617 9.25 9.24466 9.64175 9.24466 10.125V13.625C9.24466 14.1082 9.63617 14.5 10.1191 14.5H13.617C14.0999 14.5 14.4915 14.1082 14.4915 13.625V10.125C14.4915 9.64175 14.0999 9.25 13.617 9.25H10.1191Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.8117 6.1875V8.8125H4.4351V6.1875H1.8117ZM1.37447 4.875C0.891512 4.875 0.5 5.26675 0.5 5.75V9.25C0.5 9.73325 0.891512 10.125 1.37447 10.125H4.87233C5.35529 10.125 5.7468 9.73325 5.7468 9.25V5.75C5.7468 5.26675 5.35529 4.875 4.87233 4.875H1.37447Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.46328 1.59375V3.78125H11.6494V1.59375H9.46328ZM9.24466 0.5C8.76171 0.5 8.3702 0.891751 8.3702 1.375V4C8.3702 4.48325 8.76171 4.875 9.24466 4.875H11.8681C12.351 4.875 12.7425 4.48325 12.7425 4V1.375C12.7425 0.891751 12.351 0.5 11.8681 0.5H9.24466Z"
        fill="currentColor"
      />
    </svg>
  );
}
