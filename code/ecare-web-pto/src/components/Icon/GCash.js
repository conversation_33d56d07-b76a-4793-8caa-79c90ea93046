import React from 'react';

export default function GCash(props) {
  return (
    <svg width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M6.95796 2.40006C8.17248 2.00328 9.46441 1.83876 10.7418 1.92102C12.6144 2.02264 14.4434 2.68554 15.9676 3.77425C16.1322 3.89522 16.3112 4.00651 16.4515 4.16135C16.6934 4.43715 16.7902 4.82909 16.7031 5.18715C16.616 5.57425 16.3112 5.90328 15.9338 6.01941C15.566 6.13554 15.1451 6.05812 14.8499 5.81135C13.7128 4.93554 12.3289 4.38393 10.9015 4.24844C10.3789 4.19038 9.84667 4.19038 9.32409 4.24844C7.81925 4.39844 6.3628 5.00328 5.19183 5.96618C3.87086 7.04522 2.90796 8.56457 2.53054 10.2291C2.18215 11.7388 2.28861 13.3549 2.8499 14.8017C3.44022 16.3404 4.53861 17.671 5.92248 18.5613C6.95796 19.2242 8.15312 19.6355 9.37248 19.7517C9.84667 19.8097 10.3305 19.8097 10.7999 19.7565C12.2757 19.6307 13.7031 19.0646 14.8741 18.1597C15.1693 17.9275 15.5902 17.8597 15.9483 17.9807C16.4418 18.1501 16.7951 18.6775 16.7322 19.2001C16.7031 19.5049 16.5434 19.7952 16.3015 19.9791C15.3725 20.7049 14.3176 21.2613 13.1951 21.6194C11.7483 22.0839 10.1902 22.2049 8.68538 21.9968C7.01603 21.7646 5.40957 21.0968 4.06441 20.0807C2.75796 19.1033 1.69344 17.8017 0.991832 16.3259C0.304735 14.8791 -0.0291361 13.263 0.0289284 11.6613C0.0821542 9.69199 0.73538 7.74683 1.88699 6.14522C3.1257 4.40328 4.92086 3.06296 6.95796 2.40006Z"
        fill="#007CFF"
      />
      <path
        d="M21.116 5.14844C21.6192 4.99844 22.2096 5.23554 22.4515 5.70973C23.6128 7.97909 24.1209 10.5726 23.9128 13.113C23.7676 14.9226 23.2693 16.7081 22.4322 18.3194C22.1273 18.8952 21.3096 19.0791 20.7822 18.7017C20.2934 18.3872 20.1338 17.6855 20.4241 17.1775C22.0402 14.0033 22.0547 10.0694 20.458 6.88554C20.3128 6.62425 20.2354 6.31457 20.308 6.01941C20.3902 5.60812 20.7144 5.25973 21.116 5.14844Z"
        fill="#6FBAF7"
      />
      <path
        d="M6.80312 6.50812C8.85474 5.26941 11.6273 5.30328 13.6209 6.65328C14.1144 7.01618 14.216 7.79038 13.8338 8.26941C13.4902 8.73876 12.7789 8.87909 12.2902 8.56457C11.516 8.07102 10.5773 7.85812 9.66764 7.96457C8.75796 8.06135 7.88699 8.48231 7.24828 9.13554C6.60957 9.76941 6.19344 10.621 6.09183 11.5162C5.9999 12.242 6.12086 12.992 6.42086 13.6597C6.81764 14.5404 7.53861 15.271 8.41925 15.6775C9.31925 16.1033 10.3741 16.171 11.3225 15.8759C12.5902 15.4888 13.6402 14.4388 14.0031 13.1662C13.0934 13.171 12.1838 13.1662 11.2741 13.1662C10.8386 13.1662 10.4176 12.8952 10.2386 12.5033C10.016 12.0533 10.1322 11.4678 10.5096 11.1388C10.7031 10.9646 10.9596 10.8484 11.2209 10.8388C12.5951 10.8291 13.9644 10.8339 15.3386 10.8339C15.7886 10.8194 16.2241 11.1001 16.4031 11.5113C16.558 11.8307 16.5096 12.1936 16.4902 12.5372C16.3692 14.0275 15.6967 15.4646 14.6322 16.5194C13.6112 17.5452 12.2322 18.2081 10.7902 18.3533C9.37248 18.5081 7.90635 18.1646 6.70151 17.3952C5.48699 16.6259 4.53861 15.4307 4.07409 14.0662C3.5999 12.6872 3.62409 11.1436 4.11764 9.77425C4.60151 8.41941 5.56925 7.24844 6.80312 6.50812Z"
        fill="#002CB8"
      />
      <path
        d="M17.9709 7.14199C18.4692 7.01134 19.0354 7.2678 19.2676 7.72747C20.5257 10.3888 20.5257 13.6113 19.2676 16.2726C19.108 16.5871 18.7886 16.8194 18.4354 16.8775C18.0241 16.9501 17.5838 16.7759 17.3273 16.4468C17.0612 16.1033 17.0128 15.6097 17.2063 15.2226C18.1451 13.1855 18.1305 10.742 17.1822 8.70973C16.8967 8.08554 17.3031 7.28715 17.9709 7.14199Z"
        fill="#6FBAF7"
      />
    </svg>
  );
}
