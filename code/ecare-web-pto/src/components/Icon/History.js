import React from 'react';

export default function History(props) {
  return (
    <svg width="1em" height="1em" viewBox="0 0 14 15" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.48387 2.15369C2.24724 2.33871 2.02586 2.5397 1.8203 2.7555C0.612309 4.02364 0 5.75094 0 7.61118C0 11.4158 3.13401 14.5 7 14.5C10.866 14.5 14 11.4158 14 7.61118C14 7.24299 13.6967 6.94452 13.3226 6.94452C12.9485 6.94452 12.6452 7.24299 12.6452 7.61118C12.6452 10.6794 10.1177 13.1667 7 13.1667C3.88226 13.1667 1.35484 10.6794 1.35484 7.61118C1.35484 6.22781 1.75288 4.99264 2.48387 4.04599V2.15369ZM13.3226 4.72194C13.4766 4.6458 13.4763 4.64518 13.4763 4.64518L13.4755 4.64364L13.4734 4.63958L13.4671 4.62763C13.4621 4.61799 13.4551 4.60503 13.4464 4.58898C13.4288 4.55687 13.4039 4.51233 13.3713 4.45713C13.3566 4.43223 13.3404 4.40514 13.3226 4.37602V4.72194Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.8203 12.2446C2.02586 12.4604 2.24724 12.6614 2.48387 12.8464V10.9541C1.75288 10.0075 1.35484 8.77232 1.35484 7.38896C1.35484 4.32073 3.88226 1.83345 7 1.83345C9.22645 1.83345 11.1519 3.1019 12.0708 4.94453H13.5465C12.5444 2.3466 9.99129 0.500122 7 0.500122C3.13401 0.500122 0 3.58436 0 7.38896C0 9.2492 0.612309 10.9765 1.8203 12.2446Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.3226 1.16679C12.9485 1.16679 12.6452 1.46526 12.6452 1.83345V4.27787H10.1613C9.78716 4.27787 9.48387 4.57635 9.48387 4.94453C9.48387 5.31272 9.78716 5.6112 10.1613 5.6112H13.0968C13.5956 5.6112 14 5.21323 14 4.72231V1.83345C14 1.46526 13.6967 1.16679 13.3226 1.16679Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7 4.27787C7.37413 4.27787 7.67742 4.57635 7.67742 4.94453V7.47384C7.67742 7.81052 7.48413 8.11831 7.17813 8.26888L4.59327 9.54078C4.25864 9.70544 3.85174 9.57196 3.68442 9.24264C3.51711 8.91333 3.65274 8.51288 3.98737 8.34822L6.32258 7.19916V4.94453C6.32258 4.57635 6.62587 4.27787 7 4.27787Z"
        fill="currentColor"
      />
    </svg>
  );
}
