import React from 'react';

export function Wallet(props) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 22" fill="none" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.25 7.49829L2.25 18.7038C2.25 18.9813 2.2501 19.2063 2.25346 19.4009C2.46183 19.404 2.70276 19.4041 3 19.4041L21 19.4041C21.2972 19.4041 21.5382 19.404 21.7465 19.4009C21.7499 19.2063 21.75 18.9813 21.75 18.7038V7.49829C21.75 7.22074 21.7499 6.99575 21.7465 6.80118C21.5382 6.79804 21.2972 6.79795 21 6.79795H3C2.70276 6.79795 2.46182 6.79805 2.25346 6.80118C2.2501 6.99575 2.25 7.22074 2.25 7.49829ZM0.43934 21.0949C0.87868 21.5051 1.58579 21.5051 3 21.5051L21 21.5051C22.4142 21.5051 23.1213 21.5051 23.5607 21.0949C24 20.6846 24 20.0243 24 18.7038V7.49829C24 6.17771 24 5.51743 23.5607 5.10717C23.1213 4.69692 22.4142 4.69692 21 4.69692L3 4.69692C1.58579 4.69692 0.87868 4.69692 0.43934 5.10717C-1.3411e-07 5.51743 -6.17709e-08 6.17771 0 7.49829L5.24584e-07 18.7038C5.86401e-07 20.0243 6.25849e-07 20.6846 0.43934 21.0949Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.0625 10.6498C11.0625 9.97295 11.6501 9.42423 12.375 9.42423H22.5C23.0178 9.42423 23.4375 9.81617 23.4375 10.2997C23.4375 10.7831 23.0178 11.1751 22.5 11.1751H12.9375V15.027H22.5C23.0178 15.027 23.4375 15.4189 23.4375 15.9024C23.4375 16.3859 23.0178 16.7778 22.5 16.7778H12.375C11.6501 16.7778 11.0625 16.2291 11.0625 15.5522V10.6498Z"
        fill="#CE1126"
      />
      <path
        d="M16.5 13.101C16.5 13.8746 15.8284 14.5017 15 14.5017C14.1716 14.5017 13.5 13.8746 13.5 13.101C13.5 12.3274 14.1716 11.7003 15 11.7003C15.8284 11.7003 16.5 12.3274 16.5 13.101Z"
        fill="#CE1126"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15 12.7509C14.7929 12.7509 14.625 12.9076 14.625 13.101C14.625 13.2944 14.7929 13.4512 15 13.4512C15.2071 13.4512 15.375 13.2944 15.375 13.101C15.375 12.9076 15.2071 12.7509 15 12.7509ZM15 14.5017C15.8284 14.5017 16.5 13.8746 16.5 13.101C16.5 12.3274 15.8284 11.7003 15 11.7003C14.1716 11.7003 13.5 12.3274 13.5 13.101C13.5 13.8746 14.1716 14.5017 15 14.5017Z"
        fill="#CE1126"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.55717e-07 1.89556C6.55717e-07 1.12198 0.671572 0.494873 1.5 0.494873H19.875C20.4963 0.494873 21 0.965204 21 1.54539C21 2.12557 20.4963 2.5959 19.875 2.5959H2.25V7.84846C2.25 8.42864 1.74632 8.89897 1.125 8.89897C0.50368 8.89897 6.55717e-07 8.42864 6.55717e-07 7.84846V1.89556Z"
        fill="currentColor"
      />
    </svg>
  );
}
