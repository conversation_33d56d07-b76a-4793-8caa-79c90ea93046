import React from 'react';

export default function SIMCard(props) {
  return (
    <svg width="1em" height="1em" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M0 5.33333C0 2.38782 2.38781 0 5.33333 0H26.6667C29.6122 0 32 2.38781 32 5.33333V26.6667C32 29.6122 29.6122 32 26.6667 32H5.33333C2.38782 32 0 29.6122 0 26.6667V5.33333Z"
        fill="#0038A7"
      />
      <path
        d="M24 13.6122C24 12.1588 24 11.4322 23.7294 10.7788C23.4587 10.1254 22.9449 9.61153 21.9172 8.58387L20.7495 7.41613C19.7218 6.38847 19.208 5.87463 18.5546 5.60398C17.9012 5.33333 17.1745 5.33333 15.7211 5.33333H15.1111C11.7589 5.33333 10.0828 5.33333 9.0414 6.37473C8 7.41613 8 9.09223 8 12.4444V19.5556C8 22.9078 8 24.5839 9.0414 25.6253C10.0828 26.6667 11.7589 26.6667 15.1111 26.6667H16.8889C20.2411 26.6667 21.9172 26.6667 22.9586 25.6253C24 24.5839 24 22.9078 24 19.5556V13.6122Z"
        fill="#FCD116"
      />
      <path
        d="M11.5503 17.7778C11.5503 16.1017 11.5503 15.2636 12.071 14.7429C12.5917 14.2222 13.4298 14.2222 15.1059 14.2222H16.8837C18.5598 14.2222 19.3978 14.2222 19.9185 14.7429C20.4392 15.2636 20.4392 16.1017 20.4392 17.7778V19.5556C20.4392 21.2317 20.4392 22.0697 19.9185 22.5904C19.3978 23.1111 18.5598 23.1111 16.8837 23.1111H15.1059C13.4298 23.1111 12.5917 23.1111 12.071 22.5904C11.5503 22.0697 11.5503 21.2317 11.5503 19.5556V17.7778Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.8837 15.1111H15.1059C14.2427 15.1111 13.6848 15.113 13.2736 15.1683C12.8881 15.2201 12.7673 15.3038 12.6996 15.3715C12.6319 15.4391 12.5482 15.56 12.4964 15.9455C12.4411 16.3566 12.4392 16.9146 12.4392 17.7778V19.5556C12.4392 20.4187 12.4411 20.9767 12.4964 21.3878C12.5482 21.7734 12.6319 21.8942 12.6996 21.9619C12.7673 22.0295 12.8881 22.1132 13.2736 22.1651C13.6848 22.2203 14.2427 22.2222 15.1059 22.2222H16.8837C17.7469 22.2222 18.3048 22.2203 18.7159 22.1651C19.1015 22.1132 19.2223 22.0295 19.29 21.9619C19.3577 21.8942 19.4414 21.7734 19.4932 21.3878C19.5485 20.9767 19.5503 20.4187 19.5503 19.5556V17.7778C19.5503 16.9146 19.5485 16.3566 19.4932 15.9455C19.4414 15.56 19.3577 15.4391 19.29 15.3715C19.2223 15.3038 19.1015 15.2201 18.7159 15.1683C18.3048 15.113 17.7469 15.1111 16.8837 15.1111ZM12.071 14.7429C11.5503 15.2636 11.5503 16.1017 11.5503 17.7778V19.5556C11.5503 21.2317 11.5503 22.0697 12.071 22.5904C12.5917 23.1111 13.4298 23.1111 15.1059 23.1111H16.8837C18.5598 23.1111 19.3978 23.1111 19.9185 22.5904C20.4392 22.0697 20.4392 21.2317 20.4392 19.5556V17.7778C20.4392 16.1017 20.4392 15.2636 19.9185 14.7429C19.3978 14.2222 18.5598 14.2222 16.8837 14.2222H15.1059C13.4298 14.2222 12.5917 14.2222 12.071 14.7429Z"
        fill="#FF7F00"
      />
      <path fillRule="evenodd" clipRule="evenodd" d="M19.5556 20.8889H12.4444V20H19.5556V20.8889Z" fill="#FF7F00" />
      <path fillRule="evenodd" clipRule="evenodd" d="M16.4444 17.3333V20H15.5556V17.3333H16.4444Z" fill="#FF7F00" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.5556 17.3333H12.4444V16.4444H19.5556V17.3333Z"
        fill="#FF7F00"
      />
      <path d="M8.53333 32H5.86667V29.3333H8.53333V32Z" fill="#FCD117" />
      <path d="M14.4 32H11.7333V29.3333H14.4V32Z" fill="#FCD117" />
      <path d="M20.2667 32H17.6V29.3333H20.2667V32Z" fill="#FCD117" />
      <path d="M26.1333 32H23.4667V29.3333H26.1333V32Z" fill="#FCD117" />
      <path d="M8.53333 2.66667L5.86667 2.66667V8.47643e-08L8.53333 3.17891e-07V2.66667Z" fill="#FCD117" />
      <path d="M14.4 2.66667L11.7333 2.66667V8.47643e-08L14.4 3.17891e-07V2.66667Z" fill="#FCD117" />
      <path d="M20.2667 2.66667L17.6 2.66667V8.47643e-08L20.2667 3.17891e-07V2.66667Z" fill="#FCD117" />
      <path d="M26.1333 2.66667L23.4667 2.66667V8.47643e-08L26.1333 3.17891e-07V2.66667Z" fill="#FCD117" />
    </svg>
  );
}
