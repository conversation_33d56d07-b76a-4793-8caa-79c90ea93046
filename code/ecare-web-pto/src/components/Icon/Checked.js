import React from 'react';

// 带盾的勾
export function CheckedShield(props) {
  return (
    <svg width="1em" height="1em" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M9 2.24997L9.22125 1.73247C9.15132 1.70256 9.07606 1.68713 9 1.68713C8.92394 1.68713 8.84868 1.70256 8.77875 1.73247L9 2.24997ZM9 15.75L8.721 16.2382C8.80597 16.2868 8.90214 16.3123 9 16.3123C9.09786 16.3123 9.19403 16.2868 9.279 16.2382L9 15.75ZM6.018 14.0452L5.73825 14.5342L6.01725 14.0452H6.018ZM8.778 1.73322L3.984 3.78747L4.425 4.82247L9.2205 2.76747L8.77875 1.73247L8.778 1.73322ZM3.1875 4.99422V10.1392H4.3125V4.99422H3.1875ZM5.73825 14.5342L8.721 16.2382L9.279 15.2617L6.29625 13.557L5.73825 14.5342ZM9.279 16.2382L12.2618 14.5342L11.7037 13.557L8.721 15.2617L9.279 16.2382ZM14.8125 10.1385V4.99497H13.6875V10.14H14.8125V10.1385ZM14.0175 3.78822L9.22125 1.73322L8.77875 2.76672L13.5743 4.82247L14.0167 3.78822H14.0175ZM14.8125 4.99497C14.8125 4.46997 14.4997 3.99522 14.0175 3.78822L13.5743 4.82247C13.6079 4.837 13.6366 4.86108 13.6567 4.89174C13.6769 4.9224 13.6876 4.9583 13.6875 4.99497H14.8125ZM12.2618 14.5342C13.0366 14.0915 13.6806 13.4518 14.1285 12.6799C14.5765 11.9081 14.8124 11.0316 14.8125 10.1392H13.6875C13.6874 10.8332 13.5038 11.5148 13.1554 12.115C12.8071 12.7152 12.3063 13.2126 11.7037 13.557L12.2618 14.5342ZM3.1875 10.1392C3.18758 11.0316 3.42355 11.9081 3.87149 12.6799C4.31944 13.4518 4.96344 14.0915 5.73825 14.5342L6.29625 13.557C5.69363 13.2126 5.19275 12.715 4.84438 12.1147C4.49601 11.5143 4.31252 10.8326 4.3125 10.1385H3.1875V10.1392ZM3.9825 3.78747C3.74655 3.8887 3.54548 4.05691 3.40417 4.27127C3.26287 4.48563 3.18753 4.73748 3.1875 4.99422H4.3125C4.3125 4.91922 4.3575 4.85097 4.4265 4.82097L3.9825 3.78897V3.78747Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.6477 7.10225C11.8674 7.32192 11.8674 7.67808 11.6477 7.89775L8.64775 10.8977C8.42808 11.1174 8.07192 11.1174 7.85225 10.8977L6.35225 9.39775C6.13258 9.17808 6.13258 8.82192 6.35225 8.60225C6.57192 8.38258 6.92808 8.38258 7.14775 8.60225L8.25 9.7045L10.8523 7.10225C11.0719 6.88258 11.4281 6.88258 11.6477 7.10225Z"
        fill="currentColor"
      />
    </svg>
  );
}
