import React from 'react';

export default function EmojiHappy({ active = false, transition, ...props }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 32 32" {...props}>
      <circle cx="16" cy="16" r="16" fill={active ? '#FCD117' : 'transparent'} style={{ transition }} />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.9844 29C23.1571 29 28.9717 23.1797 28.9717 16C28.9717 8.8203 23.1571 3 15.9844 3C8.81169 3 2.99707 8.8203 2.99707 16C2.99707 23.1797 8.81169 29 15.9844 29ZM15.9844 32C24.8123 32 31.9688 24.8366 31.9688 16C31.9688 7.16344 24.8123 0 15.9844 0C7.15646 0 0 7.16344 0 16C0 24.8366 7.15646 32 15.9844 32Z"
        fill={active ? '#AF9213' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.23342 14.7069C6.34459 14.6413 6.4624 14.5913 6.5837 14.5569C6.70481 14.5223 6.82792 14.5037 6.95033 14.5003C7.17607 14.4936 7.39655 14.5385 7.59702 14.627C7.68102 14.6639 7.76179 14.7086 7.83813 14.7606C7.90374 14.8053 7.96608 14.8554 8.02442 14.9107L8.0292 14.9146C8.0539 14.9344 8.11388 14.9802 8.21523 15.0443C8.41742 15.1721 8.78863 15.3755 9.37885 15.5903C10.5597 16.0201 12.6295 16.5 15.9848 16.5C19.34 16.5 21.4098 16.0201 22.5907 15.5903C23.1809 15.3755 23.5521 15.1721 23.7543 15.0443C23.8557 14.9802 23.9157 14.9344 23.9404 14.9146L23.9452 14.9107C24.0647 14.7974 24.201 14.7058 24.3477 14.6381C24.5556 14.5419 24.7861 14.4929 25.0222 14.5003C25.1436 14.504 25.2657 14.5226 25.3859 14.5569C25.5079 14.5915 25.6265 14.6419 25.7383 14.7082C25.8166 14.7544 25.8897 14.8072 25.9571 14.8657C26.1277 15.0133 26.2597 15.1954 26.3477 15.3959C26.4363 15.5973 26.4813 15.819 26.4742 16.0458C26.4705 16.168 26.4519 16.2909 26.4172 16.4117C26.4129 16.427 26.4083 16.4422 26.4035 16.4573C26.3975 16.4773 26.3895 16.5032 26.3795 16.5345C26.3583 16.6011 26.3279 16.6926 26.2877 16.8053C26.2074 17.0304 26.087 17.3422 25.9199 17.712C25.5873 18.4479 25.0606 19.4338 24.2812 20.4267C22.7078 22.4311 20.0624 24.5 15.9848 24.5C11.9071 24.5 9.26174 22.4311 7.6884 20.4267C6.90897 19.4338 6.38226 18.4479 6.0497 17.712C5.88258 17.3422 5.76216 17.0304 5.68184 16.8053C5.64162 16.6926 5.6113 16.6011 5.59007 16.5345C5.58013 16.5033 5.57217 16.4776 5.56611 16.4575C5.56109 16.4418 5.55634 16.426 5.55186 16.4102C5.51731 16.289 5.49875 16.1659 5.49529 16.0434C5.49105 15.9001 5.50755 15.7589 5.54271 15.6236C5.58521 15.4589 5.65611 15.2999 5.75542 15.1545C5.8275 15.0485 5.9137 14.9511 6.01296 14.8653C6.08085 14.8064 6.15452 14.7533 6.23342 14.7069ZM15.9844 23C18.1914 23 19.9805 22.3284 19.9805 21.5C19.9805 20.6716 18.1914 20 15.9844 20C13.7774 20 11.9883 20.6716 11.9883 21.5C11.9883 22.3284 13.7774 23 15.9844 23Z"
        fill={active ? '#AF9213' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M23.0833 11.4478C23.3303 11.9413 23.93 12.1413 24.4233 11.8945C24.9168 11.6475 25.1169 11.0468 24.8702 10.5528L23.9766 11C24.8702 10.5528 24.8703 10.5532 24.8702 10.5528L24.8686 10.5497L24.8666 10.5457L24.8614 10.5357L24.8464 10.507C24.8343 10.4842 24.8179 10.4542 24.7972 10.418C24.7559 10.3456 24.6971 10.2478 24.6206 10.1328C24.4686 9.90468 24.2407 9.60013 23.9338 9.29291C23.3254 8.68388 22.337 8 20.9795 8C20.3052 8 19.733 8.17161 19.2702 8.48044C18.8136 8.7851 18.5217 9.18417 18.3376 9.55281C18.1554 9.91754 18.0693 10.2702 18.0272 10.5232C18.0058 10.652 17.9947 10.7616 17.9889 10.8429C17.986 10.8837 17.9844 10.9179 17.9835 10.9443L17.9826 10.9782L17.9825 10.9907L17.9824 10.9958L17.9824 10.998C17.9824 10.9985 17.9824 11.0001 18.9815 11L17.9824 11.0001C17.9825 11.5524 18.4298 12 18.9815 12C19.5298 12 19.9749 11.5579 19.9804 11.0104C19.9805 11.0076 19.9805 11.0047 19.9805 11.0019C19.9805 11.0018 19.9805 11.002 19.9805 11.0019L19.9805 11.0064L19.9804 11.0104C19.9804 11.0112 19.9804 11.0103 19.9804 11.0104C19.9805 11.0072 19.9809 10.9986 19.9818 10.9852C19.9839 10.9571 19.9883 10.9105 19.9981 10.8518C20.0184 10.7298 20.0572 10.5825 20.1247 10.4472C20.1903 10.3158 20.2731 10.2149 20.3785 10.1446C20.4776 10.0784 20.6548 10 20.9795 10C21.6202 10 22.1304 10.3161 22.5209 10.7071C22.7135 10.8999 22.8602 11.0953 22.958 11.2422C23.0064 11.3147 23.0412 11.3731 23.0623 11.4102C23.0729 11.4286 23.0799 11.4413 23.0833 11.4478C23.0851 11.4511 23.0859 11.453 23.0859 11.4529L23.0833 11.4478Z"
        fill={active ? '#AF9213' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.88544 11.4478C8.63851 11.9413 8.03876 12.1413 7.54547 11.8945C7.05196 11.6475 6.8519 11.0468 7.09861 10.5528L7.9922 11C7.09861 10.5528 7.09843 10.5532 7.09861 10.5528L7.1002 10.5497L7.10219 10.5457L7.10734 10.5357L7.12238 10.507C7.13451 10.4842 7.15091 10.4542 7.1716 10.418C7.21288 10.3456 7.27169 10.2478 7.34823 10.1328C7.50017 9.90468 7.72807 9.60013 8.03497 9.29291C8.64338 8.68388 9.63177 8 10.9893 8C11.6636 8 12.2358 8.17161 12.6986 8.48046C13.1552 8.78513 13.447 9.18421 13.6311 9.55287C13.8133 9.91761 13.8994 10.2702 13.9414 10.5232C13.9629 10.6521 13.974 10.7617 13.9798 10.843C13.9827 10.8838 13.9843 10.9179 13.9852 10.9444L13.986 10.9783L13.9861 10.9908L13.9862 10.9959L13.9862 10.9981C13.9862 10.9986 13.9862 11.0002 12.9872 11L13.9862 11.0002C13.9861 11.5524 13.5388 12.0001 12.987 12C12.4388 11.9999 11.9937 11.5578 11.9882 11.0103C11.9882 11.0072 11.9881 11.0041 11.9881 11.0009L11.9881 11.0034L11.9882 11.0063L11.9882 11.0103C11.9882 11.0102 11.9882 11.0105 11.9882 11.0103C11.9881 11.0071 11.9877 10.9985 11.9868 10.9852C11.9848 10.9571 11.9803 10.9104 11.9705 10.8518C11.9502 10.7298 11.9115 10.5824 11.8439 10.4471C11.7784 10.3158 11.6956 10.2149 11.5903 10.1445C11.4911 10.0784 11.314 10 10.9893 10C10.3486 10 9.83842 10.3161 9.44784 10.7071C9.25525 10.8999 9.10854 11.0953 9.01075 11.2422C8.96242 11.3147 8.92758 11.3731 8.90644 11.4102C8.89591 11.4286 8.88892 11.4413 8.88544 11.4478C8.88371 11.4511 8.88285 11.453 8.8829 11.4529L8.88544 11.4478Z"
        fill={active ? '#AF9213' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        d="M10.5 18.5H19.5L21.5 21.5L17.5 24H15.5L10.5 22V18.5Z"
        fill={active ? '#CE1126' : 'transparent'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.2339 14.7069C6.34507 14.6413 6.46288 14.5913 6.58418 14.5569C6.70529 14.5223 6.8284 14.5037 6.95081 14.5003C7.17655 14.4936 7.39703 14.5385 7.59749 14.627C7.6815 14.6639 7.76226 14.7086 7.83861 14.7606C7.90422 14.8053 7.96656 14.8554 8.0249 14.9107L8.02968 14.9146C8.05438 14.9344 8.11436 14.9802 8.21571 15.0443C8.4179 15.1721 8.78911 15.3755 9.37932 15.5903C10.5602 16.0201 12.63 16.5 15.9853 16.5C19.3405 16.5 21.4103 16.0201 22.5912 15.5903C23.1814 15.3755 23.5526 15.1721 23.7548 15.0443C23.8562 14.9802 23.9161 14.9344 23.9408 14.9146L23.9457 14.9107C24.0652 14.7974 24.2015 14.7058 24.3482 14.6381C24.5561 14.5419 24.7866 14.4929 25.0227 14.5003C25.1441 14.504 25.2662 14.5226 25.3863 14.5569C25.5084 14.5915 25.627 14.642 25.7388 14.7082C25.8171 14.7544 25.8901 14.8072 25.9575 14.8657C26.1282 15.0133 26.2602 15.1954 26.3482 15.3959C26.4368 15.5973 26.4818 15.819 26.4747 16.0458C26.471 16.168 26.4523 16.2909 26.4177 16.4117C26.4134 16.427 26.4088 16.4422 26.404 16.4573C26.3979 16.4773 26.39 16.5032 26.38 16.5345C26.3587 16.6011 26.3284 16.6926 26.2882 16.8053C26.2079 17.0304 26.0875 17.3422 25.9203 17.712C25.5878 18.4479 25.0611 19.4338 24.2816 20.4267C22.7083 22.4311 20.0629 24.5 15.9853 24.5C11.9076 24.5 9.26222 22.4311 7.68888 20.4267C6.90945 19.4338 6.38274 18.4479 6.05017 17.712C5.88306 17.3422 5.76264 17.0304 5.68232 16.8053C5.6421 16.6926 5.61178 16.6011 5.59055 16.5345C5.58061 16.5033 5.57265 16.4776 5.56659 16.4575C5.56157 16.4418 5.55682 16.426 5.55234 16.4102C5.51779 16.289 5.49923 16.1659 5.49577 16.0434C5.49152 15.9001 5.50803 15.7589 5.54319 15.6236C5.58569 15.4589 5.65659 15.2999 5.7559 15.1545C5.82798 15.0485 5.91418 14.9511 6.01344 14.8653C6.08133 14.8064 6.155 14.7533 6.2339 14.7069ZM15.9849 23C18.1919 23 19.981 22.3284 19.981 21.5C19.981 20.6716 18.1919 20 15.9849 20C13.7779 20 11.9888 20.6716 11.9888 21.5C11.9888 22.3284 13.7779 23 15.9849 23Z"
        fill={active ? '#5E0E0E' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M23.0836 11.4478C23.3305 11.9413 23.9302 12.1413 24.4235 11.8945C24.917 11.6475 25.1171 11.0468 24.8704 10.5528L23.9768 11C24.8704 10.5528 24.8704 10.5528 24.8704 10.5528L24.8688 10.5497L24.8668 10.5457L24.8617 10.5357L24.8466 10.507C24.8345 10.4842 24.8181 10.4542 24.7974 10.418C24.7561 10.3456 24.6973 10.2478 24.6208 10.1328C24.4688 9.90468 24.2409 9.60013 23.934 9.29291C23.3256 8.68388 22.3372 8 20.9797 8C20.3054 8 19.7332 8.17161 19.2704 8.48044C18.8138 8.7851 18.5219 9.18417 18.3378 9.55281C18.1556 9.91754 18.0695 10.2702 18.0274 10.5232C18.006 10.652 17.9949 10.7616 17.9891 10.8429C17.9862 10.8837 17.9846 10.9179 17.9837 10.9443L17.9828 10.9782L17.9827 10.9907L17.9827 10.9958L17.9827 10.998C17.9827 10.998 17.9827 11.0001 18.9817 11L17.9827 11.0001C17.9827 11.5524 18.43 12 18.9818 12C19.53 12 19.9751 11.5579 19.9806 11.0104C19.9807 11.0076 19.9807 11.0047 19.9807 11.0019L19.9807 11.0064L19.9806 11.0104C19.9808 11.0072 19.9811 10.9986 19.9821 10.9852C19.9841 10.9571 19.9886 10.9105 19.9983 10.8518C20.0186 10.7298 20.0574 10.5825 20.1249 10.4472C20.1905 10.3158 20.2733 10.2149 20.3787 10.1446C20.4778 10.0784 20.655 10 20.9797 10C21.6204 10 22.1306 10.3161 22.5212 10.7071C22.7137 10.8999 22.8605 11.0953 22.9582 11.2422C23.0066 11.3147 23.0414 11.3731 23.0626 11.4102C23.0731 11.4286 23.0801 11.4413 23.0836 11.4478C23.0853 11.4511 23.0861 11.4529 23.0861 11.4529L23.0836 11.4478Z"
        fill={active ? '#392E00' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.88565 11.4478C8.63872 11.9413 8.03897 12.1413 7.54569 11.8945C7.05217 11.6475 6.85211 11.0468 7.09882 10.5528L7.99241 11C7.09882 10.5528 7.09882 10.5528 7.09882 10.5528L7.10042 10.5497L7.1024 10.5457L7.10755 10.5357L7.12259 10.507C7.13473 10.4842 7.15112 10.4542 7.17181 10.418C7.2131 10.3456 7.2719 10.2478 7.34844 10.1328C7.50038 9.90468 7.72828 9.60013 8.03519 9.29291C8.64359 8.68388 9.63198 8 10.9895 8C11.6638 8 12.236 8.17161 12.6988 8.48046C13.1554 8.78513 13.4472 9.18421 13.6313 9.55287C13.8135 9.91761 13.8996 10.2702 13.9417 10.5232C13.9631 10.6521 13.9742 10.7617 13.98 10.843C13.9829 10.8838 13.9845 10.9179 13.9854 10.9444L13.9862 10.9783L13.9864 10.9908L13.9864 10.9959L13.9864 10.9981C13.9864 10.9981 13.9864 11.0002 12.9874 11L13.9864 11.0002C13.9863 11.5524 13.539 12.0001 12.9872 12C12.439 11.9999 11.9939 11.5578 11.9884 11.0103C11.9884 11.0072 11.9884 11.0041 11.9883 11.0009L11.9884 11.0034L11.9884 11.0063L11.9884 11.0103C11.9883 11.0071 11.9879 10.9985 11.987 10.9852C11.985 10.9571 11.9805 10.9104 11.9707 10.8518C11.9504 10.7298 11.9117 10.5824 11.8442 10.4471C11.7786 10.3158 11.6959 10.2149 11.5905 10.1445C11.4913 10.0784 11.3142 10 10.9895 10C10.3488 10 9.83863 10.3161 9.44805 10.7071C9.25546 10.8999 9.10875 11.0953 9.01097 11.2422C8.96263 11.3147 8.9278 11.3731 8.90665 11.4102C8.89612 11.4286 8.88913 11.4413 8.88565 11.4478C8.88392 11.4511 8.88312 11.4529 8.88312 11.4529L8.88565 11.4478Z"
        fill={active ? '#392E00' : '#C7C7C7'}
        style={{ transition }}
      />
    </svg>
  );
}
