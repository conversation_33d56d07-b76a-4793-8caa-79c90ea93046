import React from 'react';

export default function MobilePhone(props) {
  return (
    <svg width="1em" height="1em" viewBox="0 0 24 25" xmlns="http://www.w3.org/2000/svg" {...props}>
      <rect x="6" y="2.5" width="12" height="21" fill="white" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.5 8.5V16.5C17.5 18.4563 17.5 19.5 17.5 20.5C17.5 21.5 17.5 22 17.5 22C17.4362 22.0058 16.9809 22.0473 16.176 22.12C15.5 22.1821 13.0092 22.4089 13.0092 22.4089C12.2579 22.4774 12.6791 22.439 12.0092 22.5C11.6289 22.4646 11.5662 22.4587 11.0092 22.4068C9.45275 22.2618 8.74545 22.1968 8 22.1274C7.19589 22.0525 6.94174 22.0288 6.94174 22.0288L6.63336 21.9992C6.60406 21.1073 6.5 18.4563 6.5 16.5V8.5C6.5 6.54371 6.50531 5.32798 6.62415 4.44403C6.73142 3.64621 6.88982 3.48861 6.93693 3.44174L6.93934 3.43934L6.94174 3.43693C6.98861 3.38982 7.14621 3.23142 7.94403 3.12415C8.82798 3.00531 10.0437 3 12 3C13.9563 3 15.172 3.00531 16.056 3.12415C16.8538 3.23142 17.0114 3.38982 17.0583 3.43693L17.0607 3.43934L17.0631 3.44174C17.1102 3.48861 17.2686 3.64621 17.3758 4.44403C17.4947 5.32798 17.5 6.54371 17.5 8.5ZM4 8.5C4 4.72876 4 2.84315 5.17157 1.67157C6.34315 0.5 8.22876 0.5 12 0.5C15.7712 0.5 17.6569 0.5 18.8284 1.67157C20 2.84315 20 4.72876 20 8.5V16.5C20 20.2712 20 22.1569 18.8284 23.3284C17.6569 24.5 15.7712 24.5 12 24.5C8.22876 24.5 6.34315 24.5 5.17157 23.3284C4 22.1569 4 20.2712 4 16.5V8.5Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.5 18.5H6.51841L6.63336 21.9992L12.0092 22.5L17.5 22V18.5ZM12.0092 22.5C11.4569 22.5 11.0092 22.0523 11.0092 21.5C11.0092 20.9477 11.4569 20.5 12.0092 20.5C12.5615 20.5 13.0092 20.9477 13.0092 21.5C13.0092 22.0523 12.5615 22.5 12.0092 22.5Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15 3H9C9 3.94281 9 3.91421 9.29289 4.20711C9.58579 4.5 10.0572 4.5 11 4.5H13C13.9428 4.5 14.4142 4.5 14.7071 4.20711C15 3.91421 15 3.94281 15 3Z"
        fill="currentColor"
      />
    </svg>
  );
}
