import React from 'react';

export default function GrabPay(props) {
  return (
    <svg width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.01655 9.49913C2.34126 9.49913 1.04526 8.1821 1.04526 6.46471L1.04573 6.46447C1.04573 4.78917 2.39433 3.46161 4.11173 3.46161C5.12313 3.46161 5.96592 3.79882 6.46121 4.14656V4.73655L6.41913 4.76812C5.75545 4.30441 5.03893 4.08318 4.13278 4.08318C2.74184 4.08318 1.73044 5.05248 1.73044 6.46447C1.73044 7.87646 2.66817 8.87734 4.01679 8.87734C5.23888 8.87734 5.80784 8.15029 5.79732 7.52871H4.09044V6.89663H6.43994V7.3075C6.43994 8.45594 5.69185 9.49913 4.01655 9.49913ZM4.01655 10.4369C1.75125 10.4369 0.0654297 8.74053 0.0654297 6.46471C0.0654297 4.26255 1.82491 2.52411 4.11126 2.52411C5.07004 2.52411 5.86023 2.74533 6.46074 3.17725V3.79882C5.83918 3.3669 5.01743 3.14568 4.11126 3.14568C2.20422 3.14568 0.729329 4.55743 0.729329 6.46447C0.729329 8.37151 2.13055 9.81507 4.01655 9.81507C5.73395 9.81507 6.75586 8.79314 6.75586 7.14941V6.58046H4.09021V5.96942H7.3461V7.04419C7.3461 9.14088 6.08168 10.4369 4.01655 10.4369ZM8.68418 10.2577V7.00209H8.68373C8.68373 5.90627 9.4318 5.13713 10.4961 5.13713C10.8122 5.13713 11.1389 5.23185 11.3601 5.37941C11.2549 5.5375 11.1705 5.70609 11.0863 5.90627C10.8861 5.82186 10.6965 5.76924 10.4858 5.76924C9.78002 5.76924 9.30576 6.28557 9.30576 7.01261V10.2577H8.68418ZM8.38908 10.2577H7.7675V7.01285C7.7675 5.3586 8.821 4.24174 10.4752 4.24174C11.0442 4.24174 11.5711 4.39982 11.9609 4.67366C11.7923 4.81046 11.666 4.95802 11.55 5.09505C11.3604 4.97928 10.9282 4.84225 10.5068 4.84225C9.25314 4.84225 8.38908 5.71661 8.38908 7.00209V10.2577ZM17.5136 7.52895V6.61227L17.5139 6.61203C17.5452 6.56838 17.5768 6.52433 17.6085 6.47997C18.3661 5.42128 19.2481 4.18864 20.9065 4.18864C22.6345 4.18864 23.8989 5.54755 23.8989 7.30726C23.8989 9.06697 22.645 10.4366 20.9382 10.4366C19.6106 10.4366 18.5886 9.64646 18.2409 8.64558L18.6834 8.04507C18.9046 9.06697 19.8529 9.81507 20.9486 9.81507C22.2974 9.81507 23.277 8.76158 23.277 7.3075C23.277 5.85341 22.2759 4.82098 20.9063 4.82098C19.8948 4.82098 19.2416 5.32679 18.4935 6.24349C18.2404 6.55093 17.9717 6.9126 17.6675 7.32204C17.6172 7.38971 17.566 7.4587 17.5136 7.52895ZM19.4207 7.07576H19.4417L19.442 7.07529V7.17023C19.442 8.0764 20.0636 8.88762 20.9486 8.88762C21.7914 8.88762 22.3499 8.18163 22.3499 7.29674C22.3499 6.41185 21.781 5.74795 20.8959 5.74795C20.0994 5.74795 19.6784 6.25984 19.0955 6.96867C19.0305 7.04765 18.9636 7.12911 18.8938 7.21256C18.4829 7.70785 17.9245 8.42436 17.5136 9.03542V8.02402C17.8345 7.57333 18.1331 7.18923 18.3614 6.89558C18.4481 6.78408 18.5247 6.68562 18.5884 6.60151C19.0624 5.96942 19.7054 5.11587 20.9063 5.11587C22.1073 5.11587 22.9819 6.04307 22.9819 7.29697C22.9819 8.55088 22.1073 9.50966 20.9484 9.50966C19.7896 9.50966 19.0201 8.49825 18.9359 7.73966L19.4207 7.07576ZM18.8199 4.59975V1.9762H19.4415V4.18888C19.2483 4.26239 19.0632 4.40832 18.893 4.54238C18.8683 4.56184 18.844 4.58106 18.8199 4.59975ZM18.5143 4.83151C18.2931 5.05272 18.0824 5.31627 17.8927 5.55853V1.9762H18.5143V4.83151ZM15.3536 8.55088L15.3641 8.5614V8.56164V9.1832C15.1534 9.33076 14.6897 9.49937 14.2367 9.50989C13.0357 9.54147 12.1295 8.62476 12.1295 7.31826C12.1295 6.01173 13.0357 5.1161 14.2367 5.1161C15.4378 5.1161 16.2913 6.01173 16.2913 7.23383V10.258H15.6697V7.26564C15.6697 6.39103 15.1218 5.7379 14.2262 5.7379C13.3937 5.7379 12.7721 6.38053 12.7721 7.31826C12.7721 8.25599 13.4044 8.8881 14.2472 8.8881C14.6581 8.8881 15.0903 8.76158 15.3536 8.55088ZM14.2367 10.4369C12.4877 10.4369 11.1918 9.14088 11.1918 7.3075L11.1915 7.30773C11.1915 5.5375 12.4875 4.17835 14.2365 4.17835C15.9854 4.17835 17.2183 5.46383 17.2183 7.2128V10.258H16.5862V7.22354C16.5862 5.8221 15.6066 4.82122 14.2367 4.82122C12.8668 4.82122 11.8344 5.81157 11.8344 7.30773C11.8344 8.8039 12.8037 9.8153 14.2367 9.8153C14.711 9.8153 15.1744 9.63617 15.3641 9.47808V10.0997C15.0795 10.3209 14.6265 10.4369 14.2367 10.4369ZM5.83707 17.2204C7.50816 17.2204 8.55533 16.2438 8.55533 14.6936C8.55533 13.1633 7.53855 12.2571 5.83707 12.2571H3.44105V19.5055H4.61895V17.2204H5.83707ZM5.89741 13.3445C6.79328 13.3445 7.36739 13.848 7.36739 14.6936C7.36739 15.5392 6.79328 16.1332 5.89741 16.1332H4.61895V13.3445H5.89741ZM14.6347 16.6264C14.6347 14.8849 13.5375 13.7671 11.8058 13.7671L11.8061 13.7674C10.1149 13.7674 8.9772 14.9656 8.9772 16.7272C8.9772 18.4888 10.0644 19.6768 11.655 19.6566C12.5207 19.6466 13.1044 19.2841 13.4571 18.5594H13.5073V19.5056H14.6347V16.6264ZM13.4769 16.6666C13.487 17.8244 12.7621 18.5391 11.8058 18.5491C10.8092 18.5594 10.1448 17.8144 10.1448 16.697C10.1448 15.5797 10.779 14.8648 11.8058 14.8648C12.8327 14.8648 13.4669 15.63 13.4769 16.6666ZM17.7945 20.774C18.8817 20.774 19.4457 20.2205 19.4254 19.1934L19.4153 18.7203H19.365C19.0631 19.3246 18.6099 19.6567 17.7744 19.6567C16.405 19.6567 15.5294 18.7404 15.5294 17.2709V13.9488H16.6872V17.1098C16.6872 18.0461 17.1402 18.5494 17.9858 18.5494C18.8314 18.5494 19.365 18.0057 19.365 17.0698V13.9491H20.5227V19.2745C20.5227 20.9052 19.5161 21.8717 17.8146 21.8717C17.0899 21.8717 16.4859 21.6905 15.9123 21.2978V20.2006L15.9424 20.1805C16.5364 20.5832 17.1502 20.7745 17.7945 20.7745V20.774Z"
        fill="#00B14F"
      />
    </svg>
  );
}
