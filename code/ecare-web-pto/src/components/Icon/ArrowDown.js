import React from 'react';

export default function ArrowDown(props) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 12 12" {...props}>
      <path
        d="M1.74581 6.66365C1.51801 6.44596 1.14866 6.44596 0.920855 6.66365C0.693049 6.88134 0.693049 7.23428 0.920855 7.45197L5.45003 11.78C5.75377 12.0702 6.24623 12.0702 6.54997 11.78L11.0791 7.45197C11.307 7.23428 11.307 6.88134 11.0791 6.66365C10.8513 6.44596 10.482 6.44596 10.2542 6.66365L6.58333 10.1715L6.58333 0.557424C6.58333 0.249568 6.32217 -4.66635e-07 6 -4.94794e-07C5.67784 -5.22953e-07 5.41667 0.249567 5.41667 0.557424L5.41667 10.1715L1.74581 6.66365Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function ArrowDownCaret(props) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 16 17" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.281874 3.67612C0.61172 3.40504 1.09932 3.45213 1.37096 3.7813L7.99488 11.8083L14.6188 3.7813C14.8904 3.45213 15.378 3.40504 15.7079 3.67612C16.0377 3.94721 16.0849 4.43381 15.8133 4.76299L8.99028 13.0312C8.47447 13.6563 7.51529 13.6563 6.99948 13.0312L0.176479 4.76299C-0.0951594 4.43381 -0.0479723 3.94721 0.281874 3.67612Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function ArrowDownFill(props) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 12 13" {...props}>
      <path
        d="M5.69693 9.985L0.959122 3.30822C0.779491 3.05507 0.689675 2.9285 0.740372 2.82942C0.791069 2.73033 0.945594 2.73043 1.25464 2.73063L10.7303 2.73685C11.039 2.73705 11.1934 2.73715 11.244 2.83623C11.2946 2.93531 11.2048 3.06173 11.0252 3.31456L6.28741 9.98513C6.15277 10.1747 6.08545 10.2695 5.99211 10.2695C5.89876 10.2694 5.83148 10.1746 5.69693 9.985Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.95987 3.46165L5.99235 9.14444L10.0248 3.46693L1.95987 3.46165ZM0.117654 3.34454C0.0500268 3.21186 -0.096899 2.8703 0.0951651 2.49492C0.287229 2.11954 0.648791 2.04159 0.795368 2.01997C0.933755 1.99955 1.09925 1.99983 1.22611 2.00005C1.23602 2.00006 1.2457 2.00008 1.25512 2.00008L10.7307 2.0063C10.7401 2.0063 10.7498 2.0063 10.7597 2.00629C10.8865 2.00624 11.0518 2.00618 11.1902 2.02677C11.3367 2.0486 11.6979 2.12701 11.8895 2.5023C12.0811 2.87759 11.9342 3.21883 11.8665 3.35151C11.8026 3.47669 11.7063 3.61203 11.6325 3.71573C11.6267 3.72383 11.6211 3.73175 11.6156 3.73945L6.87779 10.41C6.87357 10.416 6.86916 10.4222 6.86455 10.4287C6.81059 10.5049 6.73094 10.6174 6.64708 10.7051C6.5444 10.8124 6.32687 11.0001 5.99195 11C5.65702 10.9999 5.43957 10.8122 5.33695 10.7048C5.25312 10.6171 5.17351 10.5045 5.11958 10.4283C5.11498 10.4218 5.11057 10.4156 5.10636 10.4096L0.368552 3.73284C0.363082 3.72514 0.357448 3.71721 0.351677 3.7091C0.277833 3.60526 0.181503 3.46981 0.117654 3.34454Z"
        fill="currentColor"
      />
    </svg>
  );
}
