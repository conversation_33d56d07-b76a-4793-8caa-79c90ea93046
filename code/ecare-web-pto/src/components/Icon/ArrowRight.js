import React from 'react';

export default function ArrowRight({ style = {} }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 16 16" style={style}>
      <path
        d="M8.8848 13.6722C8.59455 13.976 8.59455 14.4685 8.8848 14.7722C9.17506 15.0759 9.64564 15.0759 9.93589 14.7722L15.7066 8.7333C16.0936 8.32831 16.0936 7.67169 15.7066 7.2667L9.9359 1.22781C9.64564 0.924064 9.17506 0.924064 8.88481 1.22781C8.59456 1.53155 8.59456 2.02401 8.88481 2.32775L13.5619 7.22222L0.743171 7.22222C0.332696 7.22222 -6.13394e-05 7.57044 -6.1377e-05 8C-6.14145e-05 8.42955 0.332696 8.77778 0.743171 8.77778L13.5619 8.77778L8.8848 13.6722Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function ArrowRightIOS({ style = {}, ...props }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 17 16" style={style} {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.67063 15.7179C3.39954 15.3879 3.44663 14.9 3.77581 14.6282L11.8028 8L3.77581 1.37183C3.44663 1.10002 3.39954 0.612109 3.67063 0.282052C3.94171 -0.048005 4.42832 -0.0952214 4.7575 0.17659L13.0257 7.00396C13.6508 7.5201 13.6508 8.4799 13.0257 8.99604L4.75749 15.8234C4.42832 16.0952 3.94171 16.048 3.67063 15.7179Z"
        fill="currentColor"
      />
    </svg>
  );
}
