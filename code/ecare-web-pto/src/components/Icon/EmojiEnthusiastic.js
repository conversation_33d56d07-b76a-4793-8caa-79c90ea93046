import React from 'react';

export default function EmojiEnthusiastic({ active = false, transition, ...props }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 32 32" {...props}>
      <path
        d="M32 15.9999C32 24.8364 24.8366 31.9998 16.0001 31.9998C7.16361 31.9998 0.000213612 24.8364 0.000213612 15.9999C0.000213612 7.1634 7.16361 0 16.0001 0C24.8366 0 32 7.1634 32 15.9999Z"
        fill={active ? '#FCD117' : 'transparent'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.0001 28.9998C23.1798 28.9998 29 23.1795 29 15.9999C29 8.82024 23.1798 2.99998 16.0001 2.99998C8.82045 2.99998 3.00019 8.82024 3.00019 15.9999C3.00019 23.1795 8.82045 28.9998 16.0001 28.9998ZM16.0001 31.9998C24.8366 31.9998 32 24.8364 32 15.9999C32 7.1634 24.8366 0 16.0001 0C7.16361 0 0.000213612 7.1634 0.000213612 15.9999C0.000213612 24.8364 7.16361 31.9998 16.0001 31.9998Z"
        fill={active ? '#AF9213' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        d="M16.0008 26.9998C24.4753 26.9998 25.7676 19.1008 25.9646 16.6901C25.9924 16.35 25.6644 16.0999 25.3321 16.1774C24.1012 16.4648 21.1214 16.9999 16.0008 16.9999C10.8799 16.9999 7.89934 16.4647 6.66817 16.1774C6.33583 16.0998 6.00787 16.35 6.03571 16.6901C6.23304 19.1008 7.5262 26.9998 16.0008 26.9998Z"
        fill={active ? 'white' : 'transparent'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.10612 17.2936C7.2735 18.5582 7.69866 20.4987 8.76482 22.2231C10.0108 24.2383 12.148 25.9998 16.0008 25.9998C19.8535 25.9998 21.9905 24.2383 23.2362 22.2232C24.3022 20.4988 24.7271 18.5583 24.8943 17.2937C23.3371 17.6005 20.4545 17.9999 16.0008 17.9999C11.5468 17.9999 8.66363 17.6004 7.10612 17.2936ZM5.03905 16.7717C4.94856 15.6662 5.99299 14.9929 6.89543 15.2036C8.04594 15.4721 10.9498 15.9999 16.0008 15.9999C21.0515 15.9999 23.9545 15.4721 25.1048 15.2036C26.0072 14.993 27.0517 15.6661 26.9613 16.7716C26.8578 18.0377 26.4668 20.8006 24.9374 23.2748C23.3691 25.8118 20.6226 27.9998 16.0008 27.9998C11.379 27.9998 8.6323 25.8118 7.06372 23.2749C5.534 20.8007 5.14268 18.0378 5.03905 16.7717Z"
        fill={active ? '#AF9213' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.09368 5.3918C7.39261 4.21304 8.12145 2.61396 9.9355 2.14852C11.684 1.69992 13.0996 2.31256 13.9757 3.38022C14.8141 4.40187 15.15 5.81743 14.9381 7.07518C14.8145 7.8086 14.4605 8.54793 14.0358 9.22532C13.6059 9.91081 13.0732 10.5805 12.5414 11.1809C11.4781 12.3816 10.3718 13.3575 9.97396 13.6983C9.67885 13.9511 9.28599 14.0519 8.89839 13.9743C8.37582 13.8696 6.9082 13.5541 5.36033 13.0234C4.58627 12.758 3.77518 12.4331 3.04499 12.0452C2.32343 11.662 1.63079 11.1912 1.1408 10.6152C0.300631 9.62752 -0.148083 8.241 0.0441027 6.95489C0.244955 5.61077 1.1507 4.40245 2.89922 3.95383C4.71326 3.4884 6.17989 4.52421 7.05464 5.40183C7.06096 5.40066 7.06754 5.39921 7.0743 5.39748C7.08109 5.39573 7.08757 5.39382 7.09368 5.3918Z"
        fill={active ? '#CE1126' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M24.9061 5.3918C24.6072 4.21304 23.8783 2.61396 22.0643 2.14852C20.3158 1.69992 18.9002 2.31256 18.0241 3.38022C17.1857 4.40187 16.8498 5.81743 17.0617 7.07518C17.1853 7.8086 17.5393 8.54793 17.964 9.22532C18.3938 9.91081 18.9266 10.5805 19.4584 11.1809C20.5217 12.3816 21.6279 13.3575 22.0258 13.6983C22.3209 13.9511 22.7138 14.0519 23.1014 13.9743C23.624 13.8696 25.0916 13.5541 26.6395 13.0234C27.4135 12.758 28.2246 12.4331 28.9548 12.0452C29.6764 11.662 30.369 11.1912 30.859 10.6152C31.6992 9.62752 32.1479 8.241 31.9557 6.95489C31.7548 5.61077 30.8491 4.40245 29.1006 3.95383C27.2865 3.4884 25.8199 4.52421 24.9451 5.40183C24.9388 5.40066 24.9322 5.39921 24.9255 5.39748C24.9187 5.39573 24.9122 5.39382 24.9061 5.3918Z"
        fill={active ? '#CE1126' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.77635 16.5527C5.52937 16.6762 5.42926 16.9765 5.55275 17.2235L5.77635 16.5527ZM5.55275 17.2235L5.99996 16.9999L5.55275 17.2235ZM26.2234 16.5527C26.4704 16.6762 26.5705 16.9765 26.447 17.2235L25.9998 16.9999C26.447 17.2235 26.447 17.2235 26.447 17.2235L26.4452 17.2271L26.4419 17.2335L26.4309 17.2549C26.4215 17.2727 26.4081 17.2978 26.3907 17.3293C26.356 17.3923 26.3051 17.4812 26.2382 17.5901C26.1044 17.8075 25.9052 18.106 25.6403 18.4372C25.1132 19.0961 24.3098 19.9039 23.2234 20.4471C22.551 20.7833 21.7673 21.0432 21.1643 21.2174C20.8602 21.3052 20.5967 21.3727 20.4088 21.4184C20.3148 21.4413 20.2395 21.4587 20.1872 21.4705L20.1265 21.484L20.1101 21.4876L20.1055 21.4886L19.9999 21L20.1035 21.489C19.8333 21.5462 19.5679 21.3736 19.5107 21.1035C19.4535 20.8334 19.6261 20.568 19.8962 20.5107L19.8997 20.51L19.9129 20.5071L19.9666 20.4951C20.0142 20.4844 20.0843 20.4682 20.1726 20.4467C20.3496 20.4037 20.599 20.3398 20.8868 20.2567C21.4675 20.089 22.1838 19.8489 22.7762 19.5527C23.6898 19.0959 24.3865 18.4036 24.8594 17.8125C25.0944 17.5188 25.2703 17.2548 25.3865 17.066C25.4445 16.9717 25.4874 16.8965 25.5152 16.8462C25.529 16.8211 25.5391 16.8022 25.5453 16.7903L25.5518 16.7778L25.5528 16.7759C25.6764 16.5292 25.9766 16.4292 26.2234 16.5527Z"
        fill={active ? '#AF9213' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        d="M5.77635 16.5527C5.52937 16.6762 5.42926 16.9765 5.55275 17.2235L5.55466 17.2271L5.55795 17.2335L5.56902 17.2549C5.57839 17.2727 5.59177 17.2978 5.60916 17.3293C5.64391 17.3923 5.69476 17.4812 5.76174 17.5901C5.89552 17.8075 6.09466 18.106 6.35963 18.4372C6.88673 19.0961 7.69006 19.9039 8.77645 20.4471C9.44894 20.7833 10.2326 21.0432 10.8356 21.2174C11.1397 21.3052 11.4032 21.3727 11.5911 21.4184C11.6851 21.4413 11.7604 21.4587 11.8127 21.4705L11.8734 21.484L11.8898 21.4876L11.8944 21.4886L12 21L11.8964 21.489C12.1666 21.5462 12.4319 21.3736 12.4892 21.1035C12.5464 20.8334 12.3732 20.5679 12.103 20.5106L12.1001 20.51L12.0869 20.5071L12.0332 20.4951C11.9856 20.4844 11.9155 20.4682 11.8271 20.4467C11.6502 20.4037 11.4008 20.3398 11.113 20.2567C10.5323 20.089 9.81606 19.8489 9.22365 19.5527C8.31005 19.0959 7.61328 18.4036 7.14039 17.8125C6.90536 17.5188 6.7295 17.2548 6.61328 17.066C6.55526 16.9717 6.51236 16.8965 6.48462 16.8462C6.47076 16.8211 6.4607 16.8022 6.45445 16.7903L6.44717 16.7763C6.32368 16.5293 6.02334 16.4292 5.77635 16.5527Z"
        fill={active ? '#AF9213' : '#C7C7C7'}
        style={{ transition }}
      />
    </svg>
  );
}
