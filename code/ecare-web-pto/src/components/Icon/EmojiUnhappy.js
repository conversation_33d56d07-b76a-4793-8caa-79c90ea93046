import React from 'react';

export default function EmojiUnhappy({ active = false, transition, ...props }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 32 32" {...props}>
      <circle cx="16" cy="16" r="16" fill={active ? '#FCD117' : 'transparent'} style={{ transition }} />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16 29C23.1797 29 29 23.1797 29 16C29 8.8203 23.1797 3 16 3C8.8203 3 3 8.8203 3 16C3 23.1797 8.8203 29 16 29ZM16 32C24.8366 32 32 24.8366 32 16C32 7.16344 24.8366 0 16 0C7.16344 0 0 7.16344 0 16C0 24.8366 7.16344 32 16 32Z"
        fill={active ? '#AF9213' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M22.6364 22.6249C22.9781 23.3709 23.8574 23.7048 24.6092 23.3707C25.3662 23.0343 25.7072 22.1478 25.3707 21.3908L24 22C25.3707 21.3908 25.3707 21.3908 25.3707 21.3908L25.3691 21.3871L25.3673 21.3831L25.3631 21.3739L25.3525 21.351C25.3444 21.3338 25.3343 21.3128 25.3221 21.2882C25.2977 21.2391 25.2648 21.1755 25.2227 21.0997C25.1385 20.9481 25.0168 20.7464 24.8517 20.5118C24.5218 20.0429 24.015 19.4378 23.2832 18.8391C21.7974 17.6234 19.4631 16.5 16 16.5C12.5369 16.5 10.2026 17.6234 8.71681 18.8391C7.98497 19.4378 7.47821 20.0429 7.14829 20.5118C6.98319 20.7464 6.86153 20.948 6.77731 21.0997C6.73516 21.1755 6.70225 21.2391 6.67786 21.2882C6.66566 21.3128 6.65557 21.3338 6.64751 21.351L6.63692 21.3739L6.63274 21.3831L6.63092 21.3871L6.63008 21.389C6.63008 21.389 6.62928 21.3908 8 22L6.63008 21.389C6.29362 22.146 6.63377 23.0343 7.39079 23.3707C8.14256 23.7048 9.02195 23.3709 9.36362 22.6249L9.36571 22.6203L9.36792 22.6155L9.36924 22.6125L9.37072 22.6092L8.0335 22.0149C9.37071 22.6092 9.36924 22.6125 9.36924 22.6125C9.36715 22.6172 9.36576 22.6203 9.36362 22.6249L9.36571 22.6203C9.37039 22.6109 9.38116 22.5901 9.39978 22.5566C9.43708 22.4894 9.50292 22.3786 9.60171 22.2382C9.79956 21.9571 10.1261 21.5622 10.6165 21.1609C11.5752 20.3766 13.2408 19.5 16 19.5C18.7592 19.5 20.4248 20.3766 21.3835 21.1609C21.8739 21.5622 22.2004 21.9571 22.3983 22.2382C22.4971 22.3786 22.5629 22.4894 22.6002 22.5566C22.6188 22.5901 22.6302 22.6125 22.6349 22.6219L22.6364 22.6249Z"
        fill={active ? '#AF9213' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        d="M22 11C22 12.6569 21.1046 14 20 14C18.8954 14 18 12.6569 18 11C18 9.34315 18.8954 8 20 8C21.1046 8 22 9.34315 22 11Z"
        fill={active ? '#392E00' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        d="M14 11C14 12.6569 13.1046 14 12 14C10.8954 14 10 12.6569 10 11C10 9.34315 10.8954 8 12 8C13.1046 8 14 9.34315 14 11Z"
        fill={active ? '#392E00' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M22.6361 22.6249C22.9778 23.3709 23.8572 23.7048 24.6089 23.3707C25.3659 23.0343 25.7069 22.1478 25.3704 21.3908L23.9997 22C25.3704 21.3908 25.3704 21.3908 25.3704 21.3908L25.3688 21.3871L25.367 21.3831L25.3628 21.3739L25.3522 21.351C25.3441 21.3338 25.3341 21.3128 25.3219 21.2882C25.2975 21.2391 25.2646 21.1755 25.2224 21.0997C25.1382 20.9481 25.0165 20.7464 24.8514 20.5118C24.5215 20.0429 24.0147 19.4378 23.2829 18.8391C21.7971 17.6234 19.4628 16.5 15.9997 16.5C12.5366 16.5 10.2023 17.6234 8.71652 18.8391C7.98468 19.4378 7.47793 20.0429 7.14801 20.5118C6.9829 20.7464 6.86125 20.9481 6.77702 21.0997C6.73487 21.1755 6.70197 21.2391 6.67757 21.2882C6.66537 21.3128 6.65529 21.3338 6.64723 21.351L6.63663 21.3739L6.63245 21.3831L6.63063 21.3871L6.62979 21.389C6.62979 21.389 6.629 21.3908 7.99971 22L6.62979 21.389C6.29334 22.146 6.63348 23.0343 7.39051 23.3707C8.14227 23.7048 9.02166 23.3709 9.36334 22.6249L9.36542 22.6203L9.36763 22.6155L9.36896 22.6125L9.37043 22.6092L8.03321 22.0149C9.37042 22.6092 9.36896 22.6125 9.36896 22.6125C9.36687 22.6172 9.36547 22.6203 9.36334 22.6249L9.36542 22.6203C9.37011 22.6109 9.38088 22.5901 9.39949 22.5566C9.43679 22.4894 9.50264 22.3786 9.60142 22.2382C9.79928 21.9571 10.1259 21.5622 10.6162 21.1609C11.5749 20.3766 13.2406 19.5 15.9997 19.5C18.7589 19.5 20.4245 20.3766 21.3832 21.1609C21.8736 21.5622 22.2002 21.9571 22.398 22.2382C22.4968 22.3786 22.5626 22.4894 22.5999 22.5566C22.6185 22.5901 22.6299 22.6125 22.6346 22.6219L22.6361 22.6249Z"
        fill={active ? '#5E0E0E' : '#C7C7C7'}
        style={{ transition }}
      />
    </svg>
  );
}
