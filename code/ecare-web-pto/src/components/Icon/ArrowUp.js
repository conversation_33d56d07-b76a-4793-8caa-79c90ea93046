import React from 'react';

export function ArrowUpCircleFill({ style = {} }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 15 15" style={style}>
      <path
        d="M7.5 0.5C11.366 0.5 14.5 3.63401 14.5 7.5C14.5 11.366 11.366 14.5 7.5 14.5C3.63401 14.5 0.5 11.366 0.5 7.5C0.5 3.63401 3.63401 0.5 7.5 0.5Z"
        fill="currentColor"
      />
      <path
        d="M7.5 5.35637L7.91667 5.75311L7.91667 10.0596C7.91667 10.2568 7.73012 10.4167 7.5 10.4167C7.26988 10.4167 7.08333 10.2568 7.08333 10.0596L7.08333 5.75311L7.5 5.35637Z"
        fill="white"
      />
      <path
        d="M4.72127 7.46846C4.55022 7.33654 4.53636 7.11077 4.6903 6.96419L6.98382 4.78034C7.25969 4.51766 7.74031 4.51766 8.01618 4.78034L10.3097 6.96419C10.4636 7.11077 10.4498 7.33654 10.2787 7.46846C10.1077 7.60038 9.84423 7.5885 9.69029 7.44192L7.5 5.35637L5.30971 7.44192C5.15577 7.5885 4.89231 7.60038 4.72127 7.46846Z"
        fill="white"
      />
    </svg>
  );
}

export function ArrowUpFill(props) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 12 13" {...props}>
      <path
        d="M6.28794 3.015L11.0261 9.69178C11.2058 9.94493 11.2956 10.0715 11.2449 10.1706C11.1942 10.2697 11.0397 10.2696 10.7306 10.2694L1.25419 10.2632C0.945397 10.263 0.791003 10.2629 0.740412 10.1638C0.689821 10.0647 0.779617 9.93827 0.959209 9.68544L5.69742 3.01487C5.83207 2.82531 5.89939 2.73053 5.99274 2.73055C6.0861 2.73057 6.15338 2.82538 6.28794 3.015Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.0253 9.53835L5.9925 3.85556L1.95968 9.53307L10.0253 9.53835ZM11.8677 9.65546C11.9353 9.78814 12.0823 10.1297 11.8902 10.5051C11.6981 10.8805 11.3365 10.9584 11.1899 10.98C11.0515 11.0005 10.886 11.0002 10.7591 11C10.7492 10.9999 10.7395 10.9999 10.7301 10.9999L1.25372 10.9937C1.24431 10.9937 1.23464 10.9937 1.22473 10.9937C1.09799 10.9938 0.932577 10.9938 0.794264 10.9732C0.647669 10.9514 0.286488 10.873 0.094863 10.4977C-0.0967587 10.1224 0.0501557 9.78117 0.117861 9.64849C0.18174 9.52331 0.278082 9.38797 0.351905 9.28427C0.357676 9.27617 0.363309 9.26825 0.368777 9.26055L5.10699 2.58998C5.1112 2.58405 5.11562 2.57781 5.12022 2.57131C5.17419 2.49509 5.25385 2.38258 5.33772 2.29491C5.4404 2.18757 5.65795 1.99993 5.9929 2C6.32786 2.00007 6.54532 2.18781 6.64796 2.2952C6.73179 2.38291 6.81141 2.49546 6.86534 2.57171C6.86994 2.57821 6.87435 2.58444 6.87856 2.59038L11.6168 9.26716C11.6222 9.27486 11.6279 9.28279 11.6336 9.2909C11.7075 9.39474 11.8038 9.53019 11.8677 9.65546Z"
        fill="currentColor"
      />
    </svg>
  );
}

export function ArrowUpIOS(props) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 10 8" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.85513 6.41194C8.69019 6.54748 8.44635 6.52394 8.31051 6.35935L4.99802 2.34587L1.68552 6.35935C1.54968 6.52394 1.30585 6.54748 1.1409 6.41194C0.975946 6.2764 0.95235 6.03309 1.08819 5.86851L4.50024 1.7344C4.75818 1.42187 5.23785 1.42187 5.49579 1.7344L8.90784 5.86851C9.04368 6.03309 9.02008 6.2764 8.85513 6.41194Z"
        fill="currentColor"
        stroke="currentColor"
        strokeLinecap="round"
      />
    </svg>
  );
}
