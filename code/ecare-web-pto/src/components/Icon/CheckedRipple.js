import React from 'react';

export function CheckedRipple({ bgColor = 'white', style = {} }) {
  return (
    <svg width="1em" height="1em" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg" style={style}>
      <g opacity="0.1">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M40 77.3333C60.6186 77.3333 77.3333 60.6186 77.3333 40C77.3333 19.3814 60.6186 2.66667 40 2.66667C19.3814 2.66667 2.66667 19.3814 2.66667 40C2.66667 60.6186 19.3814 77.3333 40 77.3333ZM40 80C62.0914 80 80 62.0914 80 40C80 17.9086 62.0914 0 40 0C17.9086 0 0 17.9086 0 40C0 62.0914 17.9086 80 40 80Z"
          fill={bgColor || 'white'}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M40 68C55.464 68 68 55.464 68 40C68 24.536 55.464 12 40 12C24.536 12 12 24.536 12 40C12 55.464 24.536 68 40 68ZM40 73.3333C58.4095 73.3333 73.3333 58.4095 73.3333 40C73.3333 21.5905 58.4095 6.66667 40 6.66667C21.5905 6.66667 6.66667 21.5905 6.66667 40C6.66667 58.4095 21.5905 73.3333 40 73.3333Z"
          fill={bgColor || 'white'}
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M40 54.6667C48.1002 54.6667 54.6667 48.1002 54.6667 40C54.6667 31.8998 48.1002 25.3333 40 25.3333C31.8998 25.3333 25.3333 31.8998 25.3333 40C25.3333 48.1002 31.8998 54.6667 40 54.6667ZM40 62.6667C52.5185 62.6667 62.6667 52.5185 62.6667 40C62.6667 27.4815 52.5185 17.3333 40 17.3333C27.4815 17.3333 17.3333 27.4815 17.3333 40C17.3333 52.5185 27.4815 62.6667 40 62.6667Z"
          fill={bgColor || 'white'}
        />
      </g>
      <circle cx="39.9996" cy="40" r="23.7037" fill={bgColor || 'white'} />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M48.578 33.536C48.9385 33.8384 48.9803 34.3697 48.6713 34.7226L38.5564 46.2748C38.1219 46.7711 37.3477 46.8002 36.8756 46.338L31.3631 40.9417C31.0274 40.613 31.0274 40.0801 31.3631 39.7515C31.6989 39.4228 32.2432 39.4228 32.5789 39.7515L37.6537 44.7194L47.3658 33.6272C47.6748 33.2743 48.2175 33.2335 48.578 33.536Z"
        fill="currentColor"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
