import React from 'react';

export default function ShopeePay(props) {
  return (
    <svg width="1em" height="1em" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M18.11 2.80126H6.94001C6.59973 2.80126 6.32379 2.52532 6.32379 2.18504C6.32379 2.0305 6.44908 1.9052 6.60363 1.9052H16.6138C16.7684 1.9052 16.8937 1.77991 16.8937 1.62537V0.616204C16.8937 0.254465 16.5836 -0.0292702 16.2233 0.00241868L6.08003 0.897991C5.35655 0.96088 4.80078 1.56687 4.80078 2.29327V12.0432C4.80078 12.8164 5.42773 13.4433 6.20094 13.4433H18.1096C18.8827 13.4433 19.5097 12.8164 19.5097 12.0432V4.20191C19.5097 3.4287 18.8827 2.80175 18.1096 2.80175"
        fill="#EE4D2D"
      />
      <path
        d="M14.6588 10.1267C14.5706 10.8487 14.1303 11.4269 13.4478 11.7165C13.068 11.8779 12.5591 11.9642 12.1554 11.9369C11.528 11.913 10.9381 11.7614 10.3945 11.484C10.1966 11.3831 9.90647 11.1837 9.68807 11.0013C9.63834 10.9599 9.6125 10.9228 9.66077 10.8556C9.71195 10.779 9.9138 10.487 9.94596 10.4387C9.98888 10.3734 10.0596 10.37 10.1244 10.4207C10.1332 10.4275 10.199 10.4787 10.2121 10.4885C10.7353 10.8955 11.41 11.2012 12.1603 11.2295C13.1061 11.2168 13.792 10.7956 13.9144 10.1433C14.0494 9.4247 13.4761 8.80798 12.3714 8.46428C12.0204 8.35508 11.1375 8.00359 10.9741 7.909C10.2048 7.46048 9.84601 6.87254 9.89722 6.14857C9.97523 5.14526 10.9117 4.3935 12.1003 4.38863C12.6644 4.38765 13.1933 4.51148 13.6648 4.70892C13.8383 4.78157 14.1616 4.95463 14.2766 5.03995C14.357 5.09846 14.3507 5.16427 14.319 5.21545C14.2722 5.29346 14.1323 5.5109 14.0762 5.60108C14.0353 5.66301 13.9846 5.67032 13.9119 5.62497C13.304 5.21936 12.679 5.08188 12.1164 5.07067C11.3047 5.08675 10.6924 5.5655 10.6524 6.22267C10.6417 6.81597 11.0955 7.24646 12.0594 7.57553C14.0489 8.21027 14.8021 8.95667 14.6593 10.1262"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.7998 21.6203V15.2086C4.7998 15.0756 4.90749 14.9679 5.04051 14.9679H7.20549C8.42416 14.9837 9.4158 15.9763 9.4158 17.1804C9.4158 18.3845 8.42384 19.377 7.20454 19.3929H5.63844V21.6199C5.63844 21.7529 5.53076 21.8606 5.39775 21.8606H5.04051C4.90749 21.8606 4.7998 21.7533 4.7998 21.6203ZM5.63812 18.5216H7.19536C7.5602 18.5216 7.90319 18.3804 8.16099 18.1235C8.41878 17.867 8.56067 17.5262 8.56067 17.1639C8.56067 16.4152 7.94975 15.8065 7.19916 15.8065H5.63812V18.5216Z"
        fill="#EE4D2D"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.9351 23.7827L16.574 20.9669L14.6114 17.7542C14.5524 17.658 14.6218 17.5348 14.7346 17.5348H15.5291L17.1076 20.0947L18.6319 17.5348H19.3651C19.4768 17.5348 19.5463 17.6564 19.4893 17.7527L15.7954 23.9999H15.0599C14.9483 23.9999 14.879 23.879 14.9351 23.7827Z"
        fill="#EE4D2D"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.0915 21.5873V21.2171L12.9696 21.3184C12.575 21.6468 12.0704 21.8277 11.5499 21.8277C10.3473 21.8277 9.36906 20.8646 9.36906 19.6811C9.36906 18.4976 10.3473 17.5674 11.5499 17.5674C12.0707 17.5674 12.575 17.7482 12.9696 18.077L13.0915 18.1783V17.8081C13.0915 17.6751 13.1992 17.5674 13.3323 17.5674H13.8314V21.8277H13.3323C13.1992 21.8277 13.0915 21.7203 13.0915 21.5873ZM11.5438 18.3668C10.7881 18.3668 10.1732 18.9574 10.1732 19.6836C10.1732 20.4098 10.8009 21.0328 11.5438 21.0328C12.2867 21.0328 12.9145 20.4149 12.9145 19.6836C12.9145 18.9523 12.2995 18.3668 11.5438 18.3668Z"
        fill="#EE4D2D"
      />
    </svg>
  );
}
