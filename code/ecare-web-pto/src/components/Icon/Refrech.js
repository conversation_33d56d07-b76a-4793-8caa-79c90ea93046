import React from 'react';

export default function Refresh(props) {
  return (
    <svg width="1em" height="1em" viewBox="0 0 13 12" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g clipPath="url(#clip0_11904_479)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M1.52234 2.367C2.34162 1.20035 3.81119 0 6.30667 0C8.80216 0 10.2717 1.20035 11.091 2.367C11.497 2.94519 11.7483 3.51926 11.8984 3.94648C11.9737 4.16096 12.0243 4.34078 12.0564 4.46878C12.0724 4.53284 12.0839 4.58411 12.0916 4.62046C12.0954 4.63864 12.0983 4.6531 12.1003 4.66359L12.1027 4.67631L12.1034 4.68037L12.1037 4.68182C12.1037 4.68207 12.1039 4.68287 11.5488 4.75771L12.1039 4.68287C12.151 4.95154 11.9407 5.20283 11.6342 5.24417C11.3279 5.28545 11.0414 5.10163 10.9938 4.83345L10.9936 4.83255L10.9926 4.82737C10.9916 4.82171 10.9897 4.81223 10.9869 4.79916C10.9814 4.77303 10.9724 4.73265 10.9592 4.68008C10.9328 4.57481 10.8898 4.42138 10.8247 4.23597C10.6939 3.8634 10.4772 3.37108 10.1343 2.88289C9.45588 1.91678 8.3044 0.984354 6.30667 0.984354C4.30895 0.984354 3.15747 1.91678 2.47901 2.88289C2.13618 3.37108 1.91944 3.8634 1.78863 4.23597C1.72352 4.42138 1.68051 4.57481 1.65411 4.68008C1.64092 4.73265 1.63192 4.77303 1.62642 4.79916C1.62367 4.81223 1.62179 4.82171 1.6207 4.82737L1.61971 4.83255C1.57234 5.10101 1.28563 5.28548 0.97919 5.24417C0.672605 5.20283 0.462306 4.95154 0.509473 4.68287L1.06459 4.75771C0.509473 4.68287 0.509429 4.68312 0.509473 4.68287L0.509659 4.68182L0.509918 4.68037L0.510657 4.67631L0.513056 4.66359C0.515075 4.6531 0.517952 4.63864 0.521782 4.62046C0.52944 4.58411 0.540921 4.53284 0.556988 4.46878C0.589091 4.34078 0.639686 4.16096 0.714996 3.94648C0.865003 3.51926 1.11631 2.94519 1.52234 2.367Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.9232 1.31247C12.2334 1.31247 12.4848 1.53283 12.4848 1.80465V4.59365C12.4848 4.95608 12.1496 5.24989 11.736 5.24989H9.67658C9.36639 5.24989 9.11493 5.02954 9.11493 4.75771C9.11493 4.48589 9.36639 4.26554 9.67658 4.26554H11.3615V1.80465C11.3615 1.53283 11.613 1.31247 11.9232 1.31247Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.4625 9.63273C10.6432 10.7994 9.17365 11.9997 6.67817 11.9997C4.18268 11.9997 2.71312 10.7994 1.89384 9.63273C1.4878 9.05453 1.2365 8.48046 1.08649 8.05324C1.01118 7.83876 0.960583 7.65895 0.92848 7.53095C0.912413 7.46688 0.900932 7.41562 0.893275 7.37927C0.889444 7.36109 0.886567 7.34662 0.884548 7.33614L0.88215 7.32342L0.88141 7.31935L0.881151 7.3179C0.881108 7.31766 0.880965 7.31685 1.43609 7.24201L0.880965 7.31685C0.833798 7.04819 1.0441 6.79689 1.35068 6.75556C1.65692 6.71427 1.9434 6.8981 1.99105 7.16627L1.99121 7.16717L1.99219 7.17236C1.99328 7.17801 1.99516 7.1875 1.99791 7.20056C2.00342 7.2267 2.01241 7.26707 2.0256 7.31965C2.052 7.42492 2.09501 7.57835 2.16012 7.76376C2.29094 8.13633 2.50767 8.62864 2.85051 9.11683C3.52896 10.0829 4.68044 11.0154 6.67817 11.0154C8.67589 11.0154 9.82737 10.0829 10.5058 9.11683C10.8487 8.62864 11.0654 8.13633 11.1962 7.76376C11.2613 7.57835 11.3043 7.42492 11.3307 7.31965C11.3439 7.26707 11.3529 7.2267 11.3584 7.20056C11.3612 7.1875 11.3631 7.17801 11.3641 7.17236L11.3651 7.16717C11.4125 6.89872 11.6992 6.71424 12.0057 6.75556C12.3122 6.79689 12.5225 7.04819 12.4754 7.31685L11.9202 7.24201C12.4754 7.31685 12.4754 7.3166 12.4754 7.31685L12.4752 7.31791L12.4749 7.31935L12.4742 7.32342L12.4718 7.33614C12.4698 7.34662 12.4669 7.36109 12.4631 7.37927C12.4554 7.41562 12.4439 7.46688 12.4279 7.53095C12.3958 7.65895 12.3452 7.83876 12.2698 8.05324C12.1198 8.48046 11.8685 9.05454 11.4625 9.63273Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M1.06165 10.6873C0.751459 10.6873 0.5 10.4669 0.5 10.1951L0.5 7.40607C0.5 7.04364 0.835279 6.74983 1.24887 6.74983H3.30826C3.61845 6.74983 3.86991 6.97019 3.86991 7.24201C3.86991 7.51383 3.61845 7.73419 3.30826 7.73419L1.6233 7.73419L1.6233 10.1951C1.6233 10.4669 1.37184 10.6873 1.06165 10.6873Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_11904_479">
          <rect width="12" height="12" fill="currentColor" transform="translate(0.5)" />
        </clipPath>
      </defs>
    </svg>
  );
}
