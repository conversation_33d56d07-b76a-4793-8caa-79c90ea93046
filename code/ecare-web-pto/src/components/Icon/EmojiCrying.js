import React from 'react';

export default function EmojiCrying({ active = false, transition, ...props }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 32 32" {...props}>
      <circle cx="16" cy="16" r="16" fill={active ? '#FCD117' : 'transparent'} style={{ transition }} />
      <path
        d="M1.5 13.5L5 11V16L3.5 17L2 16.5L1 15.5L1.5 13.5Z"
        fill={active ? '#7DE7FF' : 'transparent'}
        style={{ transition }}
      />
      <path
        d="M30.5 13.5L27 11V15L28.5 17L30 16.5L31 15.5L30.5 13.5Z"
        fill={active ? '#7DE7FF' : 'transparent'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.006 29C21.9785 29 27.0104 24.9724 28.5336 19.485C29.6657 19.588 30.6333 19.1464 31.2554 18.7675C31.4366 18.6571 31.6331 18.5249 31.832 18.368C30.6876 26.0817 24.038 32 16.006 32C7.97779 32 1.33058 26.0872 0.181641 18.3788C0.375849 18.531 0.567477 18.6597 0.744575 18.7675C1.36881 19.1477 2.341 19.5911 3.47813 19.4839C5.001 24.9718 10.0331 29 16.006 29ZM5.38345 8.50407C7.73752 5.17418 11.6179 3 16.006 3C20.3936 3 24.2735 5.17361 26.6277 8.50277C27.0755 8.45145 27.554 8.53626 28.0199 8.79976C28.4811 9.06058 29.3962 9.59773 30.3028 10.25C30.5693 10.4417 30.8748 10.6705 31.1861 10.9302C29.0658 4.57856 23.0706 0 16.006 0C8.94661 0 2.95501 4.57186 0.830563 10.9162C1.13606 10.6623 1.43547 10.4382 1.6971 10.25C2.60374 9.59773 3.51888 9.06058 3.98005 8.79976C4.44982 8.53407 4.93235 8.45005 5.38345 8.50407Z"
        fill={active ? '#AF9213' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M28.8414 16.2763C28.7328 16.2614 28.4825 16.1914 28.1823 15.6429C28.0802 15.4563 27.9836 15.1507 27.9039 14.7394C27.8256 14.3355 27.7686 13.8574 27.7321 13.3444C27.7085 13.0132 27.6943 12.6835 27.6864 12.3724C27.9302 12.5306 28.1848 12.7027 28.4361 12.8835C28.838 13.1727 29.2007 13.4628 29.4919 13.7351C29.7881 14.0121 29.9894 14.2515 30.091 14.4372C30.3904 14.9842 30.3303 15.2616 30.2797 15.3901C30.2085 15.5709 30.019 15.7907 29.6496 16.0157C29.2775 16.2423 29.0097 16.2995 28.8414 16.2763ZM30.4756 17.4864C29.9497 17.8067 29.2785 18.0772 28.5706 17.9799C27.8283 17.8777 27.1929 17.3936 26.7127 16.5163C26.2981 15.7588 26.1322 14.5559 26.0584 13.5194C25.9819 12.4453 25.997 11.4041 26.0127 10.8784C26.0325 10.2153 26.7277 9.79193 27.282 10.1054C27.7214 10.354 28.5837 10.8607 29.4273 11.4676C30.2414 12.0533 31.146 12.8063 31.5607 13.5638C32.0408 14.4411 32.1206 15.2637 31.8333 15.9928C31.5593 16.6882 31.0015 17.1661 30.4756 17.4864Z"
        fill={active ? '#AF9213' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.15858 16.2763C3.26719 16.2614 3.51747 16.1914 3.81765 15.6429C3.91981 15.4563 4.01637 15.1507 4.09609 14.7394C4.1744 14.3355 4.23139 13.8574 4.26792 13.3444C4.29149 13.0132 4.30568 12.6835 4.31355 12.3724C4.06984 12.5306 3.81522 12.7027 3.56393 12.8835C3.16199 13.1727 2.79926 13.4628 2.5081 13.7351C2.21188 14.0121 2.01064 14.2515 1.90897 14.4372C1.6096 14.9842 1.66967 15.2616 1.72031 15.3901C1.79154 15.5709 1.98101 15.7907 2.3504 16.0157C2.72254 16.2423 2.99027 16.2995 3.15858 16.2763ZM1.52437 17.4864C2.05028 17.8067 2.72152 18.0772 3.42942 17.9799C4.17169 17.8777 4.80712 17.3936 5.2873 16.5163C5.70193 15.7588 5.86779 14.5559 5.94159 13.5194C6.01806 12.4453 6.00296 11.4041 5.98728 10.8784C5.96751 10.2153 5.27231 9.79193 4.71805 10.1054C4.27857 10.354 3.41626 10.8607 2.57268 11.4676C1.75855 12.0533 0.853965 12.8063 0.439333 13.5638C-0.0408458 14.4411 -0.12059 15.2637 0.166712 15.9928C0.440712 16.6882 0.998451 17.1661 1.52437 17.4864Z"
        fill={active ? '#AF9213' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M25.7693 22.2918C25.6573 22.3581 25.5386 22.4085 25.4163 22.4431C25.2955 22.4776 25.1727 22.4962 25.0506 22.4997C24.8236 22.5066 24.6019 22.4613 24.4005 22.3723C24.3174 22.3357 24.2375 22.2915 24.1619 22.2401C24.0959 22.1952 24.0331 22.1449 23.9744 22.0893L23.9696 22.0854C23.9449 22.0656 23.8848 22.0198 23.7834 21.9557C23.581 21.8279 23.2094 21.6245 22.6186 21.4097C21.7899 21.1083 20.5237 20.7824 18.6833 20.6144C19.4954 20.3399 20.0059 19.9423 20.0059 19.5C20.0059 18.6716 18.215 18 16.0059 18C13.7967 18 12.0059 18.6716 12.0059 19.5C12.0059 19.9423 12.5164 20.3399 13.3286 20.6145C11.4885 20.7824 10.2225 21.1084 9.39386 21.4097C8.80307 21.6245 8.4315 21.8279 8.22911 21.9557C8.12766 22.0198 8.06762 22.0656 8.0429 22.0854L8.03808 22.0893C7.91847 22.2026 7.78202 22.2941 7.63517 22.3618C7.42726 22.458 7.19678 22.507 6.96064 22.4997C6.83886 22.496 6.71642 22.4775 6.59596 22.4431C6.47375 22.4085 6.35507 22.358 6.24315 22.2918C5.96862 22.1299 5.75752 21.8866 5.63291 21.6034C5.54468 21.4029 5.49982 21.1824 5.50651 20.9566C5.50998 20.8342 5.52855 20.711 5.56313 20.5898C5.56762 20.574 5.57238 20.5582 5.5774 20.5425C5.58346 20.5224 5.59143 20.4967 5.60138 20.4655C5.62264 20.3989 5.65299 20.3074 5.69324 20.1947C5.77364 19.9696 5.89418 19.6578 6.06146 19.288C6.39435 18.5521 6.92157 17.5662 7.70177 16.5733C9.27664 14.5689 11.9246 12.5 16.0062 12.5C20.0879 12.5 22.7359 14.5689 24.3107 16.5733C25.0909 17.5662 25.6181 18.5521 25.951 19.288C26.1183 19.6578 26.2388 19.9696 26.3192 20.1947C26.3595 20.3074 26.3899 20.3989 26.4111 20.4655C26.4211 20.4968 26.4291 20.5227 26.4352 20.5427C26.44 20.5578 26.4446 20.573 26.4489 20.5882C26.4835 20.7091 26.5022 20.832 26.5059 20.9542C26.5104 21.0974 26.4941 21.2385 26.4592 21.3738C26.4166 21.54 26.3451 21.7004 26.2447 21.8468C26.1728 21.9522 26.0869 22.049 25.9882 22.1344C25.9207 22.1929 25.8476 22.2457 25.7693 22.2918Z"
        fill={active ? '#AF9213' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.9984 8.59513C20.2465 8.5331 20.7527 8.49142 21.5586 8.89442C22.44 9.33511 22.791 9.73044 22.9238 9.9297C22.98 10.014 23.0025 10.071 23.0102 10.0936C23.0574 10.602 23.4851 11 24.0059 11C24.5581 11 25.0059 10.5523 25.0059 10H24.0059C25.0059 10 25.0059 10 25.0059 10L25.0059 9.99604L25.0058 9.99197L25.0057 9.98353L25.0053 9.96537C25.0049 9.95271 25.0043 9.93896 25.0033 9.92414C25.0014 9.89451 24.9982 9.86066 24.9932 9.82291C24.9831 9.74732 24.9656 9.65662 24.9361 9.5534C24.8767 9.34554 24.7708 9.0947 24.5879 8.8203C24.2208 8.26957 23.5717 7.66489 22.4531 7.10557C21.2591 6.50855 20.2652 6.46687 19.5133 6.65484C19.1483 6.74611 18.8646 6.88557 18.6634 7.01136C18.5628 7.07419 18.4825 7.13382 18.4221 7.18286C18.3919 7.20741 18.3666 7.22943 18.3461 7.24802L18.3188 7.27328L18.3079 7.28383L18.3031 7.28855L18.3009 7.29076C18.3009 7.29076 18.2987 7.29288 19.0059 7.99999L18.2987 7.29288C17.9082 7.68341 17.9082 8.31657 18.2988 8.7071C18.6806 9.08899 19.2946 9.09743 19.6867 8.73242C19.6924 8.72805 19.7046 8.71905 19.7234 8.70736C19.7721 8.6769 19.8635 8.62886 19.9984 8.59513Z"
        fill={active ? '#392E00' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.0133 8.59515C11.7652 8.53312 11.259 8.49143 10.4531 8.89442C9.5717 9.33512 9.22075 9.73044 9.0879 9.9297C9.0317 10.014 9.00918 10.071 9.00153 10.0936C8.95432 10.602 8.52657 11 8.00586 11C7.45357 11 7.00586 10.5523 7.00586 9.99998H8.00586C7.00586 9.99998 7.00586 9.99998 7.00586 9.99998L7.00587 9.99602L7.00589 9.99195L7.00599 9.9835L7.00642 9.96535C7.00683 9.95269 7.00747 9.93893 7.00842 9.92412C7.01033 9.89448 7.01351 9.86063 7.01854 9.82289C7.02862 9.7473 7.0461 9.65659 7.07559 9.55338C7.13498 9.34551 7.24088 9.09468 7.42381 8.82028C7.79097 8.26956 8.44002 7.66489 9.55864 7.10558C10.7527 6.50855 11.7465 6.46689 12.4984 6.65488C12.8635 6.74615 13.1471 6.88563 13.3484 7.01142C13.4489 7.07425 13.5292 7.13388 13.5896 7.18293C13.6198 7.20748 13.6452 7.22949 13.6657 7.24808L13.6929 7.27335L13.7038 7.2839L13.7086 7.28862L13.7109 7.29083C13.7109 7.29083 13.713 7.29295 13.0105 7.99544L13.713 7.29295C14.1035 7.68349 14.1035 8.31665 13.7129 8.70717C13.331 9.08905 12.7171 9.09747 12.325 8.73246C12.3193 8.72809 12.3071 8.71909 12.2883 8.70739C12.2396 8.67693 12.1482 8.62889 12.0133 8.59515Z"
        fill={active ? '#392E00' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.006 29C21.9785 29 27.0104 24.9724 28.5336 19.485C29.6657 19.588 30.6333 19.1464 31.2554 18.7675C31.4366 18.6571 31.6331 18.5249 31.832 18.368C30.6876 26.0817 24.038 32 16.006 32C7.97779 32 1.33058 26.0872 0.181641 18.3788C0.375849 18.531 0.567477 18.6597 0.744575 18.7675C1.36881 19.1477 2.341 19.5911 3.47813 19.4839C5.001 24.9718 10.0331 29 16.006 29ZM5.38345 8.50407C7.73752 5.17418 11.6179 3 16.006 3C20.3936 3 24.2735 5.17361 26.6277 8.50277C27.0755 8.45145 27.554 8.53626 28.0199 8.79976C28.4811 9.06058 29.3962 9.59773 30.3028 10.25C30.5693 10.4417 30.8748 10.6705 31.1861 10.9302C29.0658 4.57856 23.0706 0 16.006 0C8.94661 0 2.95501 4.57186 0.830563 10.9162C1.13606 10.6623 1.43547 10.4382 1.6971 10.25C2.60374 9.59773 3.51888 9.06058 3.98005 8.79976C4.44982 8.53407 4.93235 8.45005 5.38345 8.50407Z"
        fill={active ? '#AF9213' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M28.8414 16.2763C28.7328 16.2614 28.4825 16.1914 28.1823 15.6429C28.0802 15.4563 27.9836 15.1507 27.9039 14.7394C27.8256 14.3355 27.7686 13.8574 27.7321 13.3444C27.7085 13.0132 27.6943 12.6835 27.6864 12.3724C27.9302 12.5306 28.1848 12.7027 28.4361 12.8835C28.838 13.1727 29.2007 13.4628 29.4919 13.7351C29.7881 14.0121 29.9894 14.2515 30.091 14.4372C30.3904 14.9842 30.3303 15.2616 30.2797 15.3901C30.2085 15.5709 30.019 15.7907 29.6496 16.0157C29.2775 16.2423 29.0097 16.2995 28.8414 16.2763ZM30.4756 17.4864C29.9497 17.8067 29.2785 18.0772 28.5706 17.9799C27.8283 17.8777 27.1929 17.3936 26.7127 16.5163C26.2981 15.7588 26.1322 14.5559 26.0584 13.5194C25.9819 12.4453 25.997 11.4041 26.0127 10.8784C26.0325 10.2153 26.7277 9.79193 27.282 10.1054C27.7214 10.354 28.5837 10.8607 29.4273 11.4676C30.2414 12.0533 31.146 12.8063 31.5607 13.5638C32.0408 14.4411 32.1206 15.2637 31.8333 15.9928C31.5593 16.6882 31.0015 17.1661 30.4756 17.4864Z"
        fill={active ? '#AF9213' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.15858 16.2763C3.26719 16.2614 3.51747 16.1914 3.81765 15.6429C3.91981 15.4563 4.01637 15.1507 4.09609 14.7394C4.1744 14.3355 4.23139 13.8574 4.26792 13.3444C4.29149 13.0132 4.30568 12.6835 4.31355 12.3724C4.06984 12.5306 3.81522 12.7027 3.56393 12.8835C3.16199 13.1727 2.79926 13.4628 2.5081 13.7351C2.21188 14.0121 2.01064 14.2515 1.90897 14.4372C1.6096 14.9842 1.66967 15.2616 1.72031 15.3901C1.79154 15.5709 1.98101 15.7907 2.3504 16.0157C2.72254 16.2423 2.99027 16.2995 3.15858 16.2763ZM1.52437 17.4864C2.05028 17.8067 2.72152 18.0772 3.42942 17.9799C4.17169 17.8777 4.80712 17.3936 5.2873 16.5163C5.70193 15.7588 5.86779 14.5559 5.94159 13.5194C6.01806 12.4453 6.00296 11.4041 5.98728 10.8784C5.96751 10.2153 5.27231 9.79193 4.71805 10.1054C4.27857 10.354 3.41626 10.8607 2.57268 11.4676C1.75855 12.0533 0.853965 12.8063 0.439333 13.5638C-0.0408458 14.4411 -0.12059 15.2637 0.166712 15.9928C0.440712 16.6882 0.998451 17.1661 1.52437 17.4864Z"
        fill={active ? '#AF9213' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.9984 8.59513C20.2465 8.5331 20.7527 8.49142 21.5586 8.89442C22.44 9.33511 22.791 9.73044 22.9238 9.9297C22.98 10.014 23.0025 10.071 23.0102 10.0936C23.0574 10.602 23.4851 11 24.0059 11C24.5581 11 25.0059 10.5523 25.0059 10H24.0059C25.0059 10 25.0059 10 25.0059 10L25.0059 9.99604L25.0058 9.99197L25.0057 9.98353L25.0053 9.96537C25.0049 9.95271 25.0043 9.93896 25.0033 9.92414C25.0014 9.89451 24.9982 9.86066 24.9932 9.82291C24.9831 9.74732 24.9656 9.65662 24.9361 9.5534C24.8767 9.34554 24.7708 9.0947 24.5879 8.8203C24.2208 8.26957 23.5717 7.66489 22.4531 7.10557C21.2591 6.50855 20.2652 6.46687 19.5133 6.65484C19.1483 6.74611 18.8646 6.88557 18.6634 7.01136C18.5628 7.07419 18.4825 7.13382 18.4221 7.18286C18.3919 7.20741 18.3666 7.22943 18.3461 7.24802L18.3188 7.27328L18.3079 7.28383L18.3031 7.28855L18.3009 7.29076C18.3009 7.29076 18.2987 7.29288 19.0059 7.99999L18.2987 7.29288C17.9082 7.68341 17.9082 8.31657 18.2988 8.7071C18.6806 9.08899 19.2946 9.09743 19.6867 8.73242C19.6924 8.72805 19.7046 8.71905 19.7234 8.70736C19.7721 8.6769 19.8635 8.62886 19.9984 8.59513Z"
        fill={active ? '#392E00' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.0133 8.59515C11.7652 8.53312 11.259 8.49143 10.4531 8.89442C9.5717 9.33512 9.22075 9.73044 9.0879 9.9297C9.0317 10.014 9.00918 10.071 9.00153 10.0936C8.95432 10.602 8.52657 11 8.00586 11C7.45357 11 7.00586 10.5523 7.00586 9.99998H8.00586C7.00586 9.99998 7.00586 9.99998 7.00586 9.99998L7.00587 9.99602L7.00589 9.99195L7.00599 9.9835L7.00642 9.96535C7.00683 9.95269 7.00747 9.93893 7.00842 9.92412C7.01033 9.89448 7.01351 9.86063 7.01854 9.82289C7.02862 9.7473 7.0461 9.65659 7.07559 9.55338C7.13498 9.34551 7.24088 9.09468 7.42381 8.82028C7.79097 8.26956 8.44002 7.66489 9.55864 7.10558C10.7527 6.50855 11.7465 6.46689 12.4984 6.65488C12.8635 6.74615 13.1471 6.88563 13.3484 7.01142C13.4489 7.07425 13.5292 7.13388 13.5896 7.18293C13.6198 7.20748 13.6452 7.22949 13.6657 7.24808L13.6929 7.27335L13.7038 7.2839L13.7086 7.28862L13.7109 7.29083C13.7109 7.29083 13.713 7.29295 13.0105 7.99544L13.713 7.29295C14.1035 7.68349 14.1035 8.31665 13.7129 8.70717C13.331 9.08905 12.7171 9.09747 12.325 8.73246C12.3193 8.72809 12.3071 8.71909 12.2883 8.70739C12.2396 8.67693 12.1482 8.62889 12.0133 8.59515Z"
        fill={active ? '#392E00' : '#C7C7C7'}
        style={{ transition }}
      />
      <path
        d="M20.5 20.6H12.5C12.1 20.6 11 19.6 11 19.1L13 17.6L15 17.1L20 17.6L21.5 19.1C21.5 19.6 21.3 20.6 20.5 20.6Z"
        fill={active ? '#CE1126' : 'transparent'}
        style={{ transition }}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M25.7693 22.2918C25.6573 22.3581 25.5386 22.4085 25.4163 22.4431C25.2955 22.4776 25.1727 22.4962 25.0506 22.4997C24.8236 22.5066 24.6019 22.4613 24.4005 22.3723C24.3174 22.3357 24.2375 22.2915 24.1619 22.2401C24.0959 22.1952 24.0331 22.1449 23.9744 22.0893L23.9696 22.0854C23.9449 22.0656 23.8848 22.0198 23.7834 21.9557C23.581 21.8279 23.2094 21.6245 22.6186 21.4097C21.7899 21.1083 20.5237 20.7824 18.6833 20.6144C19.4954 20.3399 20.0059 19.9423 20.0059 19.5C20.0059 18.6716 18.215 18 16.0059 18C13.7967 18 12.0059 18.6716 12.0059 19.5C12.0059 19.9423 12.5164 20.3399 13.3286 20.6145C11.4885 20.7824 10.2225 21.1084 9.39386 21.4097C8.80307 21.6245 8.4315 21.8279 8.22911 21.9557C8.12766 22.0198 8.06762 22.0656 8.0429 22.0854L8.03808 22.0893C7.91847 22.2026 7.78202 22.2941 7.63517 22.3618C7.42726 22.458 7.19678 22.507 6.96064 22.4997C6.83886 22.496 6.71642 22.4775 6.59596 22.4431C6.47375 22.4085 6.35507 22.358 6.24315 22.2918C5.96862 22.1299 5.75752 21.8866 5.63291 21.6034C5.54468 21.4029 5.49982 21.1824 5.50651 20.9566C5.50998 20.8342 5.52855 20.711 5.56313 20.5898C5.56762 20.574 5.57238 20.5582 5.5774 20.5425C5.58346 20.5224 5.59143 20.4967 5.60138 20.4655C5.62264 20.3989 5.65299 20.3074 5.69324 20.1947C5.77364 19.9696 5.89418 19.6578 6.06146 19.288C6.39435 18.5521 6.92157 17.5662 7.70177 16.5733C9.27664 14.5689 11.9246 12.5 16.0062 12.5C20.0879 12.5 22.7359 14.5689 24.3107 16.5733C25.0909 17.5662 25.6181 18.5521 25.951 19.288C26.1183 19.6578 26.2388 19.9696 26.3192 20.1947C26.3595 20.3074 26.3899 20.3989 26.4111 20.4655C26.4211 20.4968 26.4291 20.5227 26.4352 20.5427C26.44 20.5578 26.4446 20.573 26.4489 20.5882C26.4835 20.7091 26.5022 20.832 26.5059 20.9542C26.5104 21.0974 26.4941 21.2385 26.4592 21.3738C26.4166 21.54 26.3451 21.7004 26.2447 21.8468C26.1728 21.9522 26.0869 22.049 25.9882 22.1344C25.9207 22.1929 25.8476 22.2457 25.7693 22.2918Z"
        fill={active ? '#5E0E0E' : '#C7C7C7'}
      />
    </svg>
  );
}
