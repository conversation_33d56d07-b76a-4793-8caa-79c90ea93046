import React from 'react';
import { Spin } from 'antd';
import classNames from 'classnames';
import styles from './index.less';

const Loading = ({ fullscreen = true, maskStyle = 'dark', size = 'large' }) => (
  <div className={classNames(styles.loading, styles[maskStyle], styles[size], { [styles.fullscreen]: fullscreen })}>
    <div className={styles.circleBox}>
      <svg xmlns="http://www.w3.org/2000/svg" width="2em" height="2em" viewBox="0 0 32 32">
        <circle cx="16" cy="16" r="14" stroke="#CE1126" fill="transparent" strokeWidth="2" strokeOpacity="0.3" />
        <circle
          className={classNames(styles.spin, styles.red)}
          cx="16"
          cy="16"
          r="14"
          stroke="#CE1126"
          fill="transparent"
          strokeWidth="2"
          strokeLinecap="round"
          strokeDasharray="58 88"
        />

        <circle cx="16" cy="16" r="10" stroke="#335EB9" fill="transparent" strokeWidth="2" strokeOpacity="0.3" />
        <circle
          className={classNames(styles.spin, styles.blue)}
          cx="16"
          cy="16"
          r="10"
          stroke="#335EB9"
          fill="transparent"
          strokeWidth="2"
          strokeLinecap="round"
          strokeDasharray="42 62"
        />

        <circle cx="16" cy="16" r="6" stroke="#FCD116" fill="transparent" strokeWidth="2" strokeOpacity="0.3" />
        <circle
          className={classNames(styles.spin, styles.yellow)}
          cx="16"
          cy="16"
          r="6"
          stroke="#FCD116"
          fill="transparent"
          strokeWidth="2"
          strokeLinecap="round"
          strokeDasharray="25 38"
        />
      </svg>
    </div>
  </div>
);

const BaseSpin = ({ fullscreen = true, size = 'large', maskStyle = 'dark', children, ...otherProps }) => (
  <Spin indicator={<Loading fullscreen={fullscreen} size={size} maskStyle={maskStyle} />} {...otherProps}>
    {children}
  </Spin>
);

export default BaseSpin;
