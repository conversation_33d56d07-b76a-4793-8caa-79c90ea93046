.loading {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 1;
  .circleBox {
    font-size: 0.3rem;
    .spin {
      transform-origin: center;
      animation: spin 2s linear infinite forwards;
      &.red {
        animation-duration: 1.2s;
      }
      &.blue {
        animation: spin1 2s linear infinite forwards;
      }
      &.yellow {
        animation-duration: 2.5s;
      }
    }
  }
  &.fullscreen {
    position: fixed;
  }
  &.dark {
    background-color: rgba(0, 0, 0, 0.8);
  }
  &.transparent {
    background-color: transparent;
  }
  &.light {
    background-color: rgba(255, 255, 255, 0.8);
  }
  &.large {
    .circleBox {
      font-size: 0.3rem;
    }
  }
  &.middle {
    .circleBox {
      font-size: 0.2rem;
    }
  }
  &.small {
    .circleBox {
      font-size: 0.12rem;
    }
  }
}


@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@keyframes spin1 {
  from {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(540deg);
  }
}
