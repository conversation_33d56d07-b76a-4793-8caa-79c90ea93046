/*
 * @Author: zhu.huijun
 * @Date: 2021-01-13 14:51:19
 * @Last Modified by: zhu.sheng<PERSON>
 * @Last Modified time: 2021-05-11 19:11:52
 */
import React, { useEffect } from 'react';
import { connect } from 'umi';
import { Input } from 'antd';

const CaptchaInput = props => {
  const { size = 'large', value, requestSN, setRequestSN, onChange, ...preProps } = props;
  useEffect(() => {
    setRequestSN();
  }, []);
  const checkValue = e => {
    const { value: eTargetValue } = e.target;
    let result = eTargetValue.replace(/[\W]/g, '');
    if (result.length > 4) {
      result = result.substring(0, 4);
    }
    onChange(result);
  };

  const renderCodeImg = () => (
    <img
      style={{ width: 61, height: 26, cursor: 'pointer' }}
      alt=""
      src={`/ecare/webs/captcha/${requestSN}`}
      onClick={setRequestSN}
    />
  );
  return (
    <Input
      size={size}
      value={value}
      suffix={renderCodeImg()}
      maxLength={4}
      {...preProps}
      onChange={checkValue}
    />
  );
};

const mapDispatchToProps = dispatch => ({
  setRequestSN: () => dispatch({
    type: 'login/setRequestSN',
  }),
});

export default connect(({ login }) => ({ requestSN: login.requestSN }), mapDispatchToProps)(CaptchaInput);
