/*
 * @Author: zhu.huijun
 * @Date: 2021-01-07 15:37:57
 * @Last Modified by: chen.chen
 * @Last Modified time: 2023-06-09 09:55:04
 */
import React from 'react';
import { Input } from 'antd';
import { isEmpty } from 'lodash';
import styles from './index.less';
import { formatNbr } from '@/utils/utils';
import { NUMBER_FORMATTER_3_3_4, NUMBER_SINGLE_PLACEHOLDER } from '@/constants/constants';

const MobileNumInput = props => {
  const {
    size = 'large',
    value,
    maxLength,
    onChange,
    form = {},
    id,
    needFormat = true,
    prefix = '0',
    split,
    placeholder,
    ...preProps
  } = props;
  const { validateFields } = form;
  const checkValue = e => {
    let inputValue = e.target.value.replace(/[^\d]/g, '');
    if (inputValue.length > maxLength) {
      inputValue = inputValue.substring(0, maxLength);
    }
    onChange(inputValue);
  };

  const formatValue = () => {
    if (!value) {
      return '';
    }
    let inputValue = formatNbr(value, split);
    if (inputValue[inputValue.length - 1] === ' ') {
      inputValue = inputValue.substring(0, inputValue.length - 1);
    }
    return inputValue;
  };

  const changeStyle = isFocus => {
    // 重写聚焦颜色变化
    const addonSpan = document.getElementsByClassName('ant-input-group-addon');
    if (isEmpty(addonSpan)) {
      return;
    }
    if (isFocus) {
      addonSpan[0].classList.add(styles.addonFocus);
      return;
    }
    // 失去焦点
    const index = id.indexOf('_');
    const name = id.substring(index + 1, id.length);
    addonSpan[0].classList.remove(styles.addonFocus);
    if (validateFields) {
      validateFields([name]);
    }
  };

  const defaultPlaceholder = formatNbr(
    ''.padStart(maxLength, NUMBER_SINGLE_PLACEHOLDER),
    needFormat ? NUMBER_FORMATTER_3_3_4 : null,
  );

  return (
    <Input
      size={size}
      addonBefore={prefix}
      placeholder={placeholder || defaultPlaceholder}
      value={(needFormat && formatValue()) || value}
      {...preProps}
      onChange={checkValue}
      onFocus={() => changeStyle(true)}
      onBlur={() => changeStyle(false)}
      maxLength={(needFormat && maxLength + 2) || maxLength} // 实际要求是9位，这边因为空格原因需要 + 2，即11位
    />
  );
};

export default MobileNumInput;
