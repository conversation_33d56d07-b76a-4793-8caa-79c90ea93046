import React from 'react';
import { Input } from 'antd';

const FormInput = props => {
  const { size = 'large', type, value, valueType, maxLength, onChange, ...preProps } = props;
  const checkValue = e => {
    let inputValue = e.target.value;
    if (valueType === 'number') {
      inputValue = inputValue.replace(/[^\d]/g, '');
    }
    if (inputValue.length > maxLength) {
      inputValue = inputValue.substring(0, maxLength);
    }
    onChange(inputValue);
  };

  const renderPasswordIcon = visible => (
    <div>
      <span className={visible ? 'icon-eye-fill' : 'icon-eye-close'} style={{ color: '#333333' }} />
    </div>
  );

  if (type === 'password') {
    return (
      <Input.Password
        size={size}
        value={value}
        onChange={checkValue}
        iconRender={visible => renderPasswordIcon(visible)}
        {...preProps}
      />
    );
  }
  return <Input size={size} value={value} onChange={checkValue} {...preProps} />;
};

export default FormInput;
