/*
 * @Author: zhu.hui<PERSON>
 * @Date: 2021-01-13 13:41:34
 * @Last Modified by: zhu.huijun
 * @Last Modified time: 2021-04-09 10:39:33
 */
import React from 'react';
import { Button } from 'antd';
import styles from './index.less';

const GradualButton = props => {
  const { disabled, children, ...resProps } = props;
  return (
    <div className={styles.gradualButton}>
      <Button
        disabled={disabled}
        {...resProps}
      >
        {children}
      </Button>
    </div>
  );
};

export default GradualButton;
