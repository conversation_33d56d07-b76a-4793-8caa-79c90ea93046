import constants from '.';

const LOAD_TYPE = [
  {
    label: 'All',
    value: 'all',
  },
  {
    label: 'Cash',
    value: '1',
  },
  {
    label: 'Reload Card',
    value: '0',
  },
];

const SHARE_TYPE = [
  {
    label: 'All',
    value: '',
  },
  {
    label: 'Load',
    value: 'Share Load',
  },
  {
    label: 'Data',
    value: 'Share Data',
  },
];

const VIEW_ITEM = [
  {
    label: '10 items',
    value: 10,
  },
  {
    label: '20 items',
    value: 20,
  },
  {
    label: '30 items',
    value: 30,
  },
];

const RECHARGE_MODE = {
  1: 'Online Load',
  0: 'Reload Card',
};

const STATUS = {
  1: 'Completed',
  101300: 'Waiting for payment',
  300000: 'Completed',
  101600: 'Failed',
  301100: 'In Progress',
};

const STATUS_STYLE = {
  1: '#0038A8',
  101600: '#E50000',
  101300: '#333333',
  300000: '#0038A8',
  301100: '#4AE100',
};
// 买包历史返回状态为101300 为在途单
const PROMOSTATUSCODE = '101300';

const VOUCHER_FORMAT = 'MM/DD/YYYY';
const SHOW_FORMAT = 'MMM. D YYYY h:mmA';
const NEWDATE_FORMAT = 'MMM D, YYYY HH:mm:ss';
const PROMOS_FORMAT = 'MMM D, YYYY';
const HISTORY_FORMAT = 'MMM D, YYYY h:mm A'; // roaming history 页面展示时间
const DATA_FORMAT = 'MMM. D YYYY';
const STANDARD_FORMAT = 'MMM. D, YYYY';
const DATE_FORMAT_WITH_FULL_MONTH = 'MMMM D, YYYY';
const PARAM_FORMAT = 'YYYY-MM-DD';
const APP_NATIVE_FORMAT = 'YYYYMMDDHHmmss';
const START_TIME = ' 00:00:00';
const END_TIME = ' 23:59:59';
const ALL = 'Share Load';
const SHARE_ALL = '';
const SHARE_LOAD = 'Share Load';
const SHARE_DATA = 'Share Data';

// fileName写死loadhis
const LOAD_HIS = 'loadHis';
const PROMO_HIS = 'promoHis';
// 买包历史column写死
const PROMO_HIS_COLUMN = [
  {
    columnName: 'accNbr',
    columnLabelName: 'accNbr',
  },
  {
    columnName: 'displayPrice',
    columnLabelName: 'amount',
  },
  {
    columnName: 'custOrderId',
    columnLabelName: 'txnId',
  },
  {
    columnName: 'orderStateName',
    columnLabelName: 'state',
  },
  {
    columnName: 'acceptDate',
    columnLabelName: 'txnDate',
  },
  {
    columnName: 'offerName',
    columnLabelName: 'detail',
  },
];
// columns写死
const LOAD_HIS_COLUMN = [
  {
    columnName: 'accNbr',
    columnLabelName: 'accNbr',
  },
  {
    columnName: 'formatAmount',
    columnLabelName: 'amount',
  },
  {
    columnName: 'loadType',
    columnLabelName: 'loadType',
  },
  {
    columnName: 'state',
    columnLabelName: 'state',
  },
  {
    columnName: 'txnDate',
    columnLabelName: 'txtDate',
  },
];

// fileName写死loadhis
const TRANSFER_HIS = 'transferHis';
// columns写死
const TRANSFER_HIS_COLUMN = [
  // columns写死
  {
    columnName: 'accNbr',
    columnLabelName: 'accNbr',
  },
  {
    columnName: 'transferAmount',
    columnLabelName: 'amount',
  },
  {
    columnName: 'balanceTypeName',
    columnLabelName: 'transferType',
  },
  {
    columnName: 'state',
    columnLabelName: 'state',
  },
  {
    columnName: 'txnDate',
    columnLabelName: 'txtDate',
  },
];
// 后付费，预付费
const POST_PRE = [
  {
    label: 'Postpaid',
    value: '1200',
  },
  {
    label: 'Prepaid',
    value: '2100',
  },
];

const DOWNLOADURL = '/pto/download/asserts/file/app-release_sec_signed.apk';
// 邀请码存cookie
const COOKIEKEY = 'invitasionkey';
// 路由
const ROUTER = {
  invitationCode: 'invitationCode',
  download: 'download',
  portal: 'portal',
  help: 'help',
};
// 买包余额支付方式
const LOADBALANCE = {
  PayMethodName: 'Load Balance',
  thirdPayChannelId: 1,
  thirdPayMethodId: 1,
  touchPointCode: 'ECARE-PROMO-TOUCH',
  channelCode: 'Web Selfcare',
};
// 支付方式的名称
const BUYPROMOPAYMETHOD = {
  loadBalance: 'Load Balance',
  WeChat: 'WeChat',
  Credit: 'Debit/Credit Card',
  aliPay: 'AliPay',
  Grab: 'Grab',
};

// 获取积分配置项
const POINTFAQCONFIGCODE = 'kms.wiki.ecare.point.category.id';

// 查询支付结果  处理中1    2 成功   3失败   4已回退
const PAYMENT_RESULT = {
  PROCESSING: '1',
  SUCCESSFUL: '2',
  FAILURE: '3',
  FALLBACK: '4',
};
const BUYPROMO_FAILD_INFO = 'Sorry, we encountered an error while processing your request. Please try again later.';
const BUYPROMO_SUCCESS_INFO = 'Processing of your order has been completed. Please try again by refreshing the page.';
const BACK_ERROR = 'Oops! There’s a problem with your request. Please refresh the page.';

export const TABS_BAR = {
  PROVINCE: 'Province',
  CITY: 'City',
  DISTRICT: 'Barangay',
  STREET: 'Street',
};
export const AREA_TAB_KEY = {
  PROVINCE_KEY: '1',
  CITY_KEY: '2',
  DISTRICT_KEY: '3',
  STREET_KEY: '4',
};

export const NUMBER_FORMATTER_3_3_4 = '3|3|4';

export const NUMBER_SINGLE_PLACEHOLDER = 'x';

export const DEFAULT_DISPLAY_PRICE = '0.00';

export const IJoinSourceCode = {
  MOBILE_POSTPAID: 'iJoinMobilePostpaid',
  FWA: 'iJoinFWA',
  FTTH: 'ftthIjoinPostpaid',
  FWAPREMIUM: 'FWAPREMIUM',
};

export const IjoinPlanMenuType = {
  SIM_ONLY: 'IJOIN_POSTPAID_SIMONLY',
  WITH_HANDSET: 'IJOIN_POSTPAID_WITH_HANDSET',
};
export const IjoinPlanMenus = [
  {
    title: 'Handset + SIM',
    key: IjoinPlanMenuType.WITH_HANDSET,
  },
  {
    title: 'SIM Only',
    key: IjoinPlanMenuType.SIM_ONLY,
  },
];
export const KEY_OPEN_IJOIN_IN = 'open_ijoin_in';

export const Environment = {
  APP: 'app',
  BROWSER: 'browser',
};

export const ChannelEnv = {
  APP: 'APP',
  WEB: 'WEB',
  H5: 'H5',
};
export const ContactChannel = {
  APP: 'DITO App',
  H5: 'H5',
};

// 用户角色
export const Role = {
  PREPAID: 'PREPAID',
  POSTPAID: 'POSTPAID',
  FWA: 'FWA',
  FWAPOSTPAID: 'FWAPOSTPAID',
  BROADBAND: 'BROADBAND',
};

export const PromoType = {
  PROMO: 'PROMO',
  AUTO_PAY: 'AUTO_PAY',
  MCCM: 'MCCM',
  PAY_IN_ADVANCE: 'PAY_IN_ADVANCE',
  ROAMING: 'ROAMING',
  OTT: 'OTT',
};

export const LocaleKeyMap = {
  en: 'en-US',
  zh: 'zh-CN',
};

export const ContactType = {
  PHONE: 'accNbr',
  EMAIL: 'email',
};

export const RecieveMethod = {
  DELIVERY: 'DELIVERY',
  D2D: 'D2D',
};

export const TimeUnit = {
  Y: 'year',
  M: 'month',
  D: 'day',
  W: 'week',
  H: 'hour',
};

export const TRUE = 'true';
export const FALSE = 'false';
export const YES = 'Y';
export const NO = 'N';

export const CURRENCY_PHP = '₱';
export const CURRENCY_PHP_NAME = 'PHP';

export const PAYMENT_ORDER = [
  constants.PayId.Credit_DebitCard.thirdPayChannelId,
  constants.PayId.GcashQyCode.thirdPayChannelId,
  constants.PayId.GcashOld.thirdPayChannelId,
  constants.PayId.ipay88.thirdPayChannelId,
  constants.PayId.GrabPayQyCode.thirdPayChannelId,
  constants.PayId.PayMaya.thirdPayChannelId,
  constants.PayId.Shopee.thirdPayChannelId,
  constants.PayId.WeChat.thirdPayChannelId,
];

export {
  LOAD_TYPE,
  SHARE_TYPE,
  VIEW_ITEM,
  STATUS,
  STATUS_STYLE,
  DATA_FORMAT,
  STANDARD_FORMAT,
  DATE_FORMAT_WITH_FULL_MONTH,
  PARAM_FORMAT,
  APP_NATIVE_FORMAT,
  LOAD_HIS,
  PROMO_HIS,
  LOAD_HIS_COLUMN,
  PROMO_HIS_COLUMN,
  START_TIME,
  END_TIME,
  TRANSFER_HIS,
  TRANSFER_HIS_COLUMN,
  ALL,
  SHARE_ALL,
  SHARE_LOAD,
  SHARE_DATA,
  SHOW_FORMAT,
  NEWDATE_FORMAT,
  PROMOS_FORMAT,
  HISTORY_FORMAT,
  RECHARGE_MODE,
  POST_PRE,
  DOWNLOADURL,
  COOKIEKEY,
  ROUTER,
  LOADBALANCE,
  BUYPROMOPAYMETHOD,
  POINTFAQCONFIGCODE,
  PROMOSTATUSCODE,
  PAYMENT_RESULT,
  BUYPROMO_FAILD_INFO,
  BUYPROMO_SUCCESS_INFO,
  BACK_ERROR,
  VOUCHER_FORMAT,
};
