import buyPromo from '@/pages/h5/BuyPromo/locales/en-US';
import countryPromo from '@/pages/h5/CountryPromo/locales/en-US';
import roamingPromo from '@/pages/h5/RoamingOffer/locales/en-US';
import sra from '@/pages/h5/SIMRegistration/locales/en-US';
import ijoin from '@/pages/h5/Ijoin/locales/en-US';
import offer from '@/pages/h5/Offer/locales/en-US';

export default {
  btn_buy_now: 'Buy Now',
  btn_save: 'Save',
  btn_return_to_homepage: 'Return to Homepage',
  btn_track_your_sth: 'Track your {type}',
  btn_back_to_home: 'Back to Home',
  btn_view_receipt: 'View Receipt',

  lb_auto_pay: 'Auto Pay',
  lb_dito_number: 'DITO Number',
  lb_for_only: 'For only',
  lb_trans_date: 'Transaction Date',
  lb_trans_type: 'Transaction Type',
  'lb_trans_no.': 'Transaction No.',
  lb_trans_number: 'Transaction Number',

  lb_comm_select_date: 'Select date',
  lb_comm_select_date_cancel: 'Cancel',
  lb_comm_select_date_confirm: 'Procceed',
  lb_comm_select_date_ok: 'OK',

  tip_no_result: 'No result',

  // 买包相关
  lb_buy_promo_success_title: 'Purchase Successful',
  lb_buy_promo_success_msg: 'You have successfully purchased <span data-type="offerName">{offerName}.</span>',
  lb_buy_promo_total_charge: 'Total Charge',
  btn_view_promo_history: 'View Promo History',
  tips_buy_promo_combo_pay_oops: 'Oops!',
  tips_buy_promo_combo_pay_change_1: 'Looks like your balance or account has changed.',
  tips_buy_promo_combo_pay_change_2: 'Please choose your payment method again.',
  btn_buy_promo_combo_pay_change: 'Ok, got it!',
  tips_pay_bill_available_points: 'Available Points:',
  tips_buy_promo_cash: 'Cash',
  tips_buy_promo_cash_desc: 'Select lorem ipsum additional amount to pay: {paymentAmount}',
  tips_buy_promo_cash_rule_desc: 'Minimum of ₱10.00 cash transaction to proceed.',
  tips_buy_promo_choose_other_method: 'CHOOSE PAYMENT METHOD TO ADD',
  tips_buy_promo_insufficient_balance: 'Insufficient Balance',
  
  lb_buy_promo_payment_method: 'Payment Method',
  tips_buy_promo_combo_pay_name: 'ComboPay',

  // 功能模块下的 locales
  ...buyPromo,
  ...countryPromo,
  ...roamingPromo,
  ...sra,
  ...ijoin,
  ...offer,
};
