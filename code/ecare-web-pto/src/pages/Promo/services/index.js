import request from '@/utils/request';

export function configParam(params) {
  return request('/ecare/webs/common/configParam', {
    headers: {
      'content-type': 'application/json',
    },
    method: 'POST',
    data: JSON.stringify(params),
  });
}

export function queryOffer(params) {
  return request('/eshopweb/api/goods/queryGoodsForPage', {
    headers: {
      'content-type': 'application/json',
    },
    method: 'POST',
    data: JSON.stringify(params),
  });
}

// 新增买包一周年活动,查询包的种类
export function getYearPromoList(params) {
  return request('/ecare/webs/shop/catg/list', {
    headers: {
      'content-type': 'application/json',
    },
    method: 'POST',
    data: JSON.stringify(params),
  });
}
// 3月新接口 查询新的买包列表
export function queryList(params) {
  return request('/ecare/ceeOffer/list', {
    headers: {
      'content-type': 'application/json',
    },
    method: 'POST',
    data: JSON.stringify(params),
  });
}

export function orderPromotion(params) {
  return request('/ecare/webs/addOnOffer/orderPromotion', {
    headers: {
      'content-type': 'application/json',
    },
    method: 'POST',
    data: JSON.stringify(params),
  });
}

export function qryTransferRuleInfo() {
  // http://172.16.25.133:8588/ecare/webs/balTransfer/qryTransferRuleInfo
  return request('/ecare/webs/balTransfer/qryTransferRuleInfo', {
    headers: {
      'content-type': 'application/json',
    },
    method: 'GET',
  });
}

export function balance(params) {
  const { acctId } = params;
  return request(`/ecare/webs/account/${acctId}/balance`, {});
}

// 转账历史列表
export function qryPromoHis({ beginDate, endDate, accNbr, acctResId, pageSize, pageIndex }) {
  return request(
    `/ecare/webs/balTransfer/qryBalTransferHis?beginDate=${beginDate}&endDate=${endDate}&accNbr=${accNbr}&acctResId=${acctResId}&pageIndex=${pageIndex}&pageSize=${pageSize}`,
    {
      headers: {
        'content-type': 'application/json',
      },
      method: 'GET',
    },
  );
}

export function qryRechargeHis(params) {
  // ecare/webs/subs/packageHisPageList
  return request('/ecare/webs/subs/packageHisPageList', {
    headers: {
      'content-type': 'application/json',
    },
    method: 'POST',
    data: JSON.stringify(params),
  });
}
// // balInfo  的接口就是查余额的接口balance
// export function balInfo(params) {
//   return request('/ecare/webs/balance/balInfo', {
//     headers: {
//       'content-type': 'application/json',
//     },
//     method: 'POST',
//     data: JSON.stringify(params),
//   });
// }

// 充值历史下载
export function downLoadFunc(params) {
  return request('/ecare/webs/dms/download/document', {
    getResponse: true,
    responseType: 'blob',
    method: 'POST',
    data: params,
  });
}

// 查询支付方式
export function queryPayMethod(params) {
  return request('/ecare/webs/payMethod/query', {
    method: 'POST',
    data: params,
  });
}
// 金额
export function queryCalcFee(params) {
  return request('/ecare/webs/recharge/calcFee', {
    method: 'POST',
    data: params,
  });
}

// 支付
export function PayMethodSubmit(params) {
  return request('/ecare/webs/payment/prePay', {
    method: 'POST',
    data: params,
  });
}
// 交易
export function Transaction(params) {
  return request(`/ecare/webs/transaction?paymentSn=${params}`);
}

// 交易取消
export function TransactionCancel(params) {
  return request('/ecare/webs/transaction/requestCancel', {
    method: 'POST',
    data: params,
  });
}

// 查询积分
export function qryPointDetail(params) {
  return request('/ecare/webs/point/calculate', {
    method: 'POST',
    data: params,
  });
}

// 查询在途单
export function qryInTransit() {
  return request('/ecare/webs/transaction/onwayOrder', {
    headers: {
      'content-type': 'application/json',
    },
    method: 'GET',
  });
}
// 查询订单是否完成
export function qryPaymentResult(params) {
  return request('/ecare/webs/payment/qryPaymentResult', {
    method: 'POST',
    data: params,
  });
}

// payc支付成功之后，提交在途单
export function TransitionSubmit(params) {
  return request('/ecare/webs/order/submit', {
    method: 'POST',
    data: params,
  });
}

// 查询积分
export function qryPointDetails(params) {
  return request('/ecare/webs/point/calculate', {
    method: 'POST',
    data: params,
  });
}

// 获取积分详情
export function getPointDetail(params) {
  return request('/ecare/webs/point/detail', {
    method: 'POST',
    data: params,
  });
}
// 积分兑换比例
export function pointPayFactor(params) {
  return request('/ecare/webs/point/payFactor', {
    method: 'POST',
    data: params,
  });
}

export function comboPay(params) {
  return request('/ecare/webs/payMethod/combo-pay-method', {
    method: 'POST',
    data: params,
  });
}
