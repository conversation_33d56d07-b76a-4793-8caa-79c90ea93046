import React, { Component } from 'react';
import { connect } from 'dva';
import { Radio, Button, Spin } from 'antd';
import { get, isEmpty } from 'lodash';
// import { getSubsInfo } from '@/utils/utils';
import { getSubsInfo } from '@/utils/utils';
import { trackSensors } from '@/utils/sensors';
import PaymentModal from '../PaymentModal/index';
import InTransitModal from '../IsInTransit/index';
import TransitionIsEnd from '../TransitionIsEnd';
import styles from './index.less';

class Basic extends Component {
  state = {
    visible: false, // 选择付款方式的弹框
    intransitVisible: false, // 是否有在途单的弹窗
    intransitInfo: {},
    intransitMethod: false, // 在途单方式进入支付
    submitLoading: false, // 按钮loading
    item: {}, // 传入选择图片的信息
    loading: false,
    ModelCanncelDisplay: false, // modal请求未结束不可关闭
    PayMethodHTML: '',
    isdisabled: true, // 按钮不可以
    radioValue: {}, // 选中的卡片
    price: true,
    transitionIsEndVisible: false,
    delOrdPrompt: false,
  };

  PayMethodHTML = PayMethodHTML => {
    this.setState({
      PayMethodHTML,
    });
    if (!PayMethodHTML.startsWith('http')) {
      setTimeout(() => {
        document.forms[0].target = 'target';
        document.forms[0].submit();
      }, 200);
    }
  };

  filterInTransit = res => {
    const { dispatch, list } = this.props;
    const newList = this.flatten(list);
    const goodsCode = get(res, 'extParam.offerId');
    if (goodsCode) {
      const item = newList.find(promoItem => get(promoItem, 'relatedOfferId') === parseInt(goodsCode, 10));
      dispatch({
        type: 'promo/updateSelectPromo',
        payload: item,
      });
    }
  };

  setPromoPacked = () => {
    const { dispatch, list } = this.props;
    const { radioValue } = this.state;
    const newList = this.flatten(list);
    const item = newList.find(promoItem => get(promoItem, 'relatedOfferId') === radioValue);
    dispatch({
      type: 'promo/updateSelectPromo',
      payload: item,
    });
  };

  // 选择包 提交
  buyPromoSubmit = () => {
    const { dispatch, promoType, category } = this.props;
    this.setState({
      submitLoading: true,
    });
    const subInfo = getSubsInfo();
    // 神策埋点
    trackSensors('PromoOrderClick', {
      promo_name: get(promoType, 'offerName') || '',
      promo_id: get(promoType, 'relatedOfferId') || '',
      amount: get(promoType, 'displaySalesPrice') || '',
      category_name: category?.name,
      offer_group_tab: category?.code,
      is_guest: false,
      is_my_number: true,
      received_phone_num: get(subInfo, 'accNbr'),
    });
    // 是否有在途单  qryInTransit
    dispatch({
      type: 'promo/qryInTransit',
    }).then(res => {
      this.setState({
        submitLoading: false,
      });
      if (get(res, 'transSn') && get(res, 'orderId')) {
        this.setState({
          intransitVisible: true,
          intransitInfo: res,
          intransitMethod: true,
        });
        this.filterInTransit(res);
      } else {
        this.setPromoPacked();
        this.setState({
          visible: true,
          intransitInfo: {},
          intransitMethod: false,
        });
        // 选择包的时候，如果price为0 则直接支付
        const { price } = this.state;
        if (price) {
          this.setState({
            visible: true,
          });
        } else {
          this.setState({
            loading: true,
          });
          this.child.paymentByLoadBalance();
        }
      }
    });
  };

  flatten = arr => [].concat(...arr.map(x => (Array.isArray(x) ? this.flatten(x) : x)));

  // 选择卡片 改变radio的时候
  onRadioChange = e => {
    const { list } = this.props;
    const item = list.find(promoItem => get(promoItem, 'relatedOfferId') === e.target.value);
    this.setState({
      radioValue: e.target.value,
      isdisabled: false,
    });
    if (get(item, 'price') === 0) {
      this.setState({
        price: false,
      });
    } else {
      this.setState({
        price: true,
      });
    }
  };

  onCancel = () => {
    this.setState({
      visible: false,
    });
  };

  isInTransit = () => {
    this.setState({
      intransitVisible: false,
    });
  };

  Okay = () => {
    this.setState({
      transitionIsEndVisible: false,
    });
  };

  // 清空默认选中
  clearData = () => {
    this.setState({
      radioValue: null,
      isdisabled: true,
    });
  };

  setLoading = () => {
    this.setState({
      loading: false,
    });
  };

  closeInfo = () => {
    this.setState({
      delOrdPrompt: false,
    });
  };

  render() {
    const {
      visible,
      submitLoading,
      item, // 带入的对象
      loading,
      pamentLoading,
      ModelCanncelDisplay,
      PayMethodHTML,
      isdisabled,
      radioValue,
      intransitVisible,
      intransitInfo,
      intransitMethod,
      transitionIsEndVisible,
      delOrdPrompt,
    } = this.state;

    const { category, list } = this.props;

    return (
      <div className={styles.buyPromo}>
        {PayMethodHTML && PayMethodHTML.startsWith('http') ? (
          <div />
        ) : (
          // eslint-disable-next-line react/no-danger
          <div style={{ display: 'none' }} dangerouslySetInnerHTML={{ __html: PayMethodHTML }} />
        )}
        <Spin spinning={loading} tip="Loading...">
          {delOrdPrompt && (
            <div>
              <p className={styles.delOrdPrompt}>
                You have successfully cancelled a pending transaction. You can now purchase any of your preferred promo.
                <span
                  onClick={this.closeInfo}
                  className="icon-close"
                  style={{ color: '#2F3043', fontSize: '18px', paddingRight: '18px', float: 'right' }}
                />
              </p>
            </div>
          )}

          <div className={styles.cardwrap}>
            <Radio.Group name="radiogroup" onChange={this.onRadioChange} value={radioValue}>
              <div>
                <div className={styles.cardOffer}>
                  {!isEmpty(category?.brief) && (
                    // eslint-disable-next-line react/no-danger
                    <p className={styles.subTitle} dangerouslySetInnerHTML={{ __html: category.brief }} />
                  )}
                  {list?.map(promoItem => (
                    <Radio.Button value={get(promoItem, 'relatedOfferId')}>
                      <img src={get(promoItem, 'thumbImageUrl')} alt="" />
                    </Radio.Button>
                  ))}
                </div>
                <div className={styles.rWrap} />
              </div>
            </Radio.Group>
          </div>
          <Button
            type="primary"
            className={styles.submitOkBtn}
            onClick={this.buyPromoSubmit}
            loading={submitLoading}
            disabled={isdisabled}
          >
            Buy Promo
          </Button>
        </Spin>
        {visible && (
          <PaymentModal
            visible={visible}
            item={item}
            onCancel={() => {
              this.onCancel();
            }}
            modalRadioChange={() => {
              this.modalRadioChange();
            }}
            setLoading={() => {
              this.setLoading();
            }}
            pamentLoading={pamentLoading}
            ModelCanncelDisplay={ModelCanncelDisplay}
            PayMethodHTML={this.PayMethodHTML}
            clearData={this.clearData}
            onRef={ref => {
              this.child = ref;
            }}
            intransitMethod={intransitMethod}
            intransitInfo={intransitInfo}
          />
        )}
        <InTransitModal
          intransitVisible={intransitVisible}
          intransitInfo={intransitInfo}
          isInTransit={() => {
            this.isInTransit();
          }}
        />
        {transitionIsEndVisible && (
          <TransitionIsEnd Visible={transitionIsEndVisible} intransitInfo={intransitInfo} Okay={this.Okay} />
        )}
      </div>
    );
  }
}

export default connect(({ promo }) => ({
  promoType: promo.promoType,
  prodCatgList: promo.prodCatgList,
  offerListLimited: promo.offerListLimited,
}))(Basic);
