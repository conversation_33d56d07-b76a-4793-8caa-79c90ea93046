import React, { PureComponent } from 'react';
import { Radio, Button, Modal, Row, Col, List } from 'antd';
import { history, FormattedMessage } from 'umi';
import { connect } from 'dva';
import QRCode from 'qrcode';
import { get, isEmpty, find } from 'lodash';
import moment from 'moment';
import DITOLOGO from '@/assets/img/<EMAIL>';
import OneTimePin from '@/components/OneTimePink';
import EORFailModal from '@/components/FailModal';
import { trackSensors } from '@/utils/sensors';
import { numberFix, getSubsInfo, getUserInfo, previewBlob } from '@/utils/utils';
import { DATA_FORMAT, START_TIME, END_TIME, PARAM_FORMAT, LOADBALANCE, PAYMENT_ORDER } from '@/constants/constants';
import constants from '@/constants';
import {
  creditImg3,
  creditImg4,
  Gcash,
  GcashI,
  Grabpay,
  GrabpayI,
  PayMaya,
  PayMayaI,
  WechatI,
  Shopee,
  PointIcon,
  NweIcon,
  ComboPayIcon,
} from '@/constants/enumeration/getPicture';
import InTransitModal from '../IsInTransit/index';
import QyCodeModal from '../QyCodeModal';
import PaymentSuccess from '../PaymentSuccess/index';
import PaymentFailed from '../PaymentFailed';
import ComboPayment from '../ComboPayment';
import styles from './index.less';

class PaymentModal extends PureComponent {
  state = {
    // PayChannelId: LOADBALANCE,
    PayChannelId: '',
    isFailedModal: false, // 失败弹框
    isSuccessModal: false, // 成功弹框
    failEORModalVisible: false, // 未查询到EOR弹窗
    qyCode: false,
    qyCodeULR: '',
    isloaing: false,
    amount: '', // 支付的余额
    custOrderId: '', // 成功交易的订单号
    message: '',
    createDate: moment().format('LLL'), // 订单成功的时间
    // PayMethodName: LOADBALANCE.PayMethodName, // 选择支付方式的名称
    PayMethodName: '', // 选择支付方式的名称
    pamentLoading: false,
    codeDis: true,
    intransitVisible: false, // 是否有在途单的弹窗
    intransObj: {},
    PointPay: {},
    otpVisible: false,
    comboPayDetail: null,
    balanceChangedModalVisible: false,
    selectedCashPayment: null,
  };

  otpRef = React.createRef();

  componentDidMount = () => {
    if (this.props.onRef) {
      this.props.onRef(this);
    }
    const { dispatch } = this.props;
    this.queryPayMethod();
    dispatch({
      type: 'promo/getPointDetail',
    });
    dispatch({
      type: 'promo/pointPayFactor',
    });
    dispatch({
      type: 'global/getConfigParam',
      payload: {
        configCode: 'ecare.pto.pay.point-new-rule',
      },
    }).then(PointPaydata => {
      this.setState({
        PointPay: PointPaydata,
      });
    });
  };

  queryPayMethod = () => {
    const { dispatch, promoType } = this.props;
    dispatch({
      type: 'promo/queryPayMethod',
      payload: {
        source: 5,
        payEvent: 'setVas',
        ceeOfferNbrs: promoType?.offerNbr,
      },
    });
  };

  // 是否展示新标签
  isNewMenu = () => {
    const { PointPay } = this.state;
    const today = moment();
    const creationTime = moment(get(PointPay, 'startDate'));
    const dataCha = today.diff(creationTime, 'days', true);
    return dataCha <= Number(get(PointPay, 'showDays'));
  };

  pointPayError = () => {
    const { PointPay } = this.state;
    const { point, promoType } = this.props;
    let errorText = '';
    if (!get(PointPay, 'enabled')) {
      errorText = (
        <span style={{ position: 'relative', top: -10 }}>Sorry, This payment option is not available right now.</span>
      );
    } else if (Number(get(promoType, 'displaySalesPrice')) > Number(get(point, 'usablePoint'))) {
      errorText = (
        <span>
          Sorry, you have insufficient DITO Points.
          <br />
          <a href="https://dito.ph/loyaltyrewards">Click here</a> to know how to earn more points.
        </span>
      );
    }
    return errorText;
  };

  fetchComboPayDetail = async () => {
    const { dispatch, promoType } = this.props;
    const response = await dispatch({
      type: 'promo/comboPay',
      payload: {
        offerNbr: get(promoType, 'offerNbr'),
        // contactId: '',
        // offerGroupId: '',
        // relatedOfferId: 0, // 二次支付场景传packageHis接口返回的offerId
        // transactionId: '', // 二次支付场景传packageHis接口返回的transactionId
      },
    });
    if (response?.code === '200' && response.data) {
      this.setState({
        comboPayDetail: { ...response.data },
      });
      return;
    }
    if (response?.code === '40907510') {
      // 余额或者积分足够支付订单，弹框提醒
      this.setState({ balanceChangedModalVisible: true });
    }
  };

  // 买包支付方式
  renderPayMethodOptions = () => {
    const { PointPay, selectedCashPayment } = this.state;
    const { PayMethod, payMetDtoList, balanceInfo, promoType, point, pointFactor } = this.props;
    const { PayId } = constants;
    const payMethodOptions = [];
    const sortedPayMethod = PayMethod.filter(item => PAYMENT_ORDER.includes(item.thirdPayChannelId)).sort(
      (prev, cur) => PAYMENT_ORDER.indexOf(prev.thirdPayChannelId) - PAYMENT_ORDER.indexOf(cur.thirdPayChannelId),
    );
    sortedPayMethod.forEach(item => {
      if (item.thirdPayChannelId === PayId.Credit_DebitCard.thirdPayChannelId) {
        item.thirdPayMethodDtoList.forEach(itemdom => {
          if (itemdom.thirdPayMethodId === PayId.Credit_DebitCard.thirdPayMethodId) {
            payMethodOptions.push(
              <Radio value={PayId.Credit_DebitCard} key={PayId.Credit_DebitCard.thirdPayChannelId}>
                <span className={styles.Card}>Debit / Credit Card</span>
                {itemdom.logo ? (
                  <img src={itemdom.logo} style={{ height: 27, marginLeft: 10 }} alt="" />
                ) : (
                  <span>
                    <img style={{ width: 35, height: 27, marginLeft: 10 }} src={creditImg3} alt="" />
                    <img style={{ width: 44, height: 27, marginLeft: 10 }} src={creditImg4} alt="" />
                  </span>
                )}
              </Radio>,
            );
          }
        });
      }

      if (
        item.thirdPayChannelId === PayId.GcashQyCode.thirdPayChannelId ||
        item.thirdPayChannelId === PayId.GcashOld.thirdPayChannelId
      ) {
        item.thirdPayMethodDtoList.forEach(itemdom => {
          if (
            itemdom.thirdPayMethodId === PayId.GcashOld.thirdPayMethodId ||
            itemdom.thirdPayMethodId === PayId.GcashQyCode.thirdPayMethodId
          ) {
            payMethodOptions.push(
              <Radio
                value={
                  item.thirdPayChannelId === PayId.GcashQyCode.thirdPayChannelId ? PayId.GcashQyCode : PayId.GcashOld
                }
                key={
                  item.thirdPayChannelId === PayId.GcashQyCode.thirdPayChannelId
                    ? PayId.GcashQyCode.thirdPayChannelId
                    : PayId.GcashOld.thirdPayChannelId
                }
              >
                <img src={itemdom.logo || Gcash} alt="" />
              </Radio>,
            );
          }
        });
      }
      if (item.thirdPayChannelId === PayId.ipay88.thirdPayChannelId) {
        item.thirdPayMethodDtoList.forEach(itemdom => {
          if (itemdom.thirdPayMethodId === PayId.GrabPay.thirdPayMethodId) {
            payMethodOptions.push(
              <Radio value={PayId.GrabPay} key={PayId.GrabPay.thirdPayMethodId}>
                <img src={itemdom.logo || Grabpay} alt="" />
              </Radio>,
            );
          }
        });
      }
      if (item.thirdPayChannelId === PayId.PayMaya.thirdPayChannelId) {
        item.thirdPayMethodDtoList.forEach(itemdom => {
          if (itemdom.thirdPayMethodId === PayId.PayMayaQyCode.thirdPayMethodId) {
            payMethodOptions.push(
              <Radio value={PayId.PayMayaQyCode} key={PayId.PayMayaQyCode.thirdPayChannelId}>
                <img src={itemdom.logo || PayMaya} alt="" />
              </Radio>,
            );
          }
        });
      }
      if (item.thirdPayChannelId === PayId.WeChat.thirdPayChannelId) {
        item.thirdPayMethodDtoList.forEach(itemdom => {
          if (itemdom.thirdPayMethodId === PayId.WeChat.thirdPayMethodId) {
            payMethodOptions.push(
              <Radio value={PayId.WeChat} key={PayId.WeChat.thirdPayChannelId}>
                <img src={itemdom.logo || WechatI} alt="" style={{ width: '96px' }} />
              </Radio>,
            );
          }
        });
      }
      if (item.thirdPayChannelId === PayId.GrabPayQyCode.thirdPayChannelId) {
        item.thirdPayMethodDtoList.forEach(itemdom => {
          if (itemdom.thirdPayMethodId === PayId.GrabPayQyCode.thirdPayMethodId) {
            payMethodOptions.push(
              <Radio value={PayId.GrabPayQyCode} key={PayId.GrabPayQyCode.thirdPayMethodId}>
                <img src={itemdom.logo || Grabpay} alt="" />
              </Radio>,
            );
          }
        });
      }
      if (item.thirdPayChannelId === PayId.Shopee.thirdPayChannelId) {
        item.thirdPayMethodDtoList.forEach(itemdom => {
          if (itemdom.thirdPayMethodId === PayId.Shopee.thirdPayMethodId) {
            payMethodOptions.push(
              <Radio value={PayId.Shopee} key={PayId.Shopee.thirdPayChannelId}>
                <img src={itemdom.logo || Shopee} alt="" style={{ height: 22 }} />
              </Radio>,
            );
          }
        });
      }
    });
    const insufficientLoad = get(balanceInfo, 'amount') < (get(promoType, 'displaySalesPrice') * 100 || 0);
    payMethodOptions.push(
      <Radio
        value={LOADBALANCE}
        className={`${styles.paymentMethod} ${styles.loadMethod} ${
          get(balanceInfo, 'amount') >= (get(promoType, 'displaySalesPrice') * 100 || 0) ? styles.offset : ''
        }`}
        key={LOADBALANCE.thirdPayChannelId}
        disabled={insufficientLoad}
      >
        <img src={DITOLOGO} alt="" style={{ width: '19px', height: '19px', marginRight: '6px' }} />
        <span>
          Pay with Load Balance - <span style={{ fontWeight: 600 }}>₱ {numberFix(get(balanceInfo, 'amount'))}</span>
        </span>
        {insufficientLoad && (
          <span className={styles.tips}>
            Sorry, you have insufficient balance.
            <span
              className={styles.jumpToLoad}
              onClick={() => {
                history.push('/pto/buyLoad');
              }}
            >
              Click here to load now
            </span>
          </span>
        )}
      </Radio>,
    );
    const pointPay = find(payMetDtoList, { paymentMethodId: 9 });
    const insufficientPoint =
      Number(get(promoType, 'displaySalesPrice')) > Number(get(point, 'usablePoint')) / pointFactor;
    const isPointEnabled = get(PointPay, 'enabled');
    if (pointPay) {
      const pointError = this.pointPayError();
      payMethodOptions.push(
        <Radio
          value={PayId.PointPay}
          className={`${styles.pointMethod} ${!pointError ? styles.offset : ''}`}
          key={PayId.PointPay.PayMethodId}
          disabled={!isPointEnabled || insufficientPoint}
        >
          <img src={PointIcon} alt="" style={{ width: '19px', height: '19px', marginRight: '6px' }} />
          <span className={styles.PointPay}>
            Pay with DITO Points
            {get(PointPay, 'enabled') && (
              <span style={{ fontWeight: 600 }}>: {get(point, 'usablePoint') || '0.00'}</span>
            )}
          </span>
          {this.isNewMenu() && (
            <img src={NweIcon} alt="" style={{ width: '27px', height: '16px', marginLeft: '12px' }} />
          )}
          {pointError && (
            <>
              <br />
              <span className={styles.pointErr}>{pointError}</span>
            </>
          )}
        </Radio>,
      );
    }

    /**
     * 组合支付 Combo Pay 需要满足：
     * 1. DITO Load不足
     * 2. 积分支付方式可用
     * 3. 积分未被冻结
     * 4. 积分不足以买包
     * 5. paymentMethodDtoList 中有 `paymentMethodType === COMBO_PAY` 的记录
     */
    const comboPay = find(payMetDtoList, { paymentMethodType: 'COMBO_PAY' });
    const isComboPayAvailable =
      // insufficientLoad &&
      pointPay &&
      isPointEnabled &&
      //  && insufficientPoint
      !!comboPay;
    if (isComboPayAvailable) {
      const { comboPayDetail } = this.state;
      payMethodOptions.push(
        <>
          <Radio value={comboPay} key={comboPay.paymentMethodId}>
            <img src={comboPay.logo || ComboPayIcon} alt="" />
          </Radio>
          <ComboPayment
            pointPay={pointPay}
            comboPay={comboPay}
            comboPayDetail={comboPayDetail}
            paymentMethodDtoList={payMetDtoList}
            thirdPayChannelDtoList={PayMethod}
            selectedCashPayment={selectedCashPayment}
            onSelectCashPayment={m => this.setState({ selectedCashPayment: m })}
          />
        </>,
      );
    }
    return payMethodOptions;
  };

  // 改变支付方式
  handlePayMethod = e => {
    const { PayId } = constants;
    const thirdPayChannelId = get(e, 'target.value.thirdPayChannelId');
    if (thirdPayChannelId === PayId.Gcash.thirdPayChannelId) {
      e.target.value.PayMethodIcon = GcashI;
    } else if (thirdPayChannelId === PayId.GrabPayDirect.thirdPayChannelId) {
      e.target.value.PayMethodIcon = GrabpayI;
    } else if (thirdPayChannelId === PayId.PayMaya.thirdPayChannelId) {
      e.target.value.PayMethodIcon = PayMayaI;
    }

    this.setState({
      PayChannelId: e.target.value,
      PayMethodName: get(e, 'target.value.PayMethodName') || get(e, 'target.value.paymentMethodName'),
      codeDis: false,
    });
    if (get(e, 'target.value.paymentMethodType') === 'COMBO_PAY') {
      this.fetchComboPayDetail();
    }
  };

  // 余额充值方式
  paymentByLoadBalance = () => {
    const { dispatch, promoType, onCancel, setLoading = () => {}, records } = this.props;
    const transSn = get(records, 'transSn');
    const subInfo = getSubsInfo();
    const userInfo = getUserInfo() || {};
    // 余额支付方式改走prepay接口,余额支付不需要轮询支付结果  支付成功跳转成功页面
    const params = {
      businessType: 2,
      appCode: 'Web Selfcare',
      source: 5,
      payChannel: 'WSS',
      servEventId: 1,
      custId: get(userInfo, 'custId'),
      businessData: {
        accNbr: get(subInfo, 'accNbr'),
        offerId: get(promoType, 'relatedOfferId'), // 下单的offerId取relatedOfferId
        billFlag: true,
      },
    };
    if (transSn) {
      params.transactionId = transSn;
    }
    dispatch({
      type: 'promo/PayMethodSubmit',
      payload: params,
    }).then(res => {
      this.setState({
        pamentLoading: false,
      });
      if (get(res, 'payInfo.orderId')) {
        dispatch({
          type: 'promo/setRecordPromo',
          payload: {},
        });
        setLoading();
        onCancel();
        const { defaultAcctId, prodInstId } = subInfo;
        // 余额 取acctId的余额，balInfo直接取的余额 值一样
        this.setState({
          createDate: moment().format('LLL'),
          custOrderId: get(res, 'payInfo.orderId'),
        });
        this.queryHistory();
        dispatch({
          type: 'global/getBalance',
          payload: {
            acctId: defaultAcctId,
          },
        }).then(balance => {
          if (balance) {
            const amount = get(balance, 'amount');
            this.setState({
              amount,
              isSuccessModal: true,
              codeDis: true,
            });
          }
        });
        // 查询流量
        dispatch({
          type: 'global/glSumInfo',
          payload: {
            prodInstId,
          },
        });
      } else {
        setLoading();
        this.setState({
          isFailedModal: true,
          message:
            get(res, 'message') ||
            'Sorry, we encountered an error while processing your request. Please try again later.',
        });
      }
    });
  };

  // 其他支付方式
  otherByLoadBalance = (gcash, extra = {}) => {
    const { dispatch, clearData = () => {}, payMetDtoList, PayMethodHTML, promoType, records, onCancel } = this.props;
    const { PayChannelId, selectedCashPayment } = this.state;
    const transSn = get(records, 'transSn');
    const custOrderId = get(records, 'custOrderId');
    const subInfo = getSubsInfo() || {};
    const userInfo = getUserInfo() || {};
    let paymentInfoList = [
      {
        paymentMethodType: selectedCashPayment?.paymentMethodType,
        paymentMethodId: gcash?.thirdPayMethodId || PayChannelId.thirdPayMethodId || PayChannelId.paymentMethodId,
        paymentChannelId: gcash?.thirdPayChannelId || PayChannelId.thirdPayChannelId,
      },
    ];
    if (PayChannelId.paymentMethodType === 'COMBO_PAY') {
      const pointPay = find(payMetDtoList, { paymentMethodId: 9 });
      paymentInfoList = [
        // 第一个为积分支付
        {
          paymentMethodType: pointPay?.paymentMethodType,
          paymentMethodId: pointPay?.paymentMethodId,
        },
        {
          paymentMethodType: selectedCashPayment?.paymentMethodType,
          paymentMethodId: selectedCashPayment?.thirdPayMethodId || selectedCashPayment?.paymentMethodId,
          paymentChannelId: selectedCashPayment?.thirdPayChannelId,
        },
      ];
    }
    const params = {
      paymentInfoList,
      businessType: 2,
      appCode: 'Web Selfcare',
      source: 5,
      payChannel: 'WSS',
      servEventId: 1,
      custId: get(userInfo, 'custId'),
      businessData: {
        accNbr: get(subInfo, 'accNbr'),
        offerId: get(promoType, 'relatedOfferId'), // 下单的offerId取relatedOfferId
        payMethodId: gcash?.thirdPayMethodId || PayChannelId.thirdPayMethodId || PayChannelId.paymentMethodId, // 支付方式
        payChannelId: gcash?.thirdPayChannelId || PayChannelId.thirdPayChannelId, // 外部渠道ID
      },
      ...extra,
    };
    if (transSn && custOrderId) {
      params.businessData.origTransSn = transSn;
      params.businessData.origOrderId = custOrderId;
    }
    if (transSn) {
      params.transactionId = transSn;
    }
    return dispatch({
      type: 'promo/PayMethodSubmit',
      payload: params,
    }).then(submitData => {
      // 存储交易id  下次支付时候再次
      this.CloseQyCode();
      const isOPTError = get(submitData, 'oriCode') === 'ECARE-PTO-SERVER-00075';
      if (!isOPTError) {
        // 验证码错误由 OTP 弹窗来提示，所以不关闭OTP
        this.setState({
          pamentLoading: false,
          otpVisible: false,
        });
      }
      if (get(submitData, 'transactionId') && get(submitData, 'payInfo.orderId')) {
        dispatch({
          type: 'promo/setRecordPromo',
          payload: { transSn: get(submitData, 'transactionId'), custOrderId: get(submitData, 'payInfo.orderId') },
        });
      }
      if (get(submitData, 'payInfo.exceptionCode')) {
        // 重新查询query接口查看支付方式并且携带gcash新的id去请求接口
        this.setState({
          codeDis: true,
        });
        this.queryPayMethod();
      } else {
        if (get(submitData, 'transactionId')) {
          this.setState({
            custOrderId: get(submitData, 'payInfo.orderId'),
          });
        }
        const payInfo = get(submitData, 'payInfo');
        if (get(payInfo, 'qrCode')) {
          QRCode.toDataURL(get(payInfo, 'qrCode')).then(url => {
            this.setState({
              qyCode: true,
              qyCodeULR: url,
            });
          });
          this.Transaction(get(payInfo, 'paymentSn'));
          return submitData;
        }
        if (PayChannelId.paymentMethodId === 9 && get(payInfo, 'paymentSn')) {
          dispatch({
            type: 'promo/getPointDetail',
          });
          clearData();
          onCancel();
          this.setState({
            isSuccessModal: true,
            codeDis: true,
          });
          return submitData;
        }
        if (get(payInfo, 'urlOrHtml') && get(payInfo, 'paymentSn')) {
          if (
            get(payInfo, 'urlOrHtml').substring(0, 8) === 'https://' ||
            get(payInfo, 'urlOrHtml').substring(0, 7) === 'http://'
          ) {
            clearData();
            window.open(get(payInfo, 'urlOrHtml'));
          } else {
            PayMethodHTML(get(payInfo, 'urlOrHtml'));
          }
          this.Transaction(get(payInfo, 'paymentSn'));
        } else if (!isOPTError) {
          // 验证码错误提示由OTP自己处理
          const message = get(submitData, 'message');
          this.setState({
            isFailedModal: true,
            message: message || 'Sorry, we encountered an error while processing your request. Please try again later.',
          });
        }
        this.setState({
          isloaing: false,
        });
      }
      return submitData;
    });
  };

  // 查询积分
  queryPointDetail = payType => {
    const { dispatch, promoType } = this.props;
    const subsInfo = getSubsInfo();
    dispatch({
      type: 'promo/qryPointDetail',
      payload: {
        custId: get(subsInfo, 'cust.custId'),
        subsId: get(subsInfo, 'subsId'),
        ruleType: '4', // 4-可选包订购完成事件规则,6-充值完成事件规则,21-电渠资料首次更新事件
        channelId: '2', // app传3，web传2
        amountString: get(promoType, 'displaySalesPrice'), // 买包金额，/eshop精度
        orderSource: '2', // app传1，web传2
        offerId: get(promoType, 'relatedOfferId'), // 买包的时候必传
        payType, // 买包的时候必传，1是余额支付，2第三方支付
      },
    });
  };

  onProceed = gcash => {
    clearInterval(this.timer);
    const { PayChannelId } = this.state;
    const { dispatch, onCancel, his } = this.props;
    this.setState({
      pamentLoading: true,
    });
    if (
      his !== 'history' &&
      get(gcash, 'thirdPayMethodId') !== 511 &&
      get(gcash, 'thirdPayMethodId') !== 47 &&
      get(gcash, 'thirdPayMethodId') !== 44
    ) {
      // 从买包页面过来 查询是否有在途单  有在途单 弹出框 跳转历史页面
      dispatch({
        type: 'promo/qryInTransit',
      }).then(res => {
        dispatch({
          type: 'promo/setRecordPromo',
          payload: { transSn: get(res, 'transSn'), orderId: get(res, 'orderId') },
        });
        if (get(res, 'transSn') && get(res, 'orderId')) {
          onCancel();
          this.setState({
            intransitVisible: true,
            pamentLoading: false,
            intransObj: res,
          });
        } else if (PayChannelId.thirdPayChannelId === LOADBALANCE.thirdPayChannelId) {
          this.paymentByLoadBalance();
        } else if (
          PayChannelId.paymentMethodId === constants.PayId.PointPay.paymentMethodId ||
          PayChannelId.paymentMethodType === 'COMBO_PAY'
        ) {
          this.startOTP(gcash);
        } else {
          this.otherByLoadBalance(gcash);
        }
      });
    } else if (PayChannelId.thirdPayChannelId === LOADBALANCE.thirdPayChannelId) {
      this.paymentByLoadBalance();
    } else if (
      PayChannelId.paymentMethodId === constants.PayId.PointPay.paymentMethodId ||
      PayChannelId.paymentMethodType === 'COMBO_PAY'
    ) {
      this.startOTP(gcash);
    } else {
      this.otherByLoadBalance(gcash);
    }
  };

  CloseQyCode = () => {
    this.setState({
      qyCode: false,
    });
  };

  // 轮询
  Transaction = submitData => {
    clearInterval(this.timer);
    const { onCancel } = this.props;
    const { defaultAcctId } = getSubsInfo();
    this.setState(() => {
      this.timer = setInterval(() => {
        const { dispatch } = this.props;
        dispatch({
          type: 'promo/Transaction',
          payload: submitData,
        }).then(Transaction => {
          if (get(Transaction, 'status') === 'C') {
            dispatch({
              type: 'promo/setRecordPromo',
              payload: {},
            });
            this.queryHistory();
            dispatch({
              type: 'global/getBalance',
              payload: {
                acctId: defaultAcctId,
              },
            }).then(balance => {
              if (balance) {
                const amount = get(balance, 'amount');
                this.setState({
                  amount,
                  qyCode: false,
                  isSuccessModal: true,
                  codeDis: true,
                  createDate: moment().format('LLL'),
                });
              }
            });
            this.queryPointDetail(2);
            // 查未读消息数量
            dispatch({
              type: 'global/getCount',
            });
            clearInterval(this.timer);
            onCancel();
          }
          if (get(Transaction, 'status') === 'D' || get(Transaction, 'status') === 'E') {
            this.setState({
              isFailedModal: true,
            });
            clearInterval(this.timer);
            onCancel();
          }
          if (get(Transaction, 'status') === 'X') {
            clearInterval(this.timer);
          }
        });
      }, 3000);
    });
    setInterval(() => {
      clearInterval(this.timer);
    }, 600000);
  };

  onCancelFailed = () => {
    // 关闭失败的弹框
    const { onCancel, clearData = () => {} } = this.props;
    this.setState({
      isFailedModal: false,
      PayChannelId: '',
    });
    onCancel();
    clearData();
  };

  onBtnTryAgain = () => {
    const { his } = this.props;
    if (his === 'history') {
      this.onCancelFailed();
      return;
    }
    this.setState({
      isFailedModal: false,
    });
  };

  onCancel = () => {
    const { onCancel } = this.props;
    onCancel();
    clearInterval(this.timer);
    this.setState({
      PayChannelId: '',
      PayMethodName: '',
      pamentLoading: false,
      codeDis: true,
      comboPayDetail: null,
      balanceChangedModalVisible: false,
      selectedCashPayment: null,
    });
  };

  // 查询历史记录
  queryHistory = () => {
    const { dispatch } = this.props;
    const beginDate = moment().subtract(6, 'days').format(DATA_FORMAT);
    const endDate = moment().format(DATA_FORMAT);
    const subInfo = getSubsInfo();
    dispatch({
      type: 'promo/qryRechargeHis',
      payload: {
        prodInstId: get(subInfo, 'subsId'),
        beginDate: moment(beginDate).format(PARAM_FORMAT) + START_TIME,
        startDate: moment(beginDate).format(PARAM_FORMAT) + START_TIME,
        endDate: moment(endDate).format(PARAM_FORMAT) + END_TIME,
        pageSize: 10,
        pageIndex: 1,
      },
    }).then(response => {
      if (!isEmpty(response)) {
        const data = get(response, 'packageHisList');
        dispatch({
          type: 'promo/updateRechargeHisDataSource',
          payload: data,
        });
      }
    });
  };

  onBtnDone = () => {
    const { clearData = () => {} } = this.props;
    this.setState({
      isSuccessModal: false,
      PayChannelId: '',
      PayMethodName: '', // 选择支付方式的名称
    });
    clearData(); // 清除历史
  };

  junpTransactionHistory = () => {
    const { clearData = () => {}, dispatch } = this.props;
    clearData(); // 清除历史
    this.setState({
      isSuccessModal: false,
      PayChannelId: '',
      PayMethodName: '', // 选择支付方式的名称
    });

    dispatch({
      type: 'promo/TabActiveKey',
      payload: 'TransactionHistory',
    });
    // 查询历史
    this.queryHistory();
  };

  clearOrder = payInfo => {
    const { dispatch } = this.props;
    const { custOrderId } = this.state;
    if (this.timer && custOrderId) {
      // 清除定时器
      clearInterval(this.timer);
      // 回退订单
      dispatch({
        type: 'global/paymentCancel',
        payload: {
          paymentReqId: custOrderId,
        },
      }).then(res => {
        if (res) {
          this.onProceed(payInfo);
        }
      });
    }
  };

  otherWebPay = PayChannelId => {
    const { PayId } = constants;
    this.setState({
      isloaing: true,
    });
    if (PayChannelId.thirdPayChannelId === PayId.Gcash.thirdPayChannelId) {
      this.clearOrder(PayId.Gcash);
    } else if (PayChannelId.thirdPayChannelId === PayId.GrabPayDirect.thirdPayChannelId) {
      this.clearOrder(PayId.GrabPayDirect);
    } else {
      this.clearOrder(PayId.PayMaya);
      // this.onProceed(PayId.PayMaya);
    }
  };

  isInTransit = () => {
    this.setState({
      intransitVisible: false,
    });
  };

  // 开始 OTP 验证
  startOTP = gcash => {
    // 将 gcash 保存（虽然不知道是什么东西），供 otherByLoadBalance 使用
    this.gcash = gcash;
    this.setState({
      otpVisible: true,
    });
  };

  // 发送验证码
  sendCode = () => {
    const accNbr = get(getSubsInfo(), 'accNbr');
    return this.props.dispatch({
      type: 'promo/sendSMSCode',
      payload: {
        accNbr,
        type: 14, // 买包积分支付
      },
    });
  };

  // OTP 校验提交
  handleOTPSubmit = otp => this.otherByLoadBalance(this.gcash, { otp });

  handleViewReceipt = () => {
    const { custOrderId } = this.state;
    this.props
      .dispatch({
        type: 'global/downloadEOR',
        payload: {
          transSN: custOrderId,
          transType: 1, // 1 为买包
          accNbr: get(getSubsInfo(), 'accNbr'),
        },
      })
      .then(result => {
        if (!result) {
          this.setState({
            failEORModalVisible: true,
          });
          return;
        }
        previewBlob(result);
      })
      .catch(() =>
        this.setState({
          failEORModalVisible: true,
        }),
      )
      .finally(() => {
        this.onBtnDone();
      });
  };

  handleConfirmBalanceChangedModal = () => {
    const { defaultAcctId } = getSubsInfo();
    const { dispatch } = this.props;
    this.setState({
      PayChannelId: '',
      PayMethodName: '',
      codeDis: true,
    });
    this.queryPayMethod();
    dispatch({
      type: 'global/getBalance',
      payload: {
        acctId: defaultAcctId,
      },
    });
    dispatch({
      type: 'promo/getPointDetail',
    });
    this.setState({ balanceChangedModalVisible: false });
  };

  render() {
    const { visible, item, promoType, PayMethod, inputNumberConfig } = this.props;
    const payMethodOptions = this.renderPayMethodOptions();
    const {
      PayChannelId,
      qyCode,
      qyCodeULR,
      isloaing,
      isFailedModal,
      isSuccessModal,
      failEORModalVisible,
      custOrderId,
      amount,
      createDate,
      message,
      PayMethodName,
      pamentLoading,
      codeDis,
      intransitVisible,
      intransObj,
      otpVisible,
      balanceChangedModalVisible,
    } = this.state;

    return (
      <div>
        <Modal centered className={styles.PaymentModal} visible={visible} closable={false} footer={null}>
          <Row>
            <Col span={16}>
              <div className={styles.leftPayment}>
                <p className={styles.PaymentTitle}>Confirmation and Payment</p>
                <p className={styles.PaymentTips}>
                  You are about to purchase&nbsp;
                  <span>{get(promoType, 'offerName')}</span>
                  {/* &nbsp;for&nbsp;<span>{`0${get(subInfo, 'accNbr')}`}</span>. */}
                </p>
                {/* <img src={get(promoType, 'imageUrl')} alt="" className={styles.pickImg} /> */}
                <p className={styles.PaymentTipsI}>Select your payment method</p>
                <Radio.Group className={styles.radioGroup} onChange={this.handlePayMethod} value={PayChannelId}>
                  {payMethodOptions.map(radioItem => radioItem)}
                </Radio.Group>
                <div className={styles.Btns}>
                  <Button className={styles.cannel} onClick={this.onCancel}>
                    Close
                  </Button>
                  <Button
                    type="primary"
                    onClick={e => {
                      const number = getSubsInfo()?.accNbr;
                      trackSensors('PromoOrderSubmit', {
                        promo_name: get(promoType, 'offerName') || '',
                        promo_id: get(promoType, 'relatedOfferId') || '',
                        promo_code: get(promoType, 'offerCode') || '',
                        amount: get(promoType, 'displaySalesPrice') || '',
                        pay_method: get(PayChannelId, 'PayMethodName') || '',
                        purchase_options: 'One-Time Promo Purchase',
                        received_phone_num: number ? `${inputNumberConfig?.fixed || ''}${number}` : '',
                        is_my_number: true,
                        is_guest: false,
                      });
                      this.onProceed(e);
                    }}
                    loading={pamentLoading}
                    disabled={codeDis}
                  >
                    Proceed
                  </Button>
                </div>
              </div>
            </Col>
            <Col span={8}>
              <div className={styles.rightPayment}>
                <List className={styles.list}>
                  <List.Item className={styles.listTitle}>
                    <span>Items</span>
                    <span>Price</span>
                  </List.Item>
                  <div className={styles.PurchasePirceBox}>
                    <List.Item className={styles.PurchasePirce} key={get(promoType, 'relatedOfferId')}>
                      <span>{get(promoType, 'offerName')}</span>
                      <span>₱ {get(promoType, 'displaySalesPrice')}</span>
                    </List.Item>
                  </div>
                </List>
                <List className={styles.Totalprice}>
                  <List.Item>
                    <span className={styles.priceTips}>Subtotal</span>
                    <span className={styles.priceNub}>₱ {get(promoType, 'displaySalesPrice')}</span>
                  </List.Item>
                  <List.Item className={styles.TotalBox}>
                    <span className={styles.Total}>Total</span>
                    <span className={styles.TotalNub}>₱ {get(promoType, 'displaySalesPrice')}</span>
                  </List.Item>
                </List>
              </div>
            </Col>
          </Row>
        </Modal>
        {otpVisible && (
          <OneTimePin
            ref={this.otpRef}
            visible={otpVisible}
            tips="We've sent you an SMS Code to your DITO Mobile Number. Please input the 6 Digit SMS Code below."
            onSend={this.sendCode}
            onSubmit={this.handleOTPSubmit}
            onClose={() => {
              this.setState({
                otpVisible: false,
                pamentLoading: false,
              });
            }}
          />
        )}
        <QyCodeModal
          PayChannelId={PayChannelId}
          isQyCode={qyCode}
          qyCodeULR={qyCodeULR}
          isloaing={isloaing}
          paymethod={PayMethod}
          otherWebPay={() => {
            this.otherWebPay(PayChannelId);
          }}
          onCloseQyCode={this.CloseQyCode}
        />
        <PaymentFailed
          isFailedModal={isFailedModal}
          onBtnClose={this.onCancelFailed}
          onBtnTryAgain={() => {
            this.onBtnTryAgain();
          }}
          message={message}
        />
        {isSuccessModal && (
          <PaymentSuccess
            isSuccessModal={isSuccessModal}
            junpTransactionHistory={this.junpTransactionHistory}
            onBtnDone={() => {
              this.onBtnDone();
            }}
            junpPayment={this.junpPayment}
            successItem={item}
            custOrderId={custOrderId}
            amount={amount}
            createDate={createDate}
            PayMethodName={PayMethodName}
            PayChannelId={PayChannelId}
            onViewReceipt={this.handleViewReceipt}
          />
        )}
        <EORFailModal
          visible={failEORModalVisible}
          content="There seems to be a problem with the system or network.Please try again later after 5 minutes."
          onCancel={() => {
            this.setState({
              failEORModalVisible: false,
            });
          }}
        />
        <InTransitModal
          intransitVisible={intransitVisible}
          intransitInfo={intransObj}
          isInTransit={() => {
            this.isInTransit();
          }}
        />
        <Modal
          centered
          className={styles.balanceChangedModal}
          width={327}
          open={balanceChangedModalVisible}
          footer={null}
        >
          <h3>
            <FormattedMessage id="tips_buy_promo_combo_pay_oops" />
          </h3>
          <p>
            <FormattedMessage id="tips_buy_promo_combo_pay_change_1" />
          </p>
          <p>
            <FormattedMessage id="tips_buy_promo_combo_pay_change_2" />
          </p>
          <Button type="primary" onClick={this.handleConfirmBalanceChangedModal}>
            <FormattedMessage id="btn_buy_promo_combo_pay_change" />
          </Button>
        </Modal>
      </div>
    );
  }
}
export default connect(({ promo, global }) => ({
  balanceInfo: global.balanceInfo,
  PayMethod: promo.PayMethod,
  payMetDtoList: promo.payMetDtoList,
  configTypeList: global.configTypeList,
  promoType: promo.promoType, // 选择了哪个包
  records: promo.records,
  pointFactor: promo.pointFactor,
  point: promo.point,
  inputNumberConfig: promo.inputNumberConfig,
}))(PaymentModal);
