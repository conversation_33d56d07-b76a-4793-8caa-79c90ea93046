import React, { useState } from 'react';
import { Input, Modal } from 'antd';
import { isEmpty } from 'lodash';
import { FormattedMessage } from 'umi';
import { InfoCircleOutlined } from '@ant-design/icons';

import constants from '@/constants';
import { PAYMENT_ORDER } from '@/constants/constants';
import { useDeepCompareEffect, useIntl, useModal } from '@/hooks';
import { ArrowDownCaret } from '@/components/Icon/ArrowDown';
import { ArrowRightIOS } from '@/components/Icon/ArrowRight';
import styles from './index.less';

const { PayId } = constants;

const getThirdPaymentMethodList = thirdPayChannelDtoList => {
  const third = [];
  const sortedPayMethod = thirdPayChannelDtoList
    .filter(item => PAYMENT_ORDER.includes(item.thirdPayChannelId))
    .sort((prev, cur) => PAYMENT_ORDER.indexOf(prev.thirdPayChannelId) - PAYMENT_ORDER.indexOf(cur.thirdPayChannelId));
  sortedPayMethod.forEach(item => {
    if (isEmpty(item.thirdPayMethodDtoList)) return;
    if (item.thirdPayChannelId === PayId.Credit_DebitCard.thirdPayChannelId) {
      item.thirdPayMethodDtoList.forEach(itemdom => {
        if (itemdom.thirdPayMethodId === PayId.Credit_DebitCard.thirdPayMethodId) {
          third.push({ ...itemdom, thirdPayChannelId: item.thirdPayChannelId });
        }
      });
    }
    if (
      item.thirdPayChannelId === PayId.GcashQyCode.thirdPayChannelId ||
      item.thirdPayChannelId === PayId.GcashOld.thirdPayChannelId
    ) {
      item.thirdPayMethodDtoList.forEach(itemdom => {
        if (
          itemdom.thirdPayMethodId === PayId.GcashOld.thirdPayMethodId ||
          itemdom.thirdPayMethodId === PayId.GcashQyCode.thirdPayMethodId
        ) {
          third.push({ ...itemdom, thirdPayChannelId: item.thirdPayChannelId });
        }
      });
    }
    if (item.thirdPayChannelId === PayId.ipay88.thirdPayChannelId) {
      item.thirdPayMethodDtoList.forEach(itemdom => {
        if (itemdom.thirdPayMethodId === PayId.GrabPay.thirdPayMethodId) {
          third.push({ ...itemdom, thirdPayChannelId: item.thirdPayChannelId });
        }
      });
    }
    if (item.thirdPayChannelId === PayId.PayMaya.thirdPayChannelId) {
      item.thirdPayMethodDtoList.forEach(itemdom => {
        if (itemdom.thirdPayMethodId === PayId.PayMayaQyCode.thirdPayMethodId) {
          third.push({ ...itemdom, thirdPayChannelId: item.thirdPayChannelId });
        }
      });
    }
    if (item.thirdPayChannelId === PayId.WeChat.thirdPayChannelId) {
      item.thirdPayMethodDtoList.forEach(itemdom => {
        if (itemdom.thirdPayMethodId === PayId.WeChat.thirdPayMethodId) {
          third.push({ ...itemdom, thirdPayChannelId: item.thirdPayChannelId });
        }
      });
    }
    if (item.thirdPayChannelId === PayId.GrabPayQyCode.thirdPayChannelId) {
      item.thirdPayMethodDtoList.forEach(itemdom => {
        if (itemdom.thirdPayMethodId === PayId.GrabPayQyCode.thirdPayMethodId) {
          third.push({ ...itemdom, thirdPayChannelId: item.thirdPayChannelId });
        }
      });
    }
    if (item.thirdPayChannelId === PayId.Shopee.thirdPayChannelId) {
      item.thirdPayMethodDtoList.forEach(itemdom => {
        if (itemdom.thirdPayMethodId === PayId.Shopee.thirdPayMethodId) {
          third.push({ ...itemdom, thirdPayChannelId: item.thirdPayChannelId });
        }
      });
    }
  });
  return third;
};

export default function ComboPayment({
  pointPay,
  comboPay,
  paymentMethodDtoList = [],
  thirdPayChannelDtoList = [],
  comboPayDetail,
  selectedCashPayment,
  onSelectCashPayment,
}) {
  const { t } = useIntl();
  const [paymentMethodList, setPaymentMethodList] = useState([]);
  const { modalProps, show, hide } = useModal();

  useDeepCompareEffect(() => {
    const balancePay = paymentMethodDtoList?.find(m => m.paymentMethodType === 'B');
    let list = [];
    if (balancePay) {
      list.push(balancePay);
    }
    list = list.concat(getThirdPaymentMethodList(thirdPayChannelDtoList || []));
    setPaymentMethodList(list);
  }, [paymentMethodDtoList, thirdPayChannelDtoList]);

  return (
    <>
      <div className={styles.comboPay}>
        <div className={styles.name}>{comboPay.paymentMethodName}</div>
        <div className={styles.remark}>{comboPay.remarks}</div>
        {comboPayDetail && (
          <div className={styles.detail}>
            <div>
              <div>
                <span className={styles.name}>{pointPay?.paymentMethodName}</span>
                <span>（{comboPayDetail?.pointPayPercentage}%）</span>
              </div>
              <div>
                <span>
                  <FormattedMessage id="tips_pay_bill_available_points" />
                </span>
                <span className={styles.italic}>{comboPayDetail?.displayPointPayAmount}</span>
              </div>
            </div>
            <div className={styles.plus}>+</div>
            <div>
              <div>
                <span className={styles.name}>
                  <FormattedMessage id="tips_buy_promo_cash" />
                </span>
                <span>（{comboPayDetail?.otherPayPercentage}%）</span>
              </div>
              <div className={styles.italic}>
                <FormattedMessage
                  id="tips_buy_promo_cash_desc"
                  values={{
                    paymentAmount: `${comboPayDetail?.currencySymbol || ''}${
                      comboPayDetail?.displayOtherPayAmount || '--'
                    }`,
                  }}
                />
              </div>
              <div className={styles.ruleDesc}>
                <InfoCircleOutlined style={{ marginRight: 6, fontSize: 16 }} />
                <span>
                  <FormattedMessage id="tips_buy_promo_cash_rule_desc" />
                </span>
              </div>
            </div>
            <Input
              readOnly
              value={selectedCashPayment?.thirdPayMethodName}
              placeholder="Select here"
              style={{ width: 343 }}
              suffix={<ArrowDownCaret style={{ color: '#0038A7' }} />}
              onClick={show}
            />
          </div>
        )}
      </div>
      <Modal {...modalProps} centered width={375} className={styles.modal} footer={null}>
        <h5>{t('tips_buy_promo_choose_other_method')}</h5>
        <ul>
          {paymentMethodList.map(m => {
            const isBalancePay = m.paymentMethodType === 'B';
            const balanceEnable = comboPayDetail?.balanceEnable;
            const insufficientBalance = isBalancePay && !balanceEnable;
            return (
              <li
                key={m.thirdPayMethodId}
                className={insufficientBalance ? styles.insufficient : ''}
                onClick={() => {
                  if (insufficientBalance) return;
                  onSelectCashPayment?.(m);
                  hide();
                }}
              >
                <span>{m.thirdPayMethodName || m.paymentMethodName}</span>
                <span className={styles.right}>
                  {insufficientBalance && (
                    <span className={styles.danger}>{t('tips_buy_promo_insufficient_balance')}</span>
                  )}
                  <ArrowRightIOS style={{ fontSize: 16, color: '#0038A7' }} />
                </span>
              </li>
            );
          })}
        </ul>
      </Modal>
    </>
  );
}
