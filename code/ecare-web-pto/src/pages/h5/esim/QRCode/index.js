import React, { useRef, useState } from 'react';
import { Button, Toast } from 'antd-mobile5';
import classNames from 'classnames';
import html2canvas from 'html2canvas';
import QRCode from 'qrcode';
import { useDispatch, useLocation } from 'umi';

import { YES } from '@/constants/constants';
import bridge from '@/utils/bridge';
import { desensitizeNbr, formatNbr, safeJSONParse } from '@/utils/utils';
import { useIntl, useLoading, useMount, useNumberRule, useUserInfo } from '@/hooks';
import H5Layout from '@/layouts/H5Layout';
import Refresh from '@/components/Icon/Refrech';
import Icon from '@/components/Icon';

import logo from '@/assets/img/newDito.png';
import downloadIcon from '@/assets/img/point/<EMAIL>';
import logoSmall from '@/pages/h5/assets/icon_dito_logo_colorful.png';
import styles from './index.less';

const Ring = () => (
  <svg viewBox="0 0 80 80" width="2em" height="2em">
    <circle cx="40" cy="40" r="32" stroke="black" strokeOpacity="0.1" fill="transparent" strokeWidth="8" />
    {/* <circle className="circle" cx="40" cy="40" r="32" stroke="#CE1126" fill="transparent" strokeLinecap="round" strokeWidth="4" strokeDashoffset="404" strokeDasharray="202" /> */}
    <circle
      cx="40"
      cy="40"
      r="32"
      stroke="#CE1126"
      fill="transparent"
      strokeLinecap="round"
      strokeWidth="8"
      strokeDashoffset="180"
      strokeDasharray="202"
    />
  </svg>
);

export default function ESIMQRCode() {
  const { t } = useIntl();
  const dispatch = useDispatch();
  const { query } = useLocation();
  const [qrcodeURI, setQrcodeURI] = useState('');
  const snapshotRef = useRef(null);
  const { fixed: numberPrefix, split } = useNumberRule();
  const { accNbr } = useUserInfo(null, { forceUpdateRole: false });

  const loading = useLoading('esim/inventorySIMCard');

  const inventorySIMCard = async () => {
    if (loading) return;
    const result = await dispatch({ type: 'esim/inventorySIMCard' });
    if (result?.data?.simCard?.esimProfileURI) {
      try {
        const url = await QRCode.toDataURL(result.data.simCard.esimProfileURI, { margin: 0, width: 200, height: 200 });
        setQrcodeURI(url);
      } catch (error) {
        setQrcodeURI('');
        Toast.show('Generate qrcode failed');
        // eslint-disable-next-line no-console
        console.error('generate qrcode failed', error);
      }
    }
  };

  const handleSnapshot = async () => {
    const canvas = await html2canvas(snapshotRef.current, {
      scale: 3, // 缩放比例,默认为1
      allowTaint: true, // 是否允许跨域图像污染画布
      useCORS: true, // 是否尝试使用CORS从服务器加载图像
      scrollX: -window.scrollX,
      scrollY: -window.scrollY,
      windowWidth: document.documentElement.offsetWidth,
      windowHeight: document.documentElement.offsetHeight,
    });
    const imgUri = canvas.toDataURL('image/png');
    const jsonString = JSON.stringify({ base64: imgUri });
    const res = bridge.downloadFile(jsonString);
    const resJson = safeJSONParse(res);
    const { code } = resJson;
    if (+code === 200) {
      Toast.show('Download success');
      return;
    }
    Toast.show({
      content: (
        <>
          <p>Fail to download photo,</p>
          <p>please try again</p>
        </>
      ),
    });
  };

  useMount(inventorySIMCard);

  return (
    <H5Layout
      headerProps={{
        background: 'white',
        bordered: true,
        round: false,
        goToHomeWhenBack: query?.backHome === YES,
        backByPlatform: query?.backHome === YES,
      }}
    >
      <div ref={snapshotRef} className={styles.container}>
        <img className={styles.logo} src={logo} alt="" />
        <div className={styles.main}>
          {qrcodeURI && <div className={styles.title}>{t('tips_esim_replace_qr_scan')}</div>}
          <div className={styles.desc}>
            {t('tips_esim_replace_success_number')}: {numberPrefix}
            {formatNbr(desensitizeNbr(accNbr, 'x'), split)}
          </div>
          <div className={styles.qrcode}>
            {qrcodeURI ? (
              <>
                <img className={styles.qrcodeImg} src={qrcodeURI} alt="QR Code" />
                <img className={styles.watermark} src={logoSmall} alt="" />
              </>
            ) : (
              <div className={styles.placeholder}>
                <div>
                  <Ring />
                  <div className={styles.tip}>{t('tips_esim_replace_qr_loading')}</div>
                  <Button className={styles.refreshBtn} size="mini" color="primary" onClick={inventorySIMCard}>
                    <Refresh className={classNames({ [styles.rotate]: loading })} style={{ marginRight: '0.08rem' }} />
                    {t('btn_esim_replace_qr_refresh')}
                  </Button>
                </div>
              </div>
            )}
          </div>
          {qrcodeURI && (
            <div className={styles.saveBtn} onClick={handleSnapshot}>
              <Icon src={downloadIcon} size="0.16rem" style={{ marginRight: '0.08rem' }} />
              <span>{t('btn_esim_replace_success_copy')}</span>
            </div>
          )}
        </div>
      </div>
    </H5Layout>
  );
}
