/**
 * @description H5 买包成功页
 * @path /h5/success
 */
import React, { useEffect, useState } from 'react';
import moment from 'moment';
import { history, useDispatch, useLocation, useIntl as useIntlFromUmi } from 'umi';
import Draggable from 'react-draggable';
import { isEmpty } from 'lodash';
import { Button, Modal as ModalAntd } from 'antd';
import classNames from 'classnames';
import { HISTORY_FORMAT, NO, NUMBER_FORMATTER_3_3_4, PromoType, Role, YES } from '@/constants/constants';
import { MENU_SWITCH } from '@/constants/config';
import bridge, { appInfo, inApp } from '@/utils/bridge';
import { trackSensors } from '@/utils/appsensors';
import { formatNbr, safeJSONParse, compareVersion, getOSInfo } from '@/utils/utils';
import { EnventCode, toAppRating } from '@/utils/ratingBridge';
// eslint-disable-next-line no-redeclare
import { useConfig, useIntl, useLoading, useMount, useNumberRule, useUserInfo } from '@/hooks';
import H5Layout from '@/layouts/H5Layout';
import { BaseSpin } from '@/components';
import { CheckedRipple } from '@/components/Icon/CheckedRipple';
import ChatWithUs from '@/components/Icon/ChatWithUs';
import Share from '@/components/Icon/Share';
import FileTicket from '@/components/Icon/FileTicket';
import History from '@/components/Icon/History';
import Modal from '../components/Modal';
import Footer from '../components/Footer';
import { createShareInfo } from '../AutoPayPromoDetail';
import Points from './Points';

import receiptIcon from '@/pages/h5/assets/icon_receipt.png';
import closeImg from '@/assets/img/Frame.svg';
import styles from './index.less';

const TRANSACTION_TYPE = {
  AUTO_PAY: 'Buy Promo with DITO Auto Pay',
  PAY_IN_ADVANCE: 'Buy Promo with Super Saver',
};
const PURCHASE_OPTIONS = {
  AUTO_PAY: 'Auto Pay',
  PAY_IN_ADVANCE: 'Advance Pay',
};

moment.locale('en');
const NOW = moment().format(HISTORY_FORMAT);

const pickRoleFromCatgCode = parentCatgCode => {
  if (typeof parentCatgCode !== 'string') return '';
  return parentCatgCode.split('_').pop() || '';
};

const Success = () => {
  const { t } = useIntl();
  const dispatch = useDispatch();
  const { query = {} } = useLocation();
  const [shareable, setShareable] = useState(true); // 分享按钮节流
  const [noReceiptTipShow, setNoReceiptTipShow] = useState(false);
  const [showViewReceipt, setShowViewReceipt] = useState(false);
  const [transactionDetail, setTransactionDetail] = useState(null);
  const [pointDetail, setPoint] = useState();
  const [orderFeeInfo, setOrderFeeInfo] = useState(null); // 订单付费信息
  const [adList, setAdList] = useState([]); // 广告列表
  const [adPopUp, setAdPopUp] = useState(false); // 弹框状态
  const [adFloatingPopUp, setFloatingAdPopUp] = useState(false); // 悬浮窗状态
  const [curAdPopUpItem, setCurAdPopUpItem] = useState({}); // 广告弹窗
  const [curAdFloatingItem, setCurAdFloatingItem] = useState({}); // 悬浮窗
  const [imgWidth, setImgWidth] = useState('auto');
  const { version } = appInfo;
  const { isPhone } = getOSInfo();

  const handleImageLoad = event => {
    const { naturalWidth } = event.target;
    setImgWidth(naturalWidth / 2);
  };

  const intl = useIntlFromUmi();

  const loading = useLoading([
    'h5Success/queryPoint',
    'global/checkEOR',
    'global/downloadEOR',
    'h5Success/fetchPromoOrderDetail',
  ]);

  const { accNbr, offerName, promoType, custOrderId, payType, receivedPhoneNum, contactId, paymentInfoList } = transactionDetail || {};
  const queryPoint = payload => dispatch({ type: 'h5Success/queryPoint', payload });
  const fetchPromoOrderDetail = payload => dispatch({ type: 'h5Success/fetchPromoOrderDetail', payload });

  const { token, accNbr: loginNbr, role, isGeneralPostpaid } = useUserInfo(({ token: authToken, subsId, custId }) => {
    let detail = { ...query };
    if (!detail.custOrderId) {
      // query 为空说明是从原生跳转过来
      detail = safeJSONParse(bridge.getSuccessInfo());
    }
    setTransactionDetail(detail);
    const payTypeStr = String(detail?.payType);
    // 第三方支付才需要展示获取到的积分
    if (payTypeStr === '2') {
      // 查询赚取的积分
      queryPoint({
        custId,
        subsId,
        channelId: '3', // app传3，web传2
        orderSource: '3',
        ruleType: '4', // 4-可选包订购完成事件规则,6-充值完成事件规则,21-电渠资料首次更新事件
        msgId: detail?.transSn,
        custOrderId: detail?.custOrderId,
        amount: detail?.amount,
        // 兼容 iOS 埋的坑
        offerId: detail?.offerId === '0' ? undefined : detail?.offerId, // 买包的时候必传
        payType: payTypeStr, // 买包的时候必传，1是余额支付，2第三方支付，3积分支付
        referenceId: detail?.referenceId,
        'Auth-Token': authToken,
      }).then(res => {
        if (!res) return;
        setPoint(res);
      });
    }
    // 查询订单扣费信息
    fetchPromoOrderDetail({ custOrderId: detail?.custOrderId, 'Auth-Token': authToken }).then(res => {
      if (!res) return;
      setOrderFeeInfo(res);
    });
    if (authToken) {
      dispatch({
        type: 'global/checkEOR',
        payload: {
          custOrderId: detail?.custOrderId,
          payMethod: payTypeStr,
          isMCCM: promoType === PromoType.MCCM || !!detail?.contactId ? YES : NO,
          'Auth-Token': authToken,
        },
      }).then(res => {
        setShowViewReceipt(res?.data?.result);
      });
    }
  });

  let isMyNumber = !!token;
  if (receivedPhoneNum && loginNbr) {
    isMyNumber = receivedPhoneNum === loginNbr;
  } else if (accNbr && loginNbr) {
    isMyNumber = accNbr === loginNbr;
  }

  const qryMktAd = () => {
    // eslint-disable-next-line no-shadow
    const { token } = bridge.getCurrentUser();
    dispatch({
      type: 'h5Success/fetchMktAdList',
      payload: {
        authToken: token,
      },
    }).then(res => {
      if (res?.code === '200' && !isEmpty(res?.data?.mktAdList)) {
        setAdList(res.data?.mktAdList);
      }
    });
  };

  const isGuest = !token; // 游客支付成功场景
  const isFree = !orderFeeInfo; // 免费包

  const isShowAdPopup = () => {
    if (!isPhone) return false;
    const temp = compareVersion(version, '2.11.21') > -1;
    return !temp;
  };

  useEffect(() => {
    if (!transactionDetail) return;
    if (isMyNumber && isShowAdPopup()) qryMktAd();
    // 神策埋点
    const payTypeStr = String(transactionDetail?.payType);
    let paymentName;
    switch (payTypeStr) {
      case '1':
        paymentName = 'Load';
        break;
      case '2':
        // eslint-disable-next-line
        paymentName = transactionDetail?.paymentName;
        break;
      case '3':
        paymentName = 'DITO Points';
        break;
      default:
        paymentName = '';
        break;
    }
    trackSensors('PromoOrderResult', {
      role,
      entrance: transactionDetail?.sharedData?.entrance || '',
      transaction_id: transactionDetail?.custOrderId,
      promo_name: transactionDetail?.offerName,
      promo_id: transactionDetail?.offerId,
      pay_method: paymentName,
      purchase_options: PURCHASE_OPTIONS[transactionDetail?.promoType] || 'One-Time Promo Purchase',
      batch_id: transactionDetail?.batchId,
      contact_id: transactionDetail?.contactId,
      campaign_code: transactionDetail?.sharedData?.campaignCode,
      is_guest: isGuest,
      received_phone_num: receivedPhoneNum || accNbr,
      is_my_number: isMyNumber,
      promo_code: transactionDetail?.promoCode,
    });
    // 谷歌埋点
    bridge.trackEvent?.({
      channel: 'GA',
      event: 'purchase',
      payload: {
        event: 'purchase',
        event_category: transactionDetail?.categoryName,
        event_action: 'Purchase',
        event_label: offerName,
        transaction_id: transactionDetail?.custOrderId,
        currency: 'PHP',
        payment_type: paymentName,
        value: transactionDetail?.price,
        coupon: 'NA',
        items: [
          {
            item_id: transactionDetail?.offerId,
            item_name: offerName,
            item_brand: 'Dito',
            item_category: transactionDetail?.categoryName,
            currency: 'PHP',
            price: transactionDetail?.price,
            quantity: 1,
            coupon: 'NA',
          },
        ],
      },
    });
  }, [transactionDetail]);

  const menuSwitch = useConfig(MENU_SWITCH);
  const { fixed: numberPrefix, split = NUMBER_FORMATTER_3_3_4 } = useNumberRule();

  /**
   * MCCM 推送的 offer 不可分享
   *   * MCCM 推送的 auto pay 的 promoType 是 AUTO_PAY，需要根据 contactId 来判断
   * 当配置项开关关闭时不可分享
   */
  const isShareable = promoType !== PromoType.MCCM && !contactId && menuSwitch?.app?.shareOfferShow === YES && !isFree;

  const handleShare = async () => {
    if (!shareable) return;
    setShareable(false);
    const { offerNbr, relatedOfferId, categoryName, offerGroupTab } = transactionDetail || {};
    const queryParams = {
      offerNbr,
      crmOfferId: relatedOfferId || '',
      // 埋点使用
      categoryName,
      offerGroupTab,
    };
    const shareInfo = await createShareInfo(
      queryParams,
      // `${intl.formatMessage({
      //   id: 'h5_autoPayPromoDetail_share_title',
      //   defaultMessage: 'Check out this best value offer available on DITO APP!',
      // })}\n${offerName || ''}`,
    );
    const trackData = {
      promoId: transactionDetail?.offerId,
      promoName: offerName,
      sharePage: 'Promo Order Success',
    };
    bridge.shareOut({ ...shareInfo, trackData });
    setShareable(true);
  };

  const goToViewHistory = () => {
    // 给他人买包固定跳转
    if (!isMyNumber) {
      bridge.jumpVCWithUrl('mydito://PromoHistory?tabIndex=2');
      return;
    }
    // 后付费买包成功页面需要根据promoType区分，如果promoType=ROAMING，则点击View History跳转到Roaming买包历史页面，否则跳转到booster history页面
    // 预付费买包成功页面需要根据promoType区分，如果promoType=ROAMING，则点击View History跳转到Roaming买包历史页面，否则跳转到promo history页面
    if (isGeneralPostpaid) {
      if (promoType === PromoType.ROAMING) {
        history.push('/h5/RoamingOffer/history?tab=Purchase');
      } else {
        bridge.jumpVCWithUrl('mydito://BoosterHistory');
      }
    } else if (role === Role.PREPAID) {
      if (promoType === PromoType.ROAMING) {
        history.push('/h5/RoamingOffer/history?tab=Purchase');
      } else {
        bridge.jumpVCWithUrl('mydito://PromoHistory');
      }
    } else {
      // 防止异常场景
      bridge.jumpVCWithUrl('mydito://PromoHistory');
    }
  };

  const handleViewReceipt = () => {
    dispatch({
      type: 'global/downloadEOR',
      payload: {
        accNbr,
        transType: 1, // 1-买包, 2-充值
        transSN: custOrderId,
        base64: true,
        headers: {
          authToken: token,
        },
      },
    })
      .then(result => {
        if (!result?.data) {
          setNoReceiptTipShow(true);
          return;
        }
        bridge.getNativePDFPreview(result);
      })
      .catch(() => setNoReceiptTipShow(true));
  };

  const handleJump = (url, needLogin = false) => {
    // needLogin 为true，需要判断是否登录，未登录跳转到登录页
    if (needLogin) {
      bridge.invokeIfLogin(() => {
        window.location.href = url;
      });
      return;
    }
    window.location.href = url;
  };

  const renderBtnText = () => {
    const message = intl.formatMessage({ id: `btn_ad_confirm_${curAdPopUpItem?.adId}` });

    const displayMessage =
      message !== `btn_ad_confirm_${curAdPopUpItem?.adId}`
        ? message
        : intl.formatMessage({ id: 'btn_ad_confirm_default', defaultMessage: 'Claim it now' });
    return displayMessage;
  };

  const renderCancelBtnText = () => {
    const message = intl.formatMessage({ id: `btn_ad_cancel_${curAdPopUpItem?.adId}` });
    const displayMessage =
      message !== `btn_ad_cancel_${curAdPopUpItem?.adId}`
        ? message
        : intl.formatMessage({ id: 'btn_ad_cancel_default', defaultMessage: "I'll claim it later" });
    return displayMessage;
  };

  useEffect(() => {
    if (isEmpty(adList)) return;
    adList.forEach(item => {
      if (item.pageUrl !== `${window.location.origin}/h5/success`) return;
      if (inApp) {
        if (item.adType === '3') {
          setCurAdFloatingItem(item);
          setFloatingAdPopUp(true);
        } else if (item.adType === '2') {
          setCurAdPopUpItem(item);
          setAdPopUp(true);
        }
      }
    });
  }, [adList]);

  const adImgClick = type => {
    let url;
    if (type === 'popUp') {
      url = curAdPopUpItem?.adPicList?.[0]?.adAppUrl || {};
      setAdPopUp(false);
    } else {
      url = curAdFloatingItem?.adPicList?.[0]?.adAppUrl || {};
      setFloatingAdPopUp(false);
    }
    if (url.startsWith('mydito://')) {
      // 使用 bridge?.jumpVCWithUrl 进行跳转
      bridge?.openNewWebview(url);
    } else if (url.startsWith('http://') || url.startsWith('https://')) {
      window.location.href = url;
      // 使用 window.location.href 进行跳转
    }
  };

  useMount(() => {
    bridge.buyPromoSuccess();
    toAppRating(EnventCode.PURCHASE_OFFER);
  });

  return (
    <BaseSpin spinning={loading}>
      <H5Layout background="#f7f7f7" headless navColor="#ce1126">
        <div className={styles.container}>
          <div className={styles.banner} />
          <div className={styles.header}>
            <CheckedRipple bgColor="white" style={{ fontSize: '0.8rem', color: '#16AA18' }} />
            <h1>{t(isFree ? 'lb_buy_free_promo_success_page_title' : 'lb_buy_promo_success_title')}</h1>
            <p
              // eslint-disable-next-line react/no-danger
              dangerouslySetInnerHTML={{
                __html: t(isFree ? 'lb_buy_free_promo_success_page_desc' : 'lb_buy_promo_success_msg', { offerName }),
              }}
            />
          </div>

          <div className={styles.main}>
            <div className={styles.orderCard}>
              <div className={styles.detail} style={isGuest ? { paddingBottom: 0, borderBottom: 'none' } : {}}>
                <div className={styles.row}>
                  <div className={styles.label}>{t('lb_trans_type')}</div>
                  <div className={styles.value}>
                    {t(
                      `tips_promo_trans_type_${
                        pickRoleFromCatgCode(transactionDetail?.sharedData?.parentCatgCode) || role || ''
                      }_${promoType || ''}`,
                      TRANSACTION_TYPE[promoType] || 'Buy Promo',
                    )}
                  </div>
                </div>
                <div className={styles.row}>
                  <div className={styles.label}>{t('lb_dito_number')}</div>
                  <div className={styles.value}>
                    {numberPrefix || ''}
                    {formatNbr(accNbr, split)}
                  </div>
                </div>
                <div className={styles.row}>
                  <div className={styles.label}>{t('lb_trans_number')}</div>
                  <div className={styles.value}>{custOrderId}</div>
                </div>
                <div className={styles.row}>
                  <div className={styles.label}>{t('lb_trans_date')}</div>
                  <div className={styles.value}>{NOW}</div>
                </div>
                <div className={styles.row}>
                  <div className={styles.label}>{t('lb_buy_promo_total_charge')}</div>
                  {!loading && isFree ? (
                    <div className={`${styles.value} ${styles.red}`}>
                      {t('lb_buy_free_promo_success_pag_total_charge')}
                    </div>
                  ) : (
                    <div className={`${styles.value} ${styles.red}`}>
                      {orderFeeInfo?.currencySymbol}
                      {orderFeeInfo?.displayAmount || orderFeeInfo?.displayTotalCharge}
                    </div>
                  )}
                </div>
                {paymentInfoList && (
                  <div className={styles.row}>
                    <div className={styles.label}>{t('lb_buy_promo_payment_method')}</div>
                    <div className={styles.value}>
                      {JSON.parse(paymentInfoList).length > 1 ? (
                        <>
                          <div className={styles.method}>{t('tips_buy_promo_combo_pay_name')}</div>
                          <div className={styles.methodName}>{JSON.parse(paymentInfoList)?.map(item => item?.paymentMethodName)?.join(' + ')}</div>
                        </>
                      ) : (
                        <div className={styles.method}>{JSON.parse(paymentInfoList)?.map(item => item?.paymentMethodName)?.join(' + ')}</div>
                      )}
                    </div>
                  </div>
                )}
                
              </div>
              {!isGuest && (
                <div
                  className={styles.entrance}
                  style={{ justifyContent: showViewReceipt && isMyNumber ? 'space-between' : 'center' }}
                >
                  <span className={styles.link} onClick={goToViewHistory}>
                    <History />
                    <span>{t('btn_view_promo_history')}</span>
                  </span>
                  {/* 给他人买包也不展示 */}
                  {showViewReceipt && isMyNumber && !isFree && (
                    <span className={styles.link} onClick={handleViewReceipt}>
                      <img src={receiptIcon} alt="" style={{ width: '0.14rem', height: '0.16rem' }} />
                      <span>{t('btn_view_receipt')}</span>
                    </span>
                  )}
                </div>
              )}
            </div>

            {/* 第三方支付且有积分才需要展示获取到的积分 */}
            {String(payType) === '2' && !!Number(pointDetail?.point) && (
              <Points data={pointDetail} isMyNumber={isMyNumber} />
            )}

            {isShareable && (
              <div className={styles.share} onClick={handleShare}>
                <Share />
                <span>Share</span>
              </div>
            )}

            <div className={styles.more}>
              <div className={styles.title}>For any concerns or inquiries:</div>
              <div className={styles.links}>
                <div onClick={() => handleJump('mydito://ChatWithUs')}>
                  <ChatWithUs />
                  <span>
                    Chat
                    <br />
                    With Us
                  </span>
                </div>
                <div onClick={() => handleJump('mydito://FileHelpTicketFWAPostPaid', true)}>
                  <FileTicket />
                  <span>
                    File a<br />
                    Help Ticket
                  </span>
                </div>
              </div>
            </div>
          </div>

          {adFloatingPopUp &&
            (curAdFloatingItem?.extData?.drageFlag === 'Y' ? (
              <Draggable>
                <img
                  className={styles.adFloatingPopUpImg}
                  onLoad={handleImageLoad}
                  style={{ width: imgWidth }}
                  src={curAdFloatingItem?.adPicList?.[0]?.adImg}
                  onClick={() => adImgClick('float')}
                  alt=""
                />
              </Draggable>
            ) : (
              <img
                className={styles.adFloatingPopUpImg}
                onLoad={handleImageLoad}
                style={{ width: imgWidth }}
                src={curAdFloatingItem?.adPicList?.[0]?.adImg}
                onClick={() => adImgClick('float')}
                alt=""
              />
            ))}
          <Footer title={t('btn_back_to_home')} onClick={() => bridge.backHome()} />

          <Modal
            centered
            visible={noReceiptTipShow}
            okText="Go to Promo History"
            onOk={goToViewHistory}
            onCancel={() => setNoReceiptTipShow(false)}
          >
            <p>Your receipt is being processed. You may check it later in your Promo History.</p>
          </Modal>

          <ModalAntd
            centered
            visible={adPopUp}
            onCancel={() => setAdPopUp(false)}
            className={classNames(styles.adModal, curAdPopUpItem?.extData?.layout !== '2' ? styles.noPadding : '')}
            maskClosable={false}
            // noPadding={curAdPopUpItem?.extData?.layout !== '2'} // 没有按钮则没有边框
            footer={null}
          >
            <div className={styles.adWrap}>
              <img
                className={styles.popUpImg}
                src={curAdPopUpItem?.adPicList?.[0]?.adImg}
                onClick={() => {
                  if (curAdPopUpItem?.extData?.layout !== '2') {
                    adImgClick('popUp');
                  }
                }}
                alt=""
              />
              {curAdPopUpItem?.extData?.layout !== '2' && (
                <img className={styles.closeImg} src={closeImg} alt="" onClick={() => setAdPopUp(false)} />
              )}
            </div>
            {curAdPopUpItem?.extData?.layout === '2' && (
              <div
                className={styles.footerWrap}
                style={{ paddingBottom: curAdPopUpItem?.extData?.layout === '2' ? '0' : '.16rem' }}
              >
                <div className={styles.primaryBtnWrap}>
                  <Button onClick={() => adImgClick('popUp')} type="primary">
                    {renderBtnText()}
                  </Button>
                </div>
                <div className={styles.cancelBtnWrap}>
                  <Button onClick={() => setAdPopUp(false)}>{renderCancelBtnText()}</Button>
                </div>
              </div>
            )}
          </ModalAntd>
        </div>
      </H5Layout>
    </BaseSpin>
  );
};

export default Success;
