.container {
  overflow: auto;
  position: relative;
  height: 100vh;
  padding-bottom: 0.92rem;
  font-family: Exo2;
  background-color: #f7f7f7;

  .banner {
    z-index: 0;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2.82rem;
    background-color: #0038a8;
    background-image: url(../assets/bg_success_header_v2.png);
    background-repeat: no-repeat;
    background-size: contain;
  }
  .header {
    z-index: 1;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 0.18rem;
    padding-top: 0.24rem;
    padding-left: 0.32rem;
    padding-right: 0.32rem;
    font-size: 0.14rem;
    font-weight: 500;
    line-height: normal;
    color: #fff;
    text-align: center;
    
    > h1 {
      margin: 0.08rem 0;
      font-size: 0.24rem;
      font-weight: 700;
      color: #fff;
    }
    :global {
      [data-type="offerName"],
      .offerName {
        display: inline-block;
        font-weight: 800;
      }
    }
  }

  .main {
    position: relative;
    z-index: 1;
    padding: 0 0.16rem;

    .orderCard {
      margin-bottom: 0.16rem;
      padding: 0.16rem;
      border-radius: 0.1rem;
      background: #fff;
      box-shadow: 0 0.05rem 0.1rem 0 rgba(0, 0, 0, 0.02);
      .detail {
        padding-bottom: 0.2rem;
        border-bottom: 0.01rem solid #eaeaea;
        .row {
          display: flex;
          justify-content: space-between;
          font-size: 0.14rem;
          font-weight: 500;
          line-height: 0.2rem;
          .value {
            font-weight: 600;
            .method {
              text-align: right;
            }
            .methodName {
              font-size: 0.12rem;
              font-weight: 500;
            }
          }
        }
        .row + .row {
          margin-top: 0.16rem;
        }
      }
  
      .entrance {
        display: flex;
        justify-content: space-between;
        padding-top: 0.2rem;
        .link {
          display: inline-flex;
          align-items: center;
          color: #0038a8;
          font-weight: 600;
          > span {
            margin-left: 0.08rem;
          }
        }
      }
    }

    .points {
      overflow: hidden;
      position: relative;
      display: flex;
      align-items: center;
      margin-bottom: 0.16rem;
      padding: 0.16rem 0;
      background-color: #0038a7;
      color: #fff;
      border-radius: 0.08rem;
      &::before,
      &::after {
        content: "";
        position: absolute;
        display: block;
        width: 1rem;
        height: 1rem;
        background-color: #030752;
        opacity: 0.2;
        border-radius: 50%;
      }
      &::before {
        bottom: -0.28rem;
        left: -0.1rem;
      }
      &::after {
        bottom: -0.3rem;
      }

      .tipIcon {
        position: absolute;
        top: 0.08rem;
        right: 0.08rem;
      }

      .giftIcon {
        position: relative;
        z-index: 2;
        margin-left: 0.26rem;
        margin-right: 0.4rem;
      }
  
      .earning {
        font-size: 0.14rem;
        font-weight: 600;
        line-height: normal;
        .count {
          display: flex;
          align-items: center;
          font-size: 0.24rem;
          font-weight: 800;
          line-height: normal;
          color: #fcd117;
          .unit {
            margin-left: 0.04rem;
            font-size: 0.12rem;
            font-weight: 700;
          }
        }
        .validUtil {
          font-size: 0.12rem;
          transform: scale(0.8333);
          transform-origin: 0 0;
          font-weight: 400;
          white-space: nowrap;
          line-height: 0.16rem;
        }
      }
    }
  
    .share {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 0.16rem;
      text-align: center;
      color: #0038a8;
      font-size: 0.14rem;
      > span {
        margin-left: 0.08rem;
        font-weight: 600;
        line-height: normal;
      }
    }

    .more {
      text-align: center;
      .title {
        margin-bottom: 0.16rem;
        font-size: 0.14rem;
        font-weight: 500;
        line-height: 0.2rem;
      }
      .links {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.24rem;
        > div {
          display: flex;
          align-items: center;
          flex-direction: column;

          &:first-child {
            margin-right: 0.56rem;
          }

          span {
            margin-top: 0.08rem;
            font-size: 0.14rem;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
          }
        }
      }
    }
  }
}

.earnTip {
  margin-bottom: 0.12rem;
  font-family: Montserrat;
  font-size: 0.14rem;
  font-weight: 600;
}

.earnSubTip {
  width: 100%;
  font-family: Montserrat;
  font-weight: 500;
  font-size: 0.12rem;
  color: #979797;
}

.red {
  color: #ce1126 !important;
}

.adWrap {
  .popUpImg {
    width: 100%;
    // background-size: contain;
  }
  .closeImg {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: -0.48rem;
  }
}
.adFloatingPopUpImg {
  // transform: scale(0.5); /* 将图片缩小到原始尺寸的一半 */
  transform-origin: top right; /* 以左上角为缩放基点 */
  display: block; /* 确保图片占据整个行 */
  margin: 0 auto;
  position: fixed;
  right: .16rem;
  top: 50%;
  transform: translateY(-25%);
  // background-Color: white;
  border-radius: 8px;
  z-index: 1000;
}

.adModal {
  width: auto !important;
  :global {
    .ant-modal-content {
      border-radius: .08rem;
      padding: .16rem;
    }
  }
}
.noPadding {
  :global {
    .ant-modal-content {
      padding: 0;
    }
  }
}
.footerWrap {
  padding: .16rem .08rem;

  .primaryBtnWrap, .cancelBtnWrap {
    width: 100%;
    :global {
      .ant-btn {
        width: 100%;
      };
    }
  }
  .cancelBtnWrap {
    padding-top: .08rem;
    :global {
      .ant-btn {
        border: none;
        color: #0038A7;
        box-shadow: none;
      };
    }
  }
}