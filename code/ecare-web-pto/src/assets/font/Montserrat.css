@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 100;
  src: url(./Montserrat/Montserrat-normal-100.woff2) format('woff2'), url(./Montserrat/Montserrat-normal-100.ttf) format('truetype'), url(./Montserrat/Montserrat-normal-100.woff) format('woff');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 200;
  src: url(./Montserrat/Montserrat-normal-200.woff2) format('woff2'), url(./Montserrat/Montserrat-normal-200.ttf) format('truetype'), url(./Montserrat/Montserrat-normal-200.woff) format('woff');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 300;
  src: url(./Montserrat/Montserrat-normal-300.woff2) format('woff2'), url(./Montserrat/Montserrat-normal-300.ttf) format('truetype'), url(./Montserrat/Montserrat-normal-300.woff) format('woff');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 400;
  src: url(./Montserrat/Montserrat.eot);
  src: local('Montserrat'), url(./Montserrat/Montserrat.eot?#iefix) format('embedded-opentype'), url(./Montserrat/Montserrat.woff2) format('woff2'), url(./Montserrat/Montserrat.svg#Montserrat) format('svg'), url(./Montserrat/Montserrat.ttf) format('truetype'), url(./Montserrat/Montserrat.woff) format('woff');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 500;
  src: url(./Montserrat/Montserrat-normal-500.woff2) format('woff2'), url(./Montserrat/Montserrat-normal-500.ttf) format('truetype'), url(./Montserrat/Montserrat-normal-500.woff) format('woff');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 600;
  src: url(./Montserrat/Montserrat-normal-600.woff2) format('woff2'), url(./Montserrat/Montserrat-normal-600.ttf) format('truetype'), url(./Montserrat/Montserrat-normal-600.woff) format('woff');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 700;
  src: url(./Montserrat/Montserrat-normal-700.woff2) format('woff2'), url(./Montserrat/Montserrat-normal-700.ttf) format('truetype'), url(./Montserrat/Montserrat-normal-700.woff) format('woff');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 800;
  src: url(./Montserrat/Montserrat-normal-800.woff2) format('woff2'), url(./Montserrat/Montserrat-normal-800.ttf) format('truetype'), url(./Montserrat/Montserrat-normal-800.woff) format('woff');
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 900;
  src: url(./Montserrat/Montserrat-normal-900.woff2) format('woff2'), url(./Montserrat/Montserrat-normal-900.ttf) format('truetype'), url(./Montserrat/Montserrat-normal-900.woff) format('woff');
}

@font-face {
  font-family: 'Montserrat';
  font-style: italic;
  font-weight: 100;
  src: url(./Montserrat/Montserrat-italic-100.woff2) format('woff2'), url(./Montserrat/Montserrat-italic-100.ttf) format('truetype'), url(./Montserrat/Montserrat-italic-100.woff) format('woff');
}

@font-face {
  font-family: 'Montserrat';
  font-style: italic;
  font-weight: 200;
  src: url(./Montserrat/Montserrat-italic-200.woff2) format('woff2'), url(./Montserrat/Montserrat-italic-200.ttf) format('truetype'), url(./Montserrat/Montserrat-italic-200.woff) format('woff');
}

@font-face {
  font-family: 'Montserrat';
  font-style: italic;
  font-weight: 300;
  src: url(./Montserrat/Montserrat-italic-300.woff2) format('woff2'), url(./Montserrat/Montserrat-italic-300.ttf) format('truetype'), url(./Montserrat/Montserrat-italic-300.woff) format('woff');
}

@font-face {
  font-family: 'Montserrat';
  font-style: italic;
  font-weight: 400;
  src: url(./Montserrat/Montserrat-italic-400.woff2) format('woff2'), url(./Montserrat/Montserrat-italic-400.ttf) format('truetype'), url(./Montserrat/Montserrat-italic-400.woff) format('woff');
}

@font-face {
  font-family: 'Montserrat';
  font-style: italic;
  font-weight: 500;
  src: url(./Montserrat/Montserrat-italic-500.woff2) format('woff2'), url(./Montserrat/Montserrat-italic-500.ttf) format('truetype'), url(./Montserrat/Montserrat-italic-500.woff) format('woff');
}

@font-face {
  font-family: 'Montserrat';
  font-style: italic;
  font-weight: 600;
  src: url(./Montserrat/Montserrat-italic-600.woff2) format('woff2'), url(./Montserrat/Montserrat-italic-600.ttf) format('truetype'), url(./Montserrat/Montserrat-italic-600.woff) format('woff');
}

@font-face {
  font-family: 'Montserrat';
  font-style: italic;
  font-weight: 700;
  src: url(./Montserrat/Montserrat-italic-700.woff2) format('woff2'), url(./Montserrat/Montserrat-italic-700.ttf) format('truetype'), url(./Montserrat/Montserrat-italic-700.woff) format('woff');
}

@font-face {
  font-family: 'Montserrat';
  font-style: italic;
  font-weight: 800;
  src: url(./Montserrat/Montserrat-italic-800.woff2) format('woff2'), url(./Montserrat/Montserrat-italic-800.ttf) format('truetype'), url(./Montserrat/Montserrat-italic-800.woff) format('woff');
}

@font-face {
  font-family: 'Montserrat';
  font-style: italic;
  font-weight: 900;
  src: url(./Montserrat/Montserrat-italic-900.woff2) format('woff2'), url(./Montserrat/Montserrat-italic-900.ttf) format('truetype'), url(./Montserrat/Montserrat-italic-900.woff) format('woff');
}

