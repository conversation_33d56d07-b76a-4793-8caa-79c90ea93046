@font-face {
  font-family: 'Exo';
  font-style: normal;
  font-weight: 100;
  src: url(./Exo/Exo-normal-100.woff2) format('woff2'), url(./Exo/Exo-normal-100.ttf) format('truetype'), url(./Exo/Exo-normal-100.woff) format('woff');
}

@font-face {
  font-family: 'Exo';
  font-style: normal;
  font-weight: 200;
  src: url(./Exo/Exo-normal-200.woff2) format('woff2'), url(./Exo/Exo-normal-200.ttf) format('truetype'), url(./Exo/Exo-normal-200.woff) format('woff');
}

@font-face {
  font-family: 'Exo';
  font-style: normal;
  font-weight: 300;
  src: url(./Exo/Exo-normal-300.woff2) format('woff2'), url(./Exo/Exo-normal-300.ttf) format('truetype'), url(./Exo/Exo-normal-300.woff) format('woff');
}

@font-face {
  font-family: 'Exo';
  font-style: normal;
  font-weight: 400;
  src: url(./Exo/Exo.eot);
  src: local('Exo'), url(./Exo/Exo.eot?#iefix) format('embedded-opentype'), url(./Exo/Exo.woff2) format('woff2'), url(./Exo/Exo.svg#Exo) format('svg'), url(./Exo/Exo.ttf) format('truetype'), url(./Exo/Exo.woff) format('woff');
}

@font-face {
  font-family: 'Exo';
  font-style: normal;
  font-weight: 500;
  src: url(./Exo/Exo-normal-500.woff2) format('woff2'), url(./Exo/Exo-normal-500.ttf) format('truetype'), url(./Exo/Exo-normal-500.woff) format('woff');
}

@font-face {
  font-family: 'Exo';
  font-style: normal;
  font-weight: 600;
  src: url(./Exo/Exo-normal-600.woff2) format('woff2'), url(./Exo/Exo-normal-600.ttf) format('truetype'), url(./Exo/Exo-normal-600.woff) format('woff');
}

@font-face {
  font-family: 'Exo';
  font-style: normal;
  font-weight: 700;
  src: url(./Exo/Exo-normal-700.woff2) format('woff2'), url(./Exo/Exo-normal-700.ttf) format('truetype'), url(./Exo/Exo-normal-700.woff) format('woff');
}

@font-face {
  font-family: 'Exo';
  font-style: normal;
  font-weight: 800;
  src: url(./Exo/Exo-normal-800.woff2) format('woff2'), url(./Exo/Exo-normal-800.ttf) format('truetype'), url(./Exo/Exo-normal-800.woff) format('woff');
}

@font-face {
  font-family: 'Exo';
  font-style: normal;
  font-weight: 900;
  src: url(./Exo/Exo-normal-900.woff2) format('woff2'), url(./Exo/Exo-normal-900.ttf) format('truetype'), url(./Exo/Exo-normal-900.woff) format('woff');
}

@font-face {
  font-family: 'Exo';
  font-style: italic;
  font-weight: 100;
  src: url(./Exo/Exo-italic-100.woff2) format('woff2'), url(./Exo/Exo-italic-100.ttf) format('truetype'), url(./Exo/Exo-italic-100.woff) format('woff');
}

@font-face {
  font-family: 'Exo';
  font-style: italic;
  font-weight: 200;
  src: url(./Exo/Exo-italic-200.woff2) format('woff2'), url(./Exo/Exo-italic-200.ttf) format('truetype'), url(./Exo/Exo-italic-200.woff) format('woff');
}

@font-face {
  font-family: 'Exo';
  font-style: italic;
  font-weight: 300;
  src: url(./Exo/Exo-italic-300.woff2) format('woff2'), url(./Exo/Exo-italic-300.ttf) format('truetype'), url(./Exo/Exo-italic-300.woff) format('woff');
}

@font-face {
  font-family: 'Exo';
  font-style: italic;
  font-weight: 400;
  src: url(./Exo/Exo-italic-400.woff2) format('woff2'), url(./Exo/Exo-italic-400.ttf) format('truetype'), url(./Exo/Exo-italic-400.woff) format('woff');
}

@font-face {
  font-family: 'Exo';
  font-style: italic;
  font-weight: 500;
  src: url(./Exo/Exo-italic-500.woff2) format('woff2'), url(./Exo/Exo-italic-500.ttf) format('truetype'), url(./Exo/Exo-italic-500.woff) format('woff');
}

@font-face {
  font-family: 'Exo';
  font-style: italic;
  font-weight: 600;
  src: url(./Exo/Exo-italic-600.woff2) format('woff2'), url(./Exo/Exo-italic-600.ttf) format('truetype'), url(./Exo/Exo-italic-600.woff) format('woff');
}

@font-face {
  font-family: 'Exo';
  font-style: italic;
  font-weight: 700;
  src: url(./Exo/Exo-italic-700.woff2) format('woff2'), url(./Exo/Exo-italic-700.ttf) format('truetype'), url(./Exo/Exo-italic-700.woff) format('woff');
}

@font-face {
  font-family: 'Exo';
  font-style: italic;
  font-weight: 800;
  src: url(./Exo/Exo-italic-800.woff2) format('woff2'), url(./Exo/Exo-italic-800.ttf) format('truetype'), url(./Exo/Exo-italic-800.woff) format('woff');
}

@font-face {
  font-family: 'Exo';
  font-style: italic;
  font-weight: 900;
  src: url(./Exo/Exo-italic-900.woff2) format('woff2'), url(./Exo/Exo-italic-900.ttf) format('truetype'), url(./Exo/Exo-italic-900.woff) format('woff');
}

