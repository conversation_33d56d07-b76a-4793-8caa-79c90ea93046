@font-face {
  font-family: 'Exo2';
  font-style: normal;
  font-weight: 100;
  src: url(./Exo-2/Exo-2-normal-100.woff) format('woff'), url(./Exo-2/Exo-2-normal-100.woff2) format('woff2'),
    url(./Exo-2/Exo-2-normal-100.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo2';
  font-style: normal;
  font-weight: 200;
  src: url(./Exo-2/Exo-2-normal-200.woff) format('woff'), url(./Exo-2/Exo-2-normal-200.woff2) format('woff2'),
    url(./Exo-2/Exo-2-normal-200.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo2';
  font-style: normal;
  font-weight: 300;
  src: url(./Exo-2/Exo-2-normal-300.woff) format('woff'), url(./Exo-2/Exo-2-normal-300.woff2) format('woff2'),
    url(./Exo-2/Exo-2-normal-300.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo2';
  font-style: normal;
  font-weight: 400;
  src: url(./Exo-2/Exo-2-normal-400.eot);
  src: local('Exo 2'), url(./Exo-2/Exo-2-normal-400.svg#Exo2) format('svg'),
    url(./Exo-2/Exo-2-normal-400.woff) format('woff'),
    url(./Exo-2/Exo-2-normal-400.eot?#iefix) format('embedded-opentype'),
    url(./Exo-2/Exo-2-normal-400.woff2) format('woff2'), url(./Exo-2/Exo-2-normal-400.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo2';
  font-style: normal;
  font-weight: 500;
  src: url(./Exo-2/Exo-2-normal-500.woff) format('woff'), url(./Exo-2/Exo-2-normal-500.woff2) format('woff2'),
    url(./Exo-2/Exo-2-normal-500.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo2';
  font-style: normal;
  font-weight: 600;
  src: url(./Exo-2/Exo-2-normal-600.woff) format('woff'), url(./Exo-2/Exo-2-normal-600.woff2) format('woff2'),
    url(./Exo-2/Exo-2-normal-600.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo2';
  font-style: normal;
  font-weight: 700;
  src: url(./Exo-2/Exo-2-normal-700.woff) format('woff'), url(./Exo-2/Exo-2-normal-700.woff2) format('woff2'),
    url(./Exo-2/Exo-2-normal-700.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo2';
  font-style: normal;
  font-weight: 800;
  src: url(./Exo-2/Exo-2-normal-800.woff) format('woff'), url(./Exo-2/Exo-2-normal-800.woff2) format('woff2'),
    url(./Exo-2/Exo-2-normal-800.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo2';
  font-style: normal;
  font-weight: 900;
  src: url(./Exo-2/Exo-2-normal-900.woff) format('woff'), url(./Exo-2/Exo-2-normal-900.woff2) format('woff2'),
    url(./Exo-2/Exo-2-normal-900.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: italic;
  font-weight: 100;
  src: url(./Exo-2/Exo-2-italic-100.woff) format('woff'), url(./Exo-2/Exo-2-italic-100.woff2) format('woff2'),
    url(./Exo-2/Exo-2-italic-100.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: italic;
  font-weight: 200;
  src: url(./Exo-2/Exo-2-italic-200.woff) format('woff'), url(./Exo-2/Exo-2-italic-200.woff2) format('woff2'),
    url(./Exo-2/Exo-2-italic-200.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: italic;
  font-weight: 300;
  src: url(./Exo-2/Exo-2-italic-300.woff) format('woff'), url(./Exo-2/Exo-2-italic-300.woff2) format('woff2'),
    url(./Exo-2/Exo-2-italic-300.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: italic;
  font-weight: 400;
  src: url(./Exo-2/Exo-2-italic-400.woff) format('woff'), url(./Exo-2/Exo-2-italic-400.woff2) format('woff2'),
    url(./Exo-2/Exo-2-italic-400.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: italic;
  font-weight: 500;
  src: url(./Exo-2/Exo-2-italic-500.woff) format('woff'), url(./Exo-2/Exo-2-italic-500.woff2) format('woff2'),
    url(./Exo-2/Exo-2-italic-500.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: italic;
  font-weight: 600;
  src: url(./Exo-2/Exo-2-italic-600.woff) format('woff'), url(./Exo-2/Exo-2-italic-600.woff2) format('woff2'),
    url(./Exo-2/Exo-2-italic-600.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: italic;
  font-weight: 700;
  src: url(./Exo-2/Exo-2-italic-700.woff) format('woff'), url(./Exo-2/Exo-2-italic-700.woff2) format('woff2'),
    url(./Exo-2/Exo-2-italic-700.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: italic;
  font-weight: 800;
  src: url(./Exo-2/Exo-2-italic-800.woff) format('woff'), url(./Exo-2/Exo-2-italic-800.woff2) format('woff2'),
    url(./Exo-2/Exo-2-italic-800.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: italic;
  font-weight: 900;
  src: url(./Exo-2/Exo-2-italic-900.woff) format('woff'), url(./Exo-2/Exo-2-italic-900.woff2) format('woff2'),
    url(./Exo-2/Exo-2-italic-900.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: normal;
  font-weight: 100;
  src: url(./Exo-2/Exo-2-normal-100.woff) format('woff'), url(./Exo-2/Exo-2-normal-100.woff2) format('woff2'),
    url(./Exo-2/Exo-2-normal-100.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: normal;
  font-weight: 200;
  src: url(./Exo-2/Exo-2-normal-200.woff) format('woff'), url(./Exo-2/Exo-2-normal-200.woff2) format('woff2'),
    url(./Exo-2/Exo-2-normal-200.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: normal;
  font-weight: 300;
  src: url(./Exo-2/Exo-2-normal-300.woff) format('woff'), url(./Exo-2/Exo-2-normal-300.woff2) format('woff2'),
    url(./Exo-2/Exo-2-normal-300.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: normal;
  font-weight: 400;
  src: url(./Exo-2/Exo-2-normal-400.eot);
  src: local('Exo 2'), url(./Exo-2/Exo-2-normal-400.svg#Exo2) format('svg'),
    url(./Exo-2/Exo-2-normal-400.woff) format('woff'),
    url(./Exo-2/Exo-2-normal-400.eot?#iefix) format('embedded-opentype'),
    url(./Exo-2/Exo-2-normal-400.woff2) format('woff2'), url(./Exo-2/Exo-2-normal-400.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: normal;
  font-weight: 500;
  src: url(./Exo-2/Exo-2-normal-500.woff) format('woff'), url(./Exo-2/Exo-2-normal-500.woff2) format('woff2'),
    url(./Exo-2/Exo-2-normal-500.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: normal;
  font-weight: 600;
  src: url(./Exo-2/Exo-2-normal-600.woff) format('woff'), url(./Exo-2/Exo-2-normal-600.woff2) format('woff2'),
    url(./Exo-2/Exo-2-normal-600.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: normal;
  font-weight: 700;
  src: url(./Exo-2/Exo-2-normal-700.woff) format('woff'), url(./Exo-2/Exo-2-normal-700.woff2) format('woff2'),
    url(./Exo-2/Exo-2-normal-700.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: normal;
  font-weight: 800;
  src: url(./Exo-2/Exo-2-normal-800.woff) format('woff'), url(./Exo-2/Exo-2-normal-800.woff2) format('woff2'),
    url(./Exo-2/Exo-2-normal-800.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: normal;
  font-weight: 900;
  src: url(./Exo-2/Exo-2-normal-900.woff) format('woff'), url(./Exo-2/Exo-2-normal-900.woff2) format('woff2'),
    url(./Exo-2/Exo-2-normal-900.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: italic;
  font-weight: 100;
  src: url(./Exo-2/Exo-2-italic-100.woff) format('woff'), url(./Exo-2/Exo-2-italic-100.woff2) format('woff2'),
    url(./Exo-2/Exo-2-italic-100.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: italic;
  font-weight: 200;
  src: url(./Exo-2/Exo-2-italic-200.woff) format('woff'), url(./Exo-2/Exo-2-italic-200.woff2) format('woff2'),
    url(./Exo-2/Exo-2-italic-200.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: italic;
  font-weight: 300;
  src: url(./Exo-2/Exo-2-italic-300.woff) format('woff'), url(./Exo-2/Exo-2-italic-300.woff2) format('woff2'),
    url(./Exo-2/Exo-2-italic-300.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: italic;
  font-weight: 400;
  src: url(./Exo-2/Exo-2-italic-400.woff) format('woff'), url(./Exo-2/Exo-2-italic-400.woff2) format('woff2'),
    url(./Exo-2/Exo-2-italic-400.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: italic;
  font-weight: 500;
  src: url(./Exo-2/Exo-2-italic-500.woff) format('woff'), url(./Exo-2/Exo-2-italic-500.woff2) format('woff2'),
    url(./Exo-2/Exo-2-italic-500.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: italic;
  font-weight: 600;
  src: url(./Exo-2/Exo-2-italic-600.woff) format('woff'), url(./Exo-2/Exo-2-italic-600.woff2) format('woff2'),
    url(./Exo-2/Exo-2-italic-600.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: italic;
  font-weight: 700;
  src: url(./Exo-2/Exo-2-italic-700.woff) format('woff'), url(./Exo-2/Exo-2-italic-700.woff2) format('woff2'),
    url(./Exo-2/Exo-2-italic-700.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: italic;
  font-weight: 800;
  src: url(./Exo-2/Exo-2-italic-800.woff) format('woff'), url(./Exo-2/Exo-2-italic-800.woff2) format('woff2'),
    url(./Exo-2/Exo-2-italic-800.ttf) format('truetype');
}

@font-face {
  font-family: 'Exo 2';
  font-style: italic;
  font-weight: 900;
  src: url(./Exo-2/Exo-2-italic-900.woff) format('woff'), url(./Exo-2/Exo-2-italic-900.woff2) format('woff2'),
    url(./Exo-2/Exo-2-italic-900.ttf) format('truetype');
}
