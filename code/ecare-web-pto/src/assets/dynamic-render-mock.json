{"projectSchema": [{"version": "1.0.0", "componentsMap": [{"package": "iwhale-lowcode-components", "version": "0.3.3", "exportName": "Image", "main": "", "destructuring": true, "subName": "", "componentName": "Image"}, {"package": "iwhale-lowcode-components", "version": "0.3.3", "exportName": "Col", "main": "", "destructuring": true, "subName": "", "componentName": "Col"}, {"package": "iwhale-lowcode-components", "version": "0.3.3", "exportName": "Layout", "main": "", "destructuring": true, "subName": "", "componentName": "TwoCol"}, {"package": "iwhale-lowcode-components", "version": "0.3.3", "exportName": "Row", "main": "", "destructuring": true, "subName": "", "componentName": "Row"}, {"package": "iwhale-lowcode-components", "version": "0.3.3", "exportName": "Text", "main": "", "destructuring": true, "subName": "", "componentName": "Text"}, {"package": "iwhale-lowcode-components", "version": "0.3.3", "exportName": "h", "main": "", "destructuring": true, "subName": "", "componentName": "H"}, {"package": "iwhale-lowcode-components", "version": "0.3.3", "exportName": "link", "main": "", "destructuring": true, "subName": "", "componentName": "Link"}, {"package": "iwhale-lowcode-components", "version": "0.3.3", "exportName": "<PERSON><PERSON>", "main": "", "destructuring": true, "subName": "", "componentName": "<PERSON><PERSON>"}, {"devMode": "lowCode", "componentName": "Page"}], "componentsTree": [{"componentName": "Page", "id": "node_dockcviv8fo1", "props": {"ref": "outerView", "style": {"height": "100%", "backgroundColor": "#ffffff"}, "background-color": "#ffffff"}, "fileName": "/", "dataSource": {"list": [{"type": "fetch", "isInit": true, "options": {"params": {}, "method": "GET", "isCors": true, "timeout": 5000, "headers": {}, "uri": "mock/info.json"}, "id": "info"}]}, "state": {"text": {"type": "JSExpression", "value": "\"outer\""}, "isShowDialog": {"type": "JSExpression", "value": "false"}}, "css": "body {\n  font-size: 12px;\n}\n\n.button {\n  width: 100px;\n  color: #ff00ff\n}", "lifeCycles": {"componentDidMount": {"type": "JSFunction", "value": "function componentDidMount() {\n  console.log('did mount');\n}"}, "componentWillUnmount": {"type": "JSFunction", "value": "function componentWillUnmount() {\n  console.log('will unmount');\n}"}}, "methods": {"testFunc": {"type": "JSFunction", "value": "function testFunc() {\n  console.log('test func');\n}"}, "onClick": {"type": "JSFunction", "value": "function onClick() {\n  this.setState({\n    isShowDialog: true\n  });\n}"}, "closeDialog": {"type": "JSFunction", "value": "function closeDialog() {\n  this.setState({\n    isShowDialog: false\n  });\n}"}}, "originCode": "class LowcodeComponent extends Component {\n  state = {\n    \"text\": \"outer\",\n    \"isShowDialog\": false\n  }\n  componentDidMount() {\n    console.log('did mount');\n  }\n  componentWillUnmount() {\n    console.log('will unmount');\n  }\n  testFunc() {\n    console.log('test func');\n  }\n  onClick() {\n    this.setState({\n      isShowDialog: true\n    })\n  }\n  closeDialog() {\n    this.setState({\n      isShowDialog: false\n    })\n  }\n}", "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "Row", "id": "node_ocl45e5nob1j3", "props": {"backgroundType": "2", "background": "transparent"}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "TwoCol", "id": "node_ocl45e5nob1j4", "props": {"col": "2"}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "Col", "id": "node_ocl45e5nob1j5", "props": {"colSpan": 24, "style": {"padding": "0px"}}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "Image", "id": "node_ocl45e5nob1jz", "props": {"title": "", "src": "https://www.u.com.my/content/dam/u-mobile/personal/mobile-plans/prepaid/new-prepaid-plan/banners/EN-speedbooster-3000x400-desktop.png", "padding": {"top": 0, "bottom": 0, "left": 0, "right": 0, "applyAllSides": true}, "alt": "", "link": ""}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}]}]}, {"componentName": "Row", "id": "node_ocl45cvyl9m", "props": {"backgroundType": "2", "background": "transparent", "marginSpace": {"marginTop": "10px"}}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "TwoCol", "id": "node_ocl45cvyl9n", "props": {"col": "2"}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "Col", "id": "node_ocl45cvyl9o", "props": {"colSpan": 24}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "Text", "id": "node_ocl45cvyl918", "props": {"text": "<p><span style=\"font-size: 48px;\"><strong>Only The Best Prepaid Plans For U</strong></span></p>"}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "Link", "id": "node_ocl6omz8xx2f", "props": {"title": "Go to buy load", "href": "mydito://BuyLoad", "target": "", "style": {"fontSize": "18px"}}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}]}]}, {"componentName": "Row", "id": "node_ocl45e5nob96", "props": {"backgroundType": "2", "background": "transparent"}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "TwoCol", "id": "node_ocl45e5nob97", "props": {"col": "2"}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "Col", "id": "node_ocl45e5nob98", "props": {"colSpan": 12}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "Image", "id": "node_ocl45e5nob9y", "props": {"title": "", "src": "https://www.u.com.my/content/dam/u-mobile/personal/mobile-plans/prepaid/new-prepaid-plan/meta-images/EN-speedbooster-mar2022-1200x630_v2.png", "alt": "", "link": ""}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "H", "id": "node_ocl45e5nobzw", "props": {"title": "All-New Prepaid Plans", "level": 1}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "H", "id": "node_ocl45e5nob11l", "props": {"title": "UNLIMITED Data & Calls now come with 30 hours Speed Booster! Limited time promo at RM20 /month.", "level": 5}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "Link", "id": "node_ocl45e5nob13i", "props": {"title": "Learn More", "href": "https://www.u.com.my/en/personal/mobile-plans/prepaid/u-prepaid", "target": "_blank"}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}, {"componentName": "Col", "id": "node_ocl45e5nob99", "props": {"colSpan": 12}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "Image", "id": "node_ocl45e5nobaz", "props": {"title": "", "src": "https://www.u.com.my/content/dam/u-mobile/personal/meta-images-en/mobile-plans/prepaid/metaimage-59-daily-weekly-plan.png", "alt": "", "link": ""}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "H", "id": "node_ocl45e5nobel", "props": {"title": "Daily and Weekly Data Plans", "level": 1}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "H", "id": "node_ocl45e5nobqb", "props": {"title": "Enjoy the freedom to surf the Internet the way you want! Get to know more about U Mobile Daily & Weekly Data Plans. Visit us online for Prepaid plan details.", "level": 5}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}, {"componentName": "Link", "id": "node_ocl45e5nobs4", "props": {"title": "Learn More", "href": "https://www.u.com.my/en/personal/mobile-plans/prepaid/daily-and-weekly-data-plans", "target": "_blank"}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}]}]}, {"componentName": "Row", "id": "node_ocl45e5nob1ww", "props": {"backgroundType": "2", "background": "transparent"}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "TwoCol", "id": "node_ocl45e5nob1wx", "props": {"col": "2"}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "Col", "id": "node_ocl45e5nob1wy", "props": {"colSpan": 24, "style": {"textAlign": "center"}}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "<PERSON><PERSON>", "id": "node_ocl45e5nob1ye", "props": {"prefix": "next-", "type": "primary", "size": "medium", "htmlType": "button", "component": "a", "children": "Special Deal, Go & Check our Latest Promotion!", "padding": {"right": 30, "top": 20, "bottom": 20, "left": 30}, "background-color": "#ff7b00", "text-align": "center", "border-radius": 26, "text": {"fontSize": 18, "fontFamily": "frank", "color": ""}, "style": {"paddingTop": "20px", "paddingBottom": "20px", "paddingLeft": "30px", "paddingRight": "30px", "backgroundColor": "#ff7b00", "borderRadius": "26px", "fontSize": "18px"}, "href": "https://www.u.com.my/en/personal/home"}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": ""}]}]}]}]}], "i18n": {}}], "packages": [{"package": "moment", "version": "2.24.0", "urls": ["https://g.alicdn.com/mylib/moment/2.24.0/min/moment.min.js"], "library": "moment"}, {"package": "lodash", "library": "_", "urls": ["https://g.alicdn.com/platform/c/lodash/4.6.1/lodash.min.js"]}, {"title": "fusion组件库", "package": "@alifd/next", "version": "1.24.18", "urls": ["https://g.alicdn.com/code/lib/alifd__next/1.24.18/next.min.css", "https://g.alicdn.com/code/lib/alifd__next/1.24.18/next-with-locales.min.js"], "library": "Next"}, {"title": "NextTable", "package": "NextTable", "version": "1.0.1", "urls": ["https://g.alicdn.com/fusion-platform/pro-table/1.0.1/next-table.js", "https://g.alicdn.com/fusion-platform/pro-table/1.0.1/next-table.css"], "library": "NextTable"}, {"package": "@alifd/pro-layout", "version": "1.0.1-beta.0", "library": "AlifdProLayout", "urls": ["https://unpkg.com/@alifd/pro-layout@1.0.1-beta.0/dist/AlifeProLayout.js", "https://unpkg.com/@alifd/pro-layout@1.0.1-beta.0/dist/AlifeProLayout.css"], "editUrls": ["https://unpkg.com/@alifd/pro-layout@1.0.1-beta.0/build/lowcode/view.js", "https://unpkg.com/@alifd/pro-layout@1.0.1-beta.0/build/lowcode/view.css"]}, {"title": "AliLowCodeComponents", "package": "@alifd/ali-lowcode-components", "version": "latest", "urls": ["https://gw.alipayobjects.com/os/lib/alifd/ali-lowcode-components/0.1.4/dist/ali-lowcode-components.js", "https://gw.alipayobjects.com/os/lib/alifd/ali-lowcode-components/0.1.4/dist/ali-lowcode-components.css"], "library": "AliLowCodeComponents"}, {"title": "Container", "package": "@alife/container", "version": "0.3.7", "urls": ["https://g.alicdn.com/fusion-design/auto-layout/0.3.7/dist/container.css", "https://g.alicdn.com/fusion-design/auto-layout/0.3.7/dist/container.js"], "library": "Container"}, {"package": "iwhale-lowcode-components", "version": "0.3.0", "library": "IwhaleLowcodeComponents", "urls": ["https://unpkg.com/iwhale-lowcode-components@0.3.3/build/lowcode/view.js", "https://unpkg.com/iwhale-lowcode-components@0.3.3/build/lowcode/view.css"], "editUrls": ["https://unpkg.com/iwhale-lowcode-components@0.3.3/build/lowcode/view.js", "https://unpkg.com/iwhale-lowcode-components@0.3.3/build/lowcode/view.css"]}, {"package": "email", "urls": ["./foundation-emails.css"]}]}