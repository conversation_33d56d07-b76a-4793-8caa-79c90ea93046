!function e(n,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.AliLowCodeComponents=t():n.AliLowCodeComponents=t()}(window,(function(){return function(e){var n={};function t(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,t),o.l=!0,o.exports}return t.m=e,t.c=n,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:r})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(t.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var o in e)t.d(r,o,function(n){return e[n]}.bind(null,o));return r},t.n=function(e){var n=e&&e.__esModule?function n(){return e.default}:function n(){return e};return t.d(n,"a",n),n},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.p="./dist/",t(t.s=4)}([function(e,n){e.exports=window.React},function(e,n){e.exports=window.ReactDOM},function(e,n){e.exports=window.Next},function(e,n){e.exports=window.PropTypes},function(e,n,t){e.exports=t(6)},function(e,n,t){},function(e,n,t){"use strict";t.r(n),t.d(n,"Link",(function(){return f})),t.d(n,"Image",(function(){return p})),t.d(n,"Video",(function(){return g})),t.d(n,"RichText",(function(){return j})),t.d(n,"NoteWrapper",(function(){return k}));var r=t(0),o=t.n(r),i=["children","__designMode"];function a(e,n){if(null==e)return{};var t=c(e,n),r,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],n.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(t[r]=e[r])}return t}function c(e,n){if(null==e)return{};var t={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],n.indexOf(o)>=0||(t[o]=e[o]);return t}var u=function e(n,t){var o=n.children,c=n.__designMode,u=a(n,i),l={};return"design"===c&&(l.onClick=function(e){e.preventDefault()}),r.createElement("a",Object.assign({},u,{ref:t},l),o)},l=r.forwardRef(u);l.defaultProps={href:"https://fusion.design",children:"\u8fd9\u662f\u4e00\u4e2a\u8d85\u94fe\u63a5",target:"_blank"};var f=l,s=function e(n,t){return r.createElement("img",Object.assign({},n,{ref:t}))},d=r.forwardRef(s);d.defaultProps={src:"https://img.alicdn.com/tps/TB16TQvOXXXXXbiaFXXXXXXXXXX-120-120.svg"};var p=d,m=function e(n,t){return r.createElement("video",Object.assign({},n,{ref:t}))},v=r.forwardRef(m);v.defaultProps={src:"https://fusion.alicdn.com/fusion-site-2.0/fusion.mp4"};var g=v,y=["content","maxHeight"];function b(e,n){if(null==e)return{};var t=O(e,n),r,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],n.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(t[r]=e[r])}return t}function O(e,n){if(null==e)return{};var t={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],n.indexOf(o)>=0||(t[o]=e[o]);return t}var w=function e(n,t){var o=n.content,i=void 0===o?"":o,a=n.maxHeight,c=void 0===a?"auto":a,u=b(n,y);return r.createElement("div",Object.assign({},u,{ref:t}),r.createElement("div",{style:{maxHeight:c,overflowY:"scroll"},className:"message-content",dangerouslySetInnerHTML:{__html:i}}))},x=r.forwardRef(w);x.defaultProps={content:'<div><span style="font-size: 18pt; color: #333;"></span></div><div><span>- \u4f60\u53ef\u4ee5\u5728\u8fd9\u91cc\u63cf\u8ff0\u9700\u6c42</span><br /><span>- \u6216\u8005\u7c98\u8d34\u9700\u6c42\u622a\u56fe</span></div>',className:""};var j=x,h=t(1),S=t.n(h),X=t(2),E=t(3),P=t.n(E),_=t(5),N="render-wrapper-target",T="render-wrapper-root done-note-wrapper",M=function e(n,t){return n.currentStyle?n.currentStyle[t]:window.getComputedStyle(n,null)[t]},C=function e(n,t){var i=document.createElement("div"),a=null;i.className=T,i.id=n.id,i.ref=t;var c=function e(){var t=n.note,r=o.a.createElement("div",{className:"render-wrapper-note"},"N"),a=o.a.createElement(X.Balloon.Tooltip,{trigger:r,align:"rb",triggerType:"click",popupStyle:{fontSize:"12px",lineHeight:"1.6",background:"#fff"}},t);S.a.render(a,i)};Object(r.useEffect)((function(){if(c(),a){var e=S.a.findDOMNode(a);-1===e.className.indexOf(N)&&("static"===M(e,"position")&&(e.style.position="relative"),e.className+=" ".concat(N),e.appendChild(i))}}));var u=n.children,l=o.a.Children.map(u,(function(e){var n=e;return"string"==typeof e&&(n=o.a.createElement("span",null,e)),o.a.cloneElement(n,{ref:function e(n){return a=n}})}));return console.log("_children: ",l),l||o.a.createElement(o.a.Fragment,null)},R=o.a.forwardRef(C);R.displayName="Wrapper",R.propTypes={note:P.a.string};var k=R}])}));