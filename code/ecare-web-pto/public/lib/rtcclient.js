/*
 * Hisancc WebRTC Client SDK v7.3.20220301
 * © 2020 Hisancc Ltd. All rights reserved.
 */
function RTCClient() {

    var version = '7.3.20220301';

    var config = {
        username: null,
        password: null,
        domain: null,
        displayName: null,
        realm: null,
        iceServers: [],
        iceTransportPolicy: 'all',
        autoRegister: true,
        registerExpires: 120,
        registerExtraHeaders: [],
        reconnectMinInterval: 2,
        reconnectMaxInterval: 30,
        sessionTimers: false,
        userAgent: 'Hisancc WebRTC Client',
        debug: false,
        sipproxy: null,
        // 默认的振铃声音和挂机音的位置(客户端可以通过设置修改)
        ringSound: 'sound/ringing.ogg',
        hangupSound: 'sound/hangup.ogg',
    };

    var browser = {
        name: null,
        version: null
    };

    var self = this;

    var sipUA = null;
    var session = null;

    this.version = function () {
        return version;
    }

    window.AudioContext = window.AudioContext || window.webkitAudioContext || window.mozAudioContext || window.msAudioContext;

    // 播放远端媒体的audio组件
    var remoteAudio = new Audio();
    remoteAudio.autoplay = true;
    remoteAudio.style = 'display:none';
    document.body.appendChild(remoteAudio);

    // 控制是否自动应答
    var autoAnswer = false;

    // 定时发送心跳包
    var heartBeatInterval = null;

    /**
     * 设置配置项
     * @param options
     */
    this.setConfig = function (options) {

        options = options || {};

        if (config) {
            for (prop in options) {
                if (config.hasOwnProperty(prop)) {
                    config[prop] = options[prop];
                }
            }
        }
    }

    /**
     * 获取配置项
     * @param property
     * @returns {{}|*}
     */
    this.getConfig = function (property) {

        if (property == undefined) {

            var options = {};

            for (prop in config) {
                options[prop] = config[prop];
            }

            return options;
        } else {
            return config[property];
        }
    }


    this.start = function () {

        var internalConfig = {
            sockets: [new JsSIP.WebSocketInterface(config.sipproxy)],
            uri: 'sip:' + config.username + '@' + config.domain,
            authorization_user: config.username,
            password: config.password,
            display_name: config.displayName,
            register: config.autoRegister,
            session_timers: config.sessionTimers,
            register_expires: config.registerExpires,
            user_agent: config.userAgent,
            connection_recovery_min_interval: config.reconnectMinInterval,
            connection_recovery_max_interval: config.reconnectMaxInterval,
            realm: config.realm,
            contact_uri: 'sip:' + config.username + '@' + config.domain
        };

        sipUA = new JsSIP.UA(internalConfig);
        sipUA.registrator().setExtraHeaders(config.registerExtraHeaders);
        setupEventHandlers();
        sipUA.start();
    }

    /**
     * 停止客户端
     */
    this.stop = function () {
        if (sipUA) {
            sipUA.stop();
            sipUA = null;
        }
    }

    /**
     * 发起注册
     */
    this.register = async function (options) {

        // 检查设备权限(并输出日志)，没有权限的话，就停止注册
        if (await checkIsWebRTCSupport() == false) {
            debug("web rtc not support or media device disabled")
            return ;
        }

        if (sipUA == null) {
            this.start();
        }
        if (sipUA) {
            fireStatusChanged({
                status: 'registering',
                reason: ''
            })
            if (options && options.extraHeaders) {

                sipUA.registrator().setExtraHeaders(options.extraHeaders);
            }
            sipUA.register();
            registerRefreshHook();
        }
    }

    /**
     * 取消注册
     */
    this.unregister = function () {

        if (sipUA && sipUA.isRegistered()) {
            sipUA.unregister();
        }

        this.stop();
    }

    /**
     * 发起外呼
     * @param target
     * @param options
     */
    this.makeCall = function (target, options) {

        if (target.indexOf('sip:') == -1){
            target = "sip:" + target;
        }

        // 未注册情况下 realm为null
        if (sipUA.get("realm") != null){
            target = target + "@" + sipUA.get("realm");
        }

        var internalOptions = {
            mediaConstraints: {audio: true},
            pcConfig: {iceServers: config.iceServers, iceTransportPolicy: config.iceTransportPolicy}
        }

        if (options && options.video) {
            internalOptions.mediaConstraints.video = true;
        }

        if (options && options.extraHeaders) {
            internalOptions.extraHeaders = options.extraHeaders;
        }

        if (sipUA) {
            sipUA.call(target, internalOptions);
        }
    }


    /**
     * 呼叫应答
     * @param options
     */

    this.answerCall = function (option) {
        answerCall(option);
    }

    function answerCall(options) {

        var internalOptions = {
            mediaConstraints: {'audio': true, 'video': false},
            pcConfig: {iceServers: config.iceServers, iceTransportPolicy: config.iceTransportPolicy}
        };

        if (options && options.video) {
            //internalOptions.mediaConstraints.video = true;
            internalOptions.mediaConstraints.video = {
                width: { min: 640, ideal: 640, max: 640 },
                height: { min: 480, ideal: 480 },
                frameRate: { max: 15,ideal:15 }
            };
        }

        if (options && options.extraHeaders) {
            internalOptions.extraHeaders = options.extraHeaders;
        }
        // 跳过已经接通和已应答
        if (session && (session._status == 9 || session._status == 5)) {
            return;
        }
        if (session) {
            session.answer(internalOptions);
        }
    }

    /**
     * 来电拒接
     */
    this.rejectCall = function () {
        if (session) {
            session.terminate({status_code: 403});
        }
    }

    /**
     * 呼叫挂断
     * @param options
     */
    this.hangupCall = function (options) {

        options = options || {};

        var internalOptions = {
            status_code: options.statusCode || 486
        };

        if (options.extraHeaders) {
            internalOptions.extraHeaders = options.extraHeaders;
        }

        if (session) {
            session.terminate(internalOptions);
        }
    }

    /**
     * 呼叫转移
     * @param target
     * @param options
     */
    this.transferCall = function (target, options) {

        options = options || {};

        var internalOptions = {};

        if (options.extraHeaders) {
            internalOptions.extraHeaders = options.extraHeaders;
        }

        if (session) {
            session.refer(target, internalOptions);
        }
    }

    /**
     * 发送DTMF
     * @param dtmf 一位或多位数字组成的DTMF
     */
    this.sendDTMF = function (dtmf) {
        if (session) {
            session.sendDTMF(dtmf,{'transportType':'RFC2833'});
        }
    }

    /**
     * 呼叫保持
     */
    this.holdCall = function () {
        if (session) {
            session.hold();
        }
    }

    /**
     * 取消保持
     */
    this.unholdCall = function () {
        if (session) {
            session.unhold();
        }
    }

    /**
     * 打开麦克风
     */
    this.unmuteAudio = function () {
        if (session) {
            session.unmute({'audio': true})
        }
    }

    /**
     * 关闭麦克风
     */
    this.muteAudio = function () {
        if (session) {
            session.mute({'audio': true})
        }
    }

    /**
     * 打开摄像头
     */
    this.videoOn = function () {
        if (session) {
            session.unmute({'video': true})
        }
    }
    /**
     * 关闭摄像头
     */
    this.videoOff = function () {
        if (session) {
            session.mute({'video': true})
        }
    }

    this.setAutoAnswer = function () {
        autoAnswer = true;
    }

    /**
     * 获取当前呼叫的RTCSession
     * @returns {RTCSession}
     */
    this.getSession = function () {
        return session;
    }

    /**
     * 获取当前呼叫的RTCPeerConnection
     * @returns {RTCPeerConnection}
     */
    this.getConnection = function () {
        if (session) {
            return session.connection;
        }
    }

    /**
     * 是否已注册
     * @returns {boolean}
     */
    this.isRegistered = function () {
        if (sipUA) {
            return sipUA.isRegistered();
        }
        return false;
    }

    /**
     * 是否来电
     * @returns {boolean}
     */
    this.isIncoming = function () {
        if (session) {
            return session.direction == 'incoming';
        }
        return false;
    }

    /**
     * 是否外呼
     * @returns {boolean}
     */
    this.isOutgoing = function () {
        if (session) {
            return session.direction == 'outgoing';
        }
        return false;
    }

    /**
     * 是否语音静音
     * @returns {boolean}
     */
    this.isAudioMuted = function () {
        if (session) {
            return session.isMuted().audio;
        }
        return false;
    }

    /**
     * 是否视频静音
     * @returns {boolean}
     */
    this.isVideoMuted = function () {
        if (session) {
            return session.isMuted().video;
        }
        return false;
    }

    /**
     * 是否本端保持
     * @returns {boolean}
     */
    this.isLocalHold = function () {
        if (session) {
            return session.isOnHold().local;
        }
        return false;
    }

    /**
     * 是否对端保持
     * @returns {boolean}
     */
    this.isRemoteHold = function () {
        if (session) {
            return session.isOnHold().remote;
        }
        return false;
    }

    /**
     * 设置事件处理器
     */
    function setupEventHandlers() {

        sipUA.on('connected', function (e) {
            fireStatusChanged({
                status: 'network_connected',
                reason: null
            });
            setHeartBeatInterval();
        });

        sipUA.on('disconnected', function (e) {
            session = null;
            fireStatusChanged({
                status: 'network_disconnected',
                reason: null
            });
            clearHeartBeatInterval();
        });

        sipUA.on('registered', function (e) {
            fireStatusChanged({
                status: 'registered',
                reason: null
            });
        });

        sipUA.on('unregistered', function (e) {

            session = null;

            fireStatusChanged({
                status: 'unregistered',
                reason: null
            });
        });

        sipUA.on('registrationFailed', function (e) {

            session = null;

            fireStatusChanged({
                status: 'registerFailed',
                reason: e.cause
            });
        });

        sipUA.on('newMessage', function (e) {
            debug('newMessage', e);
        });

        sipUA.on('newRTCSession', function (e) {

            debug('newRTCSession', e);

            if (e.originator === 'local') {

                if (session == null) {
                    session = e.session;
                }

                // 客户端发起的事件，也需要处理媒体事件，不然onaddStream钩子不会设置，听不到对端声音

                handleSessionEvent(e.session);

                if (e.session.connection) {
                    handleStreamEvent(e.session);
                } else {
                    e.session.on('peerconnection', function (event) {
                        handleStreamEvent(e.session);
                    })
                }

                return;
            }

            if (session) {
                if (e.originator === 'remote') {
                    debug('incoming call reply with 486 Busy Here', e);
                    e.session.terminate(486);
                }
                return;
            }

            session = e.session;

            handleSessionEvent(session);

            if (session.connection) {
                handleStreamEvent(session);
            } else {
                session.on('peerconnection', function (e) {
                    handleStreamEvent(session);
                })
            }

            var remote;

            if (e.originator === 'remote') {
                remote = e.request.from;
            } else {
                remote = e.request.to;
            }

            // set call data
            session.data['remote_user'] = remote.uri.user;
            session.data['remote_name'] = remote.display_name;
            session.data['create_time'] = new Date();

            if (e.originator === 'remote') {
                // Incoming call. Set video flag according m=video in SDP.
                let hasVideo = false;
                if (e.request.body) {
                    debug(e.request.body)

                    hasVideo = e.request.body.indexOf("m=video") != -1;

                } else {
                    debug('AC: warning: incoming INVITE without sdp');
                }
                session.data['_video'] = hasVideo;

                if (autoAnswer) {
                    autoAnswer = false
                    answerCall();
                }
            }
        });
    }

    /**
     * 处理会话事件
     * @param session
     */
    function handleSessionEvent(session) {

        session.on('progress', function (e) {

            debug('session progress event: ', e);

            playRinging();

            if (session.direction === 'outgoing') {

                fireStatusChanged({
                    status: 'dialing',
                    reason: null
                })

                fireCallDialing({
                    status: 'dialing',
                    reason: null
                });
            } else {

                fireStatusChanged({
                    status: 'offering',
                    session: session,
                    reason: null
                })

                fireCallOffering({
                    status: 'offering',
                    session: session,
                    reason: e.cause
                });
            }
        });

        session.on('sdp', function (e) {
            // if (e.type == 'offer') {
            //     e.sdp = e.sdp.replace(/a=setup:passive/g, 'a=setup:actpass');
            // } else if (e.type == 'answer') {
            //     e.sdp = e.sdp.replace(/a=setup:actpass/g, 'a=setup:actpass');
            // }
        });

        session.on('reinvite', function (e) {
            debug('session reinvite event:', e);
        });

        session.on('accepted', function (e) {

            debug('session accepted event: ', e);

        });

        session.on('confirmed', function (e) {

            debug('session confirmed event: ', e);

            pauseRinging();

            fireStatusChanged({
                status: 'connected',
                reason: null
            })

            fireCallConnected({
                status: 'connected',
                reason: null
            });

        });

        // 保持
        session.on('hold', function (e) {

            debug('session hold event:', e);

            fireStatusChanged({
                status: 'hold',
                originator: e.originator,
                reason: null
            });
        });

        // 取消保持
        session.on('unhold', function (e) {

            debug('session unhold event:', e);

            fireStatusChanged({
                status: 'unhold',
                originator: e.originator,
                reason: null
            });
        });

        // 静音
        session.on('muted', function (e) {

            debug('session muted event:', e);

            fireStatusChanged({
                status: 'muted',
                audio: e.audio,
                video: e.video,
                reason: null
            });
        });

        // 取消静音
        session.on('unmuted', function (e) {

            debug('session unmuted event:', e);

            fireStatusChanged({
                status: 'unmuted',
                audio: e.audio,
                video: e.video,
                reason: null
            });
        });

        session.on('ended', function (e) {

            debug('session ended event:', e);

            session = null;

            // 客户端没有应答导致的释放，也要调用停止按钮
            setTimeout(function () {
                pauseRinging();
                playHangup();
            },1000)

            fireStatusChanged({
                status: 'released',
                originator: e.originator,
                reason: e.cause
            })

            fireCallReleased({
                status: 'released',
                originator: e.originator,
                reason: e.cause
            });
        });

        session.on('failed', function (e) {

            debug('session failed event:', e);

            session = null;

            // 如果是客户端没有应答成功导致的超时，就停止播放铃声(20210203)
            setTimeout(function () {
                pauseRinging();
                playHangup();
            },1000);

            fireStatusChanged({
                status: 'released',
                reason: e.cause
            })

            fireCallReleased({
                status: 'released',
                reason: e.cause
            });
        });
    }

    /**
     * 处理呼叫媒体事件
     * @param session
     */
    function handleStreamEvent(session) {

        let connection = session.connection;

        if (connection.signalingState == "kStable") {
            debug("connection state:" + connection.signalingState)
            return;
        }

        connection.onaddstream = function (e) {

            if (connection.signalingState == "kStable") {
                debug("connection state:" + connection.signalingState)
                return;
            }

            var localStream = connection.getLocalStreams()[0];
            var remoteStream = e.stream;

            remoteStream.onremovetrack = function (e) {
                debug('remote stream onremovetrack', e);
            }

            remoteStream.onaddtrack = function (e) {
                debug('remote stream onaddtrack', e);
            }

            fireStreamChanged({
                session: session,
                local: localStream,
                remote: remoteStream
            });
        };

        connection.onremovestream = function (e) {
            debug('connection onremovestream', e);
        }

        connection.ontrack = function (e) {
            debug('connection ontrack', e);
        }
    }

    /**
     * 触发状态变化事件
     * @param event
     */
    function fireStatusChanged(event) {

        debug('fireStatusChanged: ', event);

        if (self.onStatusChanged) {
            self.onStatusChanged(event);
        }
    }

    /**
     * 触发外呼振铃事件
     * @param event
     */
    function fireCallDialing(event) {
        debug('fireCallDialing: ', event);
        if (self.onCallDialing) {
            self.onCallDialing(event);
        }
    }

    /**
     * 触发呼入振铃事件
     * @param event
     */
    function fireCallOffering(event) {

        debug('fireCallOffering: ', event);

        if (self.onCallOffering) {
            self.onCallOffering(event);
        }
    }

    /**
     * 触发呼叫媒体事件
     * @param event
     */
    function fireStreamChanged(event) {
        debug('fireStreamChanged: ', event);
        // if (self.onStreamChanged && (event.session && event.session.data && event.session.data['_video'] == 'true')) {
        if (self.onStreamChanged) {
            self.onStreamChanged(event);
        }else{
            playRemoteAudio(event.remote);
        }
    }

    /**
     * 触发呼叫接通事件
     * @param event
     */
    function fireCallConnected(event) {
        debug('fireCallConnected: ', event);
        if (self.onCallConnected) {
            self.onCallConnected(event);
        }
    }

    /**
     * 触发呼叫终止事件
     * @param event
     */
    function fireCallReleased(event) {

        debug('fireCallReleased: ', event);

        if (self.onCallReleased) {
            self.onCallReleased(event);
        }
        session = null;
    }

    function debug(eventName, event) {

        if (config.debug) {

            console.log(eventName || '');

            if (event != undefined) {

                if (event.status != undefined) {

                    console.log(event.status);
                }

                if (event.session != undefined) {

                    console.log(event.session);
                }

                if (event.reason != undefined) {

                    console.log(event.reason);
                }
            }
        }
    }

    async function checkIsWebRTCSupport() {

        var ua = window.navigator.userAgent;

        if (ua.indexOf('MSIE ') > 0 || ua.indexOf('Trident/') > 0) {
            debug("Internet Explorer is not supported. Please use Chrome or Firefox");
            return false;
        }

        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {

            debug("WebRTC API is not supported in this browser !");

            return false;
        }

        if( await checkMediaDevice() == false) {
            debug("media device not found");
            return false;
        }

        // 仅chrome支持检测摄像头和麦克风是否有权限，其他浏览器没有实现
//        if (navigator.userAgent.indexOf("Chrome") !== -1) {
//           return await checkMediaPermission();
//        }
        return true;
    }

    async function checkMediaDevice() {
        var mic = false;
        var spkr = false;
        return await navigator.mediaDevices.enumerateDevices()
            .then(function (deviceInfos) {
                deviceInfos.forEach(function (d) {

                    switch (d.kind) {

                        case 'audioinput':
                            mic = true;
                            break;
                        case 'audiooutput':
                            spkr = true;
                            break;
                    }
                })

                // Chrome supports 'audiooutput', Firefox doesn't. Others should be checked.
                if (navigator.userAgent.indexOf('Firefox') !== -1) {
                    spkr = true;
                }

                if (!spkr) {

                    debug('Missing a speaker! Please connect one and reload');

                    return false;
                }

                if (!mic) {
                    debug('Missing a microphone! Please connect one and reload');

                    return false;
                }

                return true;
            });
    }

    async function checkMediaPermission() {

       let result =  await navigator.permissions.query(
            {name: 'microphone'}
        ).then(function (permissionStatus) {

            if (permissionStatus.state == 'denied') {
                debug("micphone disabled");
                return false;
            }
            return true;
        });

       return result;
    }

    var ringBufferSource = null;

    var audioContext = new window.AudioContext();

    function playRinging() {

        if(config.ringSound == undefined || config.ringSound == null || config.ringSound == ""){
            return;
        }

        var request = new XMLHttpRequest();

        request.open('GET', config.ringSound, true);

        request.responseType = 'arraybuffer';

        request.onload = function () {
            audioContext.decodeAudioData(request.response, function (buffer) {
                ringBufferSource = audioContext.createBufferSource();
                ringBufferSource.loop = true;
                ringBufferSource.buffer = buffer;
                ringBufferSource.connect(audioContext.destination);
                ringBufferSource.start(audioContext.currentTime);
                ringBufferSource.stop(audioContext.currentTime + 30);
            });
        }

        request.send();
    }

    function pauseRinging() {

        if (ringBufferSource != null) {
            ringBufferSource.stop();
        }
    }

    function playHangup() {
        //没有挂机音就不播放声音
        if(config.hangupSound == undefined || config.hangupSound == null || config.hangupSound == ""){
            return;
        }

        var request = new XMLHttpRequest();

        request.open('GET', config.hangupSound, true);

        request.responseType = 'arraybuffer';

        request.onload = function () {
            audioContext.decodeAudioData(request.response, function (buffer) {
                var bufferSource = audioContext.createBufferSource();
                bufferSource.buffer = buffer;
                bufferSource.connect(audioContext.destination);
                bufferSource.start(0);
            });
        }

        request.send();
    }

    function playRemoteAudio(stream) {

        remoteAudio.srcObject = stream;
    }

    // 设置浏览器刷新和关闭钩子

    function registerRefreshHook() {

        window.onunload = function (e) {
            clearWebRTCResources(e);
        };

        // 火狐浏览器，关闭时只触发onbeforeunlod
        if (navigator.userAgent.indexOf("Firefox")>0) {
            window.onbeforeunload = function (e) {
                clearWebRTCResources(e);
            };
        }
    }

    function clearWebRTCResources(event) {

        if (sipUA && sipUA.isRegistered()) {
            try {
                if (session) {
                    session.terminate({status_code: 486});
                }
            } catch (e) {}
            try {
                sipUA.unregister();
            } catch (e) {}
        }
        return true;
    }

    function setHeartBeatInterval() {
        heartBeatInterval = setInterval(function () {
            sipUA.sendOptions('sip:' + config.username+ '@' + config.domain,"i'am alive");
        },20000);
    }

    function clearHeartBeatInterval() {
        if (heartBeatInterval != null) {
            clearInterval(heartBeatInterval);
        }
    }
}
