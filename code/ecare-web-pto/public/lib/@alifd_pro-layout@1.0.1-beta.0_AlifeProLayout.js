!function e(t,n){"object"==typeof exports&&"object"==typeof module?module.exports=n(require("react"),require("@alifd/next"),require("moment")):"function"==typeof define&&define.amd?define(["react","@alifd/next","moment"],n):"object"==typeof exports?exports.AlifeProLayout=n(require("react"),require("@alifd/next"),require("moment")):t.AlifeProLayout=n(t.React,t.Next,t.moment)}(window,(function(e,t,n){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function t(){return e.default}:function t(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="./dist/",n(n.s=8)}([function(t,n){t.exports=e},function(e,t,n){var r,o;
/*!
  Copyright (c) 2018 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/!function(){"use strict";var n={}.hasOwnProperty;function a(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var o=typeof r;if("string"===o||"number"===o)e.push(r);else if(Array.isArray(r)){if(r.length){var i=a.apply(null,r);i&&e.push(i)}}else if("object"===o)if(r.toString===Object.prototype.toString)for(var c in r)n.call(r,c)&&r[c]&&e.push(c);else e.push(r.toString())}}return e.join(" ")}e.exports?(a.default=a,e.exports=a):void 0===(o=function(){return a}.apply(t,r=[]))||(e.exports=o)}()},function(e,n){e.exports=t},function(e,t,n){(function(t){var n=200,r="__lodash_hash_undefined__",o=1/0,a=9007199254740991,i="[object Arguments]",c="[object Function]",l="[object GeneratorFunction]",u="[object Symbol]",s=/[\\^$.*+?()[\]{}|]/g,f=/^\[object .+?Constructor\]$/,p=/^(?:0|[1-9]\d*)$/,d="object"==typeof t&&t&&t.Object===Object&&t,y="object"==typeof self&&self&&self.Object===Object&&self,b=d||y||Function("return this")();function m(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}function h(e,t){var n;return!!(e?e.length:0)&&w(e,t,0)>-1}function v(e,t,n){for(var r=-1,o=e?e.length:0;++r<o;)if(n(t,e[r]))return!0;return!1}function g(e,t){for(var n=-1,r=e?e.length:0,o=Array(r);++n<r;)o[n]=t(e[n],n,e);return o}function O(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e}function _(e,t,n,r){for(var o=e.length,a=n+(r?1:-1);r?a--:++a<o;)if(t(e[a],a,e))return a;return-1}function w(e,t,n){if(t!=t)return _(e,j,n);for(var r=n-1,o=e.length;++r<o;)if(e[r]===t)return r;return-1}function j(e){return e!=e}function E(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}function x(e){return function(t){return e(t)}}function P(e,t){return e.has(t)}function S(e,t){return null==e?void 0:e[t]}function C(e){var t=!1;if(null!=e&&"function"!=typeof e.toString)try{t=!!(e+"")}catch(e){}return t}function N(e,t){return function(n){return e(t(n))}}var k=Array.prototype,T=Function.prototype,R=Object.prototype,A=b["__core-js_shared__"],B=(M=/[^.]+$/.exec(A&&A.keys&&A.keys.IE_PROTO||""))?"Symbol(src)_1."+M:"",M,D=T.toString,I=R.hasOwnProperty,F=R.toString,G=RegExp("^"+D.call(I).replace(s,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),H=b.Symbol,L=N(Object.getPrototypeOf,Object),z=R.propertyIsEnumerable,W=k.splice,V=H?H.isConcatSpreadable:void 0,q=Object.getOwnPropertySymbols,$=Math.max,U=Ce(b,"Map"),Y=Ce(Object,"create");function J(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function K(){this.__data__=Y?Y(null):{}}function Q(e){return this.has(e)&&delete this.__data__[e]}function X(e){var t=this.__data__;if(Y){var n=t[e];return n===r?void 0:n}return I.call(t,e)?t[e]:void 0}function Z(e){var t=this.__data__;return Y?void 0!==t[e]:I.call(t,e)}function ee(e,t){var n;return this.__data__[e]=Y&&void 0===t?r:t,this}function te(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function ne(){this.__data__=[]}function re(e){var t=this.__data__,n=he(t,e),r;return!(n<0)&&(n==t.length-1?t.pop():W.call(t,n,1),!0)}function oe(e){var t=this.__data__,n=he(t,e);return n<0?void 0:t[n][1]}function ae(e){return he(this.__data__,e)>-1}function ie(e,t){var n=this.__data__,r=he(n,e);return r<0?n.push([e,t]):n[r][1]=t,this}function ce(e){var t=-1,n=e?e.length:0;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function le(){this.__data__={hash:new J,map:new(U||te),string:new J}}function ue(e){return Se(this,e).delete(e)}function se(e){return Se(this,e).get(e)}function fe(e){return Se(this,e).has(e)}function pe(e,t){return Se(this,e).set(e,t),this}function de(e){var t=-1,n=e?e.length:0;for(this.__data__=new ce;++t<n;)this.add(e[t])}function ye(e){return this.__data__.set(e,r),this}function be(e){return this.__data__.has(e)}function me(e,t){var n=Le(e)||He(e)?E(e.length,String):[],r=n.length,o=!!r;for(var a in e)!t&&!I.call(e,a)||o&&("length"==a||Re(a,r))||n.push(a);return n}function he(e,t){for(var n=e.length;n--;)if(Ge(e[n][0],t))return n;return-1}function ve(e,t,n,r){var o=-1,a=h,i=!0,c=e.length,l=[],u=t.length;if(!c)return l;n&&(t=g(t,x(n))),r?(a=v,i=!1):t.length>=200&&(a=P,i=!1,t=new de(t));e:for(;++o<c;){var s=e[o],f=n?n(s):s;if(s=r||0!==s?s:0,i&&f==f){for(var p=u;p--;)if(t[p]===f)continue e;l.push(s)}else a(t,f,r)||l.push(s)}return l}function ge(e,t,n,r,o){var a=-1,i=e.length;for(n||(n=Te),o||(o=[]);++a<i;){var c=e[a];t>0&&n(c)?t>1?ge(c,t-1,n,r,o):O(o,c):r||(o[o.length]=c)}return o}function Oe(e,t,n){var r=t(e);return Le(e)?r:O(r,n(e))}function _e(e){return!(!$e(e)||Be(e))&&(Ve(e)||C(e)?G:f).test(Fe(e));var t}function we(e){if(!$e(e))return De(e);var t=Me(e),n=[];for(var r in e)("constructor"!=r||!t&&I.call(e,r))&&n.push(r);return n}function je(e,t){return Ee(e=Object(e),t,(function(t,n){return n in e}))}function Ee(e,t,n){for(var r=-1,o=t.length,a={};++r<o;){var i=t[r],c=e[i];n(c,i)&&(a[i]=c)}return a}function xe(e,t){return t=$(void 0===t?e.length-1:t,0),function(){for(var n=arguments,r=-1,o=$(n.length-t,0),a=Array(o);++r<o;)a[r]=n[t+r];r=-1;for(var i=Array(t+1);++r<t;)i[r]=n[r];return i[t]=a,m(e,this,i)}}function Pe(e){return Oe(e,Je,ke)}function Se(e,t){var n=e.__data__;return Ae(t)?n["string"==typeof t?"string":"hash"]:n.map}function Ce(e,t){var n=S(e,t);return _e(n)?n:void 0}J.prototype.clear=K,J.prototype.delete=Q,J.prototype.get=X,J.prototype.has=Z,J.prototype.set=ee,te.prototype.clear=ne,te.prototype.delete=re,te.prototype.get=oe,te.prototype.has=ae,te.prototype.set=ie,ce.prototype.clear=le,ce.prototype.delete=ue,ce.prototype.get=se,ce.prototype.has=fe,ce.prototype.set=pe,de.prototype.add=de.prototype.push=ye,de.prototype.has=be;var Ne=q?N(q,Object):Qe,ke=q?function(e){for(var t=[];e;)O(t,Ne(e)),e=L(e);return t}:Qe;function Te(e){return Le(e)||He(e)||!!(V&&e&&e[V])}function Re(e,t){return!!(t=null==t?9007199254740991:t)&&("number"==typeof e||p.test(e))&&e>-1&&e%1==0&&e<t}function Ae(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}function Be(e){return!!B&&B in e}function Me(e){var t=e&&e.constructor,n;return e===("function"==typeof t&&t.prototype||R)}function De(e){var t=[];if(null!=e)for(var n in Object(e))t.push(n);return t}function Ie(e){if("string"==typeof e||Ye(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}function Fe(e){if(null!=e){try{return D.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function Ge(e,t){return e===t||e!=e&&t!=t}function He(e){return We(e)&&I.call(e,"callee")&&(!z.call(e,"callee")||F.call(e)==i)}var Le=Array.isArray;function ze(e){return null!=e&&qe(e.length)&&!Ve(e)}function We(e){return Ue(e)&&ze(e)}function Ve(e){var t=$e(e)?F.call(e):"";return t==c||t==l}function qe(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}function $e(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function Ue(e){return!!e&&"object"==typeof e}function Ye(e){return"symbol"==typeof e||Ue(e)&&F.call(e)==u}function Je(e){return ze(e)?me(e,!0):we(e)}var Ke=xe((function(e,t){return null==e?{}:(t=g(ge(t,1),Ie),je(e,ve(Pe(e),t)))}));function Qe(){return[]}e.exports=Ke}).call(this,n(4))},function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t){e.exports=n},function(e,t,n){var r,o;e.exports=n(10)()},function(e,t,n){"use strict";(function(e){var n=function(){if("undefined"!=typeof Map)return Map;function e(e,t){var n=-1;return e.some((function(e,r){return e[0]===t&&(n=r,!0)})),n}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(t){var n=e(this.__entries__,t),r=this.__entries__[n];return r&&r[1]},t.prototype.set=function(t,n){var r=e(this.__entries__,t);~r?this.__entries__[r][1]=n:this.__entries__.push([t,n])},t.prototype.delete=function(t){var n=this.__entries__,r=e(n,t);~r&&n.splice(r,1)},t.prototype.has=function(t){return!!~e(this.__entries__,t)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(e,t){void 0===t&&(t=null);for(var n=0,r=this.__entries__;n<r.length;n++){var o=r[n];e.call(t,o[1],o[0])}},t}()}(),r="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,o=void 0!==e&&e.Math===Math?e:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),a="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(o):function(e){return setTimeout((function(){return e(Date.now())}),1e3/60)},i=2;function c(e,t){var n=!1,r=!1,o=0;function i(){n&&(n=!1,e()),r&&l()}function c(){a(i)}function l(){var e=Date.now();if(n){if(e-o<2)return;r=!0}else n=!0,r=!1,setTimeout(c,t);o=e}return l}var l=20,u=["top","right","bottom","left","width","height","size","weight"],s="undefined"!=typeof MutationObserver,f=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=c(this.refresh.bind(this),20)}return e.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},e.prototype.removeObserver=function(e){var t=this.observers_,n=t.indexOf(e);~n&&t.splice(n,1),!t.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){var e;this.updateObservers_()&&this.refresh()},e.prototype.updateObservers_=function(){var e=this.observers_.filter((function(e){return e.gatherActive(),e.hasActive()}));return e.forEach((function(e){return e.broadcastActive()})),e.length>0},e.prototype.connect_=function(){r&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),s?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){r&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(e){var t=e.propertyName,n=void 0===t?"":t,r;u.some((function(e){return!!~n.indexOf(e)}))&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),p=function(e,t){for(var n=0,r=Object.keys(t);n<r.length;n++){var o=r[n];Object.defineProperty(e,o,{value:t[o],enumerable:!1,writable:!1,configurable:!0})}return e},d=function(e){var t;return e&&e.ownerDocument&&e.ownerDocument.defaultView||o},y=E(0,0,0,0);function b(e){return parseFloat(e)||0}function m(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.reduce((function(t,n){var r;return t+b(e["border-"+n+"-width"])}),0)}function h(e){for(var t,n={},r=0,o=["top","right","bottom","left"];r<o.length;r++){var a=o[r],i=e["padding-"+a];n[a]=b(i)}return n}function v(e){var t=e.getBBox();return E(0,0,t.width,t.height)}function g(e){var t=e.clientWidth,n=e.clientHeight;if(!t&&!n)return y;var r=d(e).getComputedStyle(e),o=h(r),a=o.left+o.right,i=o.top+o.bottom,c=b(r.width),l=b(r.height);if("border-box"===r.boxSizing&&(Math.round(c+a)!==t&&(c-=m(r,"left","right")+a),Math.round(l+i)!==n&&(l-=m(r,"top","bottom")+i)),!_(e)){var u=Math.round(c+a)-t,s=Math.round(l+i)-n;1!==Math.abs(u)&&(c-=u),1!==Math.abs(s)&&(l-=s)}return E(o.left,o.top,c,l)}var O="undefined"!=typeof SVGGraphicsElement?function(e){return e instanceof d(e).SVGGraphicsElement}:function(e){return e instanceof d(e).SVGElement&&"function"==typeof e.getBBox};function _(e){return e===d(e).document.documentElement}function w(e){return r?O(e)?v(e):g(e):y}function j(e){var t=e.x,n=e.y,r=e.width,o=e.height,a="undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object,i=Object.create(a.prototype);return p(i,{x:t,y:n,width:r,height:o,top:n,right:t+r,bottom:o+n,left:t}),i}function E(e,t,n,r){return{x:e,y:t,width:n,height:r}}var x=function(){function e(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=E(0,0,0,0),this.target=e}return e.prototype.isActive=function(){var e=w(this.target);return this.contentRect_=e,e.width!==this.broadcastWidth||e.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},e}(),P=function(){function e(e,t){var n=j(t);p(this,{target:e,contentRect:n})}return e}(),S=function(){function e(e,t,r){if(this.activeObservations_=[],this.observations_=new n,"function"!=typeof e)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=t,this.callbackCtx_=r}return e.prototype.observe=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof d(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)||(t.set(e,new x(e)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof d(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)&&(t.delete(e),t.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach((function(t){t.isActive()&&e.activeObservations_.push(t)}))},e.prototype.broadcastActive=function(){if(this.hasActive()){var e=this.callbackCtx_,t=this.activeObservations_.map((function(e){return new P(e.target,e.broadcastRect())}));this.callback_.call(e,t,e),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),C="undefined"!=typeof WeakMap?new WeakMap:new n,N=function(){function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=f.getInstance(),r=new S(t,n,this);C.set(this,r)}return e}();["observe","unobserve","disconnect"].forEach((function(e){N.prototype[e]=function(){var t;return(t=C.get(this))[e].apply(t,arguments)}}));var k=void 0!==o.ResizeObserver?o.ResizeObserver:N;t.a=k}).call(this,n(4))},function(e,t,n){n(9),e.exports=n(12)},function(e,t,n){},function(e,t,n){"use strict";var r=n(11);function o(){}function a(){}a.resetWarningCache=o,e.exports=function(){function e(e,t,n,o,a,i){if(i!==r){var c=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:o};return n.PropTypes=n,n}},function(e,t,n){"use strict";var r="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";e.exports=r},function(e,t,n){"use strict";n.r(t),n.d(t,"PageHeader",(function(){return Lt})),n.d(t,"PageFooter",(function(){return It})),n.d(t,"PageContent",(function(){return Jt})),n.d(t,"PageAside",(function(){return Wt})),n.d(t,"PageNav",(function(){return qt})),n.d(t,"Page",(function(){return yn})),n.d(t,"Block",(function(){return At})),n.d(t,"BlockCell",(function(){return Ct})),n.d(t,"BlockCellItem",(function(){return gn})),n.d(t,"Text",(function(){return nt})),n.d(t,"P",(function(){return _t})),n.d(t,"RowColContainer",(function(){return En})),n.d(t,"Row",(function(){return Cn})),n.d(t,"Col",(function(){return Rn})),n.d(t,"ProCard",(function(){return ce}));var r=n(0),o=n.n(r),a=n(1),i=n.n(a),c=n(2),l=n(5),u=n.n(l),s=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},f=c.Icon.createFromIconfontCN({scriptUrl:"/fontslib/font_2535522_4oyyibdwj5v.js"}),p=function e(t){var n=t.type,o=s(t,["type"]);return r.createElement(f,Object.assign({type:"icon".concat(n)},o))},d=c.Balloon.Tooltip,y=function e(t){var n=t.className,o=s(t,["className"]);return r.createElement("div",Object.assign({className:i()("pro-layout-toggle-icon-group",n)},o))},b;r.forwardRef((function(e,t){var n=e.title,o=e.active,a=e.type,c=e.size,l=e.className,u=s(e,["title","active","type","size","className"]),f=r.createElement("span",Object.assign({ref:t},u,{className:i()("pro-layout-toggle-icon",{"pro-layout-toggle-icon--active":o},l)}),r.createElement(p,{type:a,size:c}));return n?r.createElement(d,{trigger:f},n):f})).defaultProps={size:"small"};var m=n(3),h=n.n(m),v,g=function e(t,n,o){var a,i,c,l=o||[null===(a=n.displayName)||void 0===a?void 0:a.toLowerCase()],u=(Array.isArray(l)?l:[l]).filter((function(e){return e})).map((function(e){return e.toLowerCase()}));if(!u||!u.length)return!1;if(r.isValidElement(t)){var s=null===(c=null===(i=t.type)||void 0===i?void 0:i.displayName)||void 0===c?void 0:c.toLowerCase();return!!s&&u.includes(s)}return!1};function O(e){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function w(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function j(e,t,n){return t&&w(e.prototype,t),n&&w(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function E(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&x(e,t)}function x(e,t){return(x=Object.setPrototypeOf||function e(t,n){return t.__proto__=n,t})(e,t)}function P(e){var t=N();return function n(){var r=k(e),o;if(t){var a=k(this).constructor;o=Reflect.construct(r,arguments,a)}else o=r.apply(this,arguments);return S(this,o)}}function S(e,t){if(t&&("object"===O(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return C(e)}function C(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function N(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function k(e){return(k=Object.setPrototypeOf?Object.getPrototypeOf:function e(t){return t.__proto__||Object.getPrototypeOf(t)})(e)}var T=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},R=function(e){E(n,e);var t=P(n);function n(){var e;return _(this,n),(e=t.apply(this,arguments)).getDataSource=function(){var t=e.props,n=t.dataSource,o=t.children,a=t.text,i={};a&&(i={type:"primary",text:!0});var l=[];return o?r.Children.forEach(o,(function(e){g(e,c.Button)?l.push(Object.assign(Object.assign({},i),e.props)):g(e,null,["SubmitButton","ResetButton","Submit","Reset","Form.Submit","Form.Reset","Config(Button)"])&&l.push(e)})):Array.isArray(n)&&n.forEach((function(e){l.push(Object.assign(Object.assign({},i),e))})),l},e.getVisibleDataSourceAndCollapseDataSource=function(t){var n=e.props.visibleButtonCount;if(!1===n)return{visible:t,collapse:[]};var r=[],o=[];return t.forEach((function(e,t){t>=n?o.push(e):r.push(e)})),{visible:r,collapse:o}},e}return j(n,[{key:"renderVisible",value:function e(t,n){var o=[];return t.forEach((function(e,t){r.isValidElement(e)?o.push(e):o.push(r.createElement(c.Button,Object.assign({key:t,text:n},e)))})),o}},{key:"renderCollapse",value:function e(t){var n=this.props,o=n.moreMenuButtonProps,a=n.text,i=t.map((function(e){return"object"===O(e.props)?h()(e.props,["type","text"]):h()(e,["type","text"])}));if(t.length>0)return r.createElement(c.MenuButton,Object.assign({popupProps:{align:"tr br"},label:"",text:a,type:a?"primary":"normal"},o),i.map((function(e){var t=e.children,n=T(e,["children"]);return r.createElement(c.Menu.Item,Object.assign({},n),t)})))}},{key:"render",value:function e(){var t=this.props,n=t.style,o=t.className,a=t.text,l=t.visibleButtonCount,u=t.dataSource,s=t.moreMenuButtonProps,f=t.i18nBundle,p=T(t,["style","className","text","visibleButtonCount","dataSource","moreMenuButtonProps","i18nBundle"]),d=i()("pro-layout-button-group",o),y=this.getDataSource(),b=this.getVisibleDataSourceAndCollapseDataSource(y),m=b.visible,h=b.collapse;return r.createElement(c.Box,Object.assign({direction:"row",spacing:8,className:d,style:n},p),this.renderVisible(m,a),this.renderCollapse(h))}}]),n}(r.Component);function A(e){return(A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}R.defaultProps={visibleButtonCount:3,moreMenuButtonProps:{}},R.displayName="ButtonGroup";var B=c.Balloon;function M(e){return e&&Array.isArray(e)?e.map((function(e){return e.label&&!e.children&&(e.children=e.label),e})):[]}var D=function e(t){var n=t.title,o=t.description,a=t.hasCollapse,l=t.actionBar,u=t.collapsed,s=t.onCollapse,f=t.explanation,d=t.explanationTooltipProps,y=t.hasDivider,b=t.tagGroup,m=t.actionButtons,h=t.text,v=void 0===h||h,g=t.visibleButtonCount;return r.createElement("div",{className:i()("pro-layout-card-header",{"pro-layout-card-header--collapsed":u,"has-description":o}),onClick:u?s:void 0},r.createElement("div",{className:"pro-layout-card-header__content"},r.createElement("div",{className:"pro-layout-card-header__hd"},n&&r.createElement("div",{className:"pro-layout-card-header__title"},n),n&&f&&r.createElement("div",{className:"pro-layout-card-header__tooltip"},r.createElement(B,Object.assign({trigger:r.createElement(p,{type:"ic_error2",size:"xs"})},d),f)),b.map((function(e){var t=e,n="blue";return"object"===A(e)&&(t=e.label,e.color&&(n=e.color)),r.createElement(c.Tag,{color:n,size:"small"},t)}))),r.createElement("div",{className:"pro-layout-card-header__ft"},l&&r.createElement("div",{className:"pro-layout-card-header__action-bar"},l),r.createElement(R,{dataSource:M(m),text:v,visibleButtonCount:g}),a&&r.createElement(r.Fragment,null,r.createElement("div",{className:"vertical-divider"}),r.createElement(c.Button,{className:i()("pro-layout-card-header__collapse-btn",{"pro-layout-card-header__collapse-btn--collapsed":u}),text:v,onClick:s,type:v?"primary":"normal"},u?r.createElement(r.Fragment,null,r.createElement("span",null,"\u5c55\u5f00\xa0"),r.createElement(p,{size:"xs",type:"triangle-down"})):r.createElement(r.Fragment,null,r.createElement("span",null,"\u6536\u8d77\xa0"),r.createElement(p,{size:"xs",type:"triangle-up"})))))),o?r.createElement("div",{className:"pro-layout-card-header__desc"},o):null,y&&!u&&r.createElement("div",{className:i()("pro-layout-card-header__divider")}))};function I(e,t){return z(e)||L(e,t)||G(e,t)||F()}function F(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function G(e,t){if(e){if("string"==typeof e)return H(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?H(e,t):void 0}}function H(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function L(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r=[],o=!0,a=!1,i,c;try{for(n=n.call(e);!(o=(i=n.next()).done)&&(r.push(i.value),!t||r.length!==t);o=!0);}catch(e){a=!0,c=e}finally{try{o||null==n.return||n.return()}finally{if(a)throw c}}return r}}function z(e){if(Array.isArray(e))return e}function W(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"value"in e,n=Object(r.useState)(e.defaultValue),o=I(n,2),a=o[0],i=o[1],c=Object(r.useRef)();c.current=function(n){var r="function"==typeof n?n(a):n;if(t||i(r),e.onChange){for(var o=arguments.length,c=new Array(o>1?o-1:0),l=1;l<o;l++)c[l-1]=arguments[l];e.onChange.apply(e,[r].concat(c))}};var l=Object(r.useCallback)((function(){return c.current.apply(c,arguments)}),[]);return Object(r.useEffect)((function(){t&&i(e.value)}),[t,e.value]),[t?e.value:a,l]}function V(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function q(e,t){return K(e)||J(e,t)||U(e,t)||$()}function $(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function U(e,t){if(e){if("string"==typeof e)return Y(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Y(e,t):void 0}}function Y(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function J(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r=[],o=!0,a=!1,i,c;try{for(n=n.call(e);!(o=(i=n.next()).done)&&(r.push(i.value),!t||r.length!==t);o=!0);}catch(e){a=!0,c=e}finally{try{o||null==n.return||n.return()}finally{if(a)throw c}}return r}}function K(e){if(Array.isArray(e))return e}D.defaultProps={onCollapse:function e(){},hasDivider:!0,tagGroup:[]},D.displayName="CardHeader";var Q=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},X="pro-layout",Z="".concat("pro-layout","-pro-card"),ee=function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(t&&!(n>100))return n++,t.parentElement&&t.clientHeight<=t.parentElement.clientHeight?e(t.parentElement,n):t},te=function e(t){var n=t.operations,o=void 0===n?[]:n,a=t.operationConfig,l=void 0===a?{}:a,s=t.lastSaveTime,f=l.align,p=void 0===f?"center":f,d=l.fixed,y=void 0!==d&&d,b=l.showSaveTime,m,h=q(r.useState(null),2),v=h[0],g=h[1];r.useEffect((function(){return function(){v&&v.remove()}}),[v]);var O=r.useRef(),_=i()("".concat(Z,"-operation-container"),V({},"".concat(Z,"-operation-fixed"),y));if(y){var w=ee(O.current);if(w!==O.current)if(w&&!v){var j=document.createElement("div");j.style.setProperty("height","60px"),j.setAttribute("id","fixPlaceHolder"),g(j),w.append(j)}else w&&w.append(v)}else v&&v.remove();var E=b?r.createElement(c.Button,{text:!0},r.createElement(c.Icon,{type:"success",style:{color:"#1DC11D"}}),u()(s).fromNow()):null;return o&&o.length>0?r.createElement("div",{ref:O,className:_},r.createElement(c.Box,{spacing:8,direction:"row",justify:p,align:"center"},o.map((function(e,t){var n=e.content,o=e.action,a=e.onClick,i=Q(e,["content","action","onClick"]),l;return"submit"===o?(l=c.Form.Submit,i.validate=!0,i.htmlType="submit"):l="reset"===o?c.Form.Reset:c.Button,a&&(i.onClick=a),r.createElement(l,Object.assign({key:"operation-".concat(t)},i),n)})),E)):null},ne=function e(t){var n=t.title,o=t.description,a=t.visibleButtonCount,l=t.className,u=t.actionBar,s=t.actionButtons,f=t.loading,p=t.tagGroup,d=t.style,y=t.hasCollapse,b=t.isDialogCard,m=t.explanation,h=t.explanationTooltipProps,v=t.children,g=t.segmentLine,O=t.defaultCollapse,_=t.setCollapse,w=t.isCollapse,j=t.hasDivider,E=t.operations,x=t.operationConfig,P=t.lastSaveTime,S=t.bodyPadding,C=t.text,N=Q(t,["title","description","visibleButtonCount","className","actionBar","actionButtons","loading","tagGroup","style","hasCollapse","isDialogCard","explanation","explanationTooltipProps","children","segmentLine","defaultCollapse","setCollapse","isCollapse","hasDivider","operations","operationConfig","lastSaveTime","bodyPadding","text"]),k,T=q(W({value:w,defaultValue:O,onChange:_},"isCollapse"in t),2),R=T[0],A=T[1],B=v,M="flow",I=r.createElement(r.Fragment,null,(n||y||u||s||p)&&r.createElement(D,{text:C,title:n,description:o,visibleButtonCount:a,hasCollapse:y,actionBar:u,actionButtons:s,tagGroup:p,collapsed:R,onCollapse:function e(){return A(!R)},explanation:m,explanationTooltipProps:h,hasDivider:j}),r.createElement("div",{className:i()("pro-layout-card-body",{})},!R&&r.createElement("div",{className:i()("pro-layout-card-body__panel","pro-layout-card-body__panel--".concat("flow"),V({},"pro-layout-card-body__nopadding",S))},B)),!R&&r.createElement("div",{className:i()("pro-layout-card-footer-actions",{})},r.createElement(te,{operations:E,operationConfig:x,lastSaveTime:P})));return r.createElement("div",Object.assign({className:i()("pro-layout-card",V({"pro-layout-card--dialog":b},l,l)),style:d||{}},N,{"data-name":"Card"}),f?r.createElement(c.Loading,{visible:!0,inline:!1,size:"medium"},I):I)};function re(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function oe(e){return(oe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}ne.defaultProps={segmentLine:!1,loading:!1,isDialogCard:!1,hasCollapse:!1,defaultCollapse:!1},ne.displayName="Card";var ae=c.Balloon.Tooltip,ie=function e(t){var n=t.children,o=t.title,a=t.className,l=t.loading,u=t.segmentLine,s=t.actionBar,f=t.explanation,p=t.explanationTooltipProps,d=t.tagGroup,y=t.id,b=t.headerDivider,m=t.footerDivider,h=t.hasDividerIndent,v=t.noBullet,g=l,O=r.createElement(r.Fragment,null,(o||s)&&r.createElement("div",{className:"pro-layout-card-section-header"},o&&r.createElement("div",{className:"pro-layout-card-section-header__hd"},r.createElement("div",{className:i()("pro-layout-card-section-header__title",{"pro-layout-card-section-header-noBullet":v})},o),r.createElement("div",{className:"pro-layout-card-section-header__tooltip"},f&&r.createElement(ae,Object.assign({trigger:r.createElement(c.Icon,{type:"ic_error2",size:"xs"})},p),f)),d.map((function(e){var t=e,n="blue";return"object"===oe(e)&&(t=e.label,e.color&&(n=e.color)),r.createElement(c.Tag,{color:n,size:"small"},t)}))),r.createElement("div",{className:"pro-layout-card-section-header__hd"},s&&r.createElement("div",{className:"pro-layout-card-section-header__action-bar"},s))),b&&r.createElement("div",{className:i()("pro-layout-card-section-header__divider",{"pro-layout-section-divider-indent":h})}),r.createElement("div",{className:"pro-layout-card-section-body"},n),m&&r.createElement("div",{className:i()("pro-layout-card-section-footer__divider",{"pro-layout-section-divider-indent":h})}));return r.createElement("div",{id:y,className:i()("pro-layout-card-section",re({"pro-layout-card-section--segment-line":u},a,a))},g?r.createElement(c.Loading,{inline:!1,visible:g,size:"medium"},O):O)};ie.displayName="CardSection",ie.defaultProps={title:null,segmentLine:!1,loading:!1,tagGroup:[],noBullet:!1},ne.CardSection=ie;var ce=ne,le=n(6),ue=n.n(le);function se(e){return(se="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var fe=function e(){var t="production",n=!1;try{n=!0}catch(e){}return n},pe={"body-1":"body2","body-2":"body1",subhead:"h6",title:"h5",headline:"h4","display-1":"h3","display-2":"h2","display-3":"h1"},de=function e(t){var n;return{top:"flex-start",bottom:"flex-end",middle:"center"}[t]||t},ye=function e(t){var n;return{"flex-start":"left","flex-end":"right"}[t]||t},be=function e(t){var n;return{"flex-start":"top","flex-end":"bottom",center:"middle"}[t]||t};function me(e){return/-/.test(e)?e.toLowerCase().replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()})):e||""}function he(e){return Object.prototype.toString.call(e).replace(/\[object\s|]/g,"")}function ve(e){var t=!!e&&"length"in e&&e.length,n;return"Array"===he(e)||0===t||"number"==typeof t&&t>0&&t-1 in e}function ge(e,t,n){var r=-1===n,o=e.length,a,i=r?o-1:0;if(ve(e))for(;i<o&&i>=0&&!1!==(a=t.call(e[i],e[i],i));r?i--:i++);else for(i in e)if(e.hasOwnProperty(i)&&!1===(a=t.call(e[i],e[i],i)))break;return e}var Oe={cssFloat:1,styleFloat:1,float:1},_e="undefined"!=typeof window&&!!window.document&&!!document.createElement,we=/margin|padding|width|height|max|min|offset|size|top/i,je=1,Ee=1,xe=1,Pe=1;function Se(e,t,n){if(!_e||!e)return!1;"object"===se(t)&&2===arguments.length?ge(t,(function(t,n){return Se(e,n,t)})):(t=Oe[t]?"cssFloat"in e.style?"cssFloat":"styleFloat":t,"number"==typeof n&&we.test(t)&&(n="".concat(n,"px")),e.style[me(t)]=n)}var Ce=fe,Ne=pe,ke=de,Te=ye,Re=be,Ae=Se;function Be(e){return(Be="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var Me=["prefix","className"];function De(){return(De=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function Ie(e,t){if(null==e)return{};var n=Fe(e,t),r,o;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)r=a[o],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function Fe(e,t){if(null==e)return{};var n={},r=Object.keys(e),o,a;for(a=0;a<r.length;a++)o=r[a],t.indexOf(o)>=0||(n[o]=e[o]);return n}function Ge(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function He(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Le(e,t,n){return t&&He(e.prototype,t),n&&He(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function ze(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&We(e,t)}function We(e,t){return(We=Object.setPrototypeOf||function e(t,n){return t.__proto__=n,t})(e,t)}function Ve(e){var t=Ue();return function n(){var r=Ye(e),o;if(t){var a=Ye(this).constructor;o=Reflect.construct(r,arguments,a)}else o=r.apply(this,arguments);return qe(this,o)}}function qe(e,t){if(t&&("object"===Be(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return $e(e)}function $e(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ue(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function Ye(e){return(Ye=Object.setPrototypeOf?Object.getPrototypeOf:function e(t){return t.__proto__||Object.getPrototypeOf(t)})(e)}function Je(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ke=function(e){var t=function(t){ze(r,t);var n=Ve(r);function r(){var t;return Ge(this,r),t=n.call(this),fe()||console.error("Warning: Text.".concat(e.toUpperCase(),' is depracted, use <P type="').concat(e,'"> instead')),t}return Le(r,[{key:"render",value:function t(){var n=this.props,r=n.prefix,a=n.className,i=Ie(n,Me);return o.a.createElement(nt,De({},i,{component:e,className:"".concat(a||""," ").concat(r,"typography-title")}))}}]),r}(r.Component);return Je(t,"propTypes",{prefix:ue.a.string}),Je(t,"defaultProps",{prefix:"pro-layout-"}),t.displayName=e.toUpperCase(),t};function Qe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Xe=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},Ze=function e(t,n){var o,a=t.prefix,c=t.className,l=t.type,u=t.style,s=t.component,f=void 0===s?"span":s,p=t.strong,d=t.underline,y=t.delete,b=t.code,m=t.mark,h=t.color,v=Xe(t,["prefix","className","type","style","component","strong","underline","delete","code","mark","color"]),g=t.children,O=pe[l]||l,_=i()(c,(Qe(o={},"".concat(a,"text"),!0),Qe(o,"".concat(a,"text-").concat(O),O),o));if("string"==typeof g&&-1!==g.indexOf("\n")){var w=g.split("\n"),j=[];w.forEach((function(e){j.push(e),j.push(r.createElement("br",null))})),j.pop(),g=j}var E=f;p&&(g=r.createElement("strong",null,g)),d&&(g=r.createElement("u",null,g)),y&&(g=r.createElement("del",null,g)),b&&(g=r.createElement("code",null,g)),m&&(g=r.createElement("mark",null,g));var x={};return h&&(x=Object.assign({color:h},u)),r.createElement(E,Object.assign({},v,{style:x,className:_,ref:n}),g)},et=r.forwardRef(Ze);et.defaultProps={prefix:"pro-layout-"};var tt=function e(t){var n=t.prefix,o=t.className,a=t.component,c=Xe(t,["prefix","className","component"]),l=i()("".concat(n,"text-paragraph"),o);return fe()||console.error("Warning: Text.Paragraph is depracted, use P instead"),r.createElement(Ze,Object.assign({},c,{className:l,component:a}))};et.H1=Ke("h1"),et.H2=Ke("h2"),et.H3=Ke("h3"),et.H4=Ke("h4"),et.H5=Ke("h5"),et.H6=Ke("h6"),et.Paragraph=tt;var nt=et;function rt(e){return(rt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ot(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function at(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function it(e,t,n){return t&&at(e.prototype,t),n&&at(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function ct(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&lt(e,t)}function lt(e,t){return(lt=Object.setPrototypeOf||function e(t,n){return t.__proto__=n,t})(e,t)}function ut(e){var t=pt();return function n(){var r=dt(e),o;if(t){var a=dt(this).constructor;o=Reflect.construct(r,arguments,a)}else o=r.apply(this,arguments);return st(this,o)}}function st(e,t){if(t&&("object"===rt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return ft(e)}function ft(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function pt(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function dt(e){return(dt=Object.setPrototypeOf?Object.getPrototypeOf:function e(t){return t.__proto__||Object.getPrototypeOf(t)})(e)}function yt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var bt=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},mt=function e(t,n){var o,a=t.prefix,c=void 0===a?"pro-layout-":a,l=t.children,u=t.width,s=t.direction,f=void 0===s?"hoz":s,p=t.align,d=void 0===p?"inherit":p,y=t.alignSelf,b=void 0===y?"inherit":y,m=t.verAlign,h=void 0===m?"inherit":m,v=t.className,g=t.type,O=void 0===g?"body2":g,_=bt(t,["prefix","children","width","direction","align","alignSelf","verAlign","className","type"]),w=pe[O]||O,j=r.Children.map(l,(function(e){return"string"==typeof e?e.trim()?r.createElement(nt,{},e):null:e})),E=i()(v,(yt(o={},"".concat(c,"p-cell"),!0),yt(o,"".concat(c,"p-cell-").concat(w),w),yt(o,"".concat(c,"p-cell-").concat(f),f),yt(o,"".concat(c,"p-cell-align-").concat(d),d),yt(o,"".concat(c,"p-cell-veralign-").concat(h),h),yt(o,"".concat(c,"p-block"),j&&j.length<2),o)),x=Object.assign({width:u,alignSelf:de(b),justifyContent:"center"},t.style);return r.createElement("div",Object.assign({},_,{className:E,style:x,ref:n}),j)},ht=function e(t,n){var o,a=t.prefix,c=void 0===a?"pro-layout-":a,l=t.children,u=t.className,s=t.wrap,f=t.type,p=void 0===f?"body2":f,d=t.textSpacing,y=t.flex,b=bt(t,["prefix","children","className","wrap","type","textSpacing","flex"]),m=pe[p]||p,h=b.verAlign,v=void 0===h?"baseline":h,g=b.align,O=b.full,_=bt(b,[]),w=!1;"full"===g&&(O=!0),g=ye(g),v=be(v);var j=[],E=-1,x="",P=0|r.Children.count(l),S=function e(t){x&&j.push(r.createElement(nt,{},t)),E=-1,x=""};r.Children.map(l,(function(e,t){"string"==typeof e?(E+1===t?(E+=1,x+=e):(E=t,x=e),t===P-1&&S(x)):(S(x),e&&e.type&&"p_cell"===e.type._typeMark?(w=!0,j.push(r.cloneElement(e,{type:e.props.type||p}))):j.push(e))}));var C=i()(u,(yt(o={},"".concat(c,"p"),!0),yt(o,"".concat(c,"p-").concat(m),m),yt(o,"".concat(c,"p-align-").concat(g),g),yt(o,"".concat(c,"p-veralign-").concat(v),v),yt(o,"".concat(c,"p-no-text-space "),!d),yt(o,"".concat(c,"p-flex"),w||y),yt(o,"".concat(c,"p-align-full"),O),yt(o,"".concat(c,"p-block"),j&&j.length<2),o)),N=Object.assign({flexWrap:s?"wrap":void 0},t.style);return r.createElement("div",Object.assign({},_,{className:C,style:N,ref:n}),j)},vt,gt;r.forwardRef?(vt=r.forwardRef(ht)).displayName="Paragraph":vt=function(e){ct(n,e);var t=ut(n);function n(){var e;return ot(this,n),(e=t.apply(this,arguments)).state={},e}return it(n,[{key:"render",value:function e(){return r.createElement(ht,Object.assign({},this.props))}}]),n}(r.Component);vt.Cell=r.forwardRef(mt),vt.Cell._typeMark="p_cell";var Ot,_t=vt;function wt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var jt=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},Et=function e(t,n){var o,a=t.prefix,c=void 0===a?"pro-layout-":a,l=t.direction,u=jt(t,["prefix","direction"]),s=i()((wt(o={},"".concat(c,"block-divider"),!0),wt(o,"".concat(c,"block-divider-ver"),"ver"===l),o));return r.createElement("div",Object.assign({},u,{className:s,ref:n}))},xt=r.forwardRef(Et),Pt=function e(t,n){var o,a,c=t.prefix,l=void 0===c?"pro-layout-":c,u=t.className,s=t.children,f=t.content,p=t.rowSpan,d=t.style,y=t.colSpan,b=t.title,m=t.extra,h=t.mode,v=t.childMode,g=t.childWidth,O=t.childTotalColumns,_=jt(t,["prefix","className","children","content","rowSpan","style","colSpan","title","extra","mode","childMode","childWidth","childTotalColumns"]),w=i()(u,(wt(o={},"".concat(l,"block-cell"),!0),wt(o,"".concat(l,"block-cell-").concat(h),h),o)),j=i()((wt(a={},"".concat(l,"block-cell-body"),!0),wt(a,"".concat(l,"block-cell-").concat(v),v),a)),E=Object.assign({gridRowEnd:"span ".concat(p),gridColumnEnd:"span ".concat(y)},d),x="string"==typeof g?g:"".concat(g,"px"),P=O;"flow"===v?P="repeat(auto-fit, minmax(".concat(x,", 1fr))"):"number"==typeof O&&(P="repeat(".concat(O,", minmax(0px, ").concat((100/O).toFixed(5),"%)"));var S={gridTemplateColumns:P},C=r.createElement(ce,Object.assign({actionBar:m},t),f||s);return"procard"===h?r.createElement("div",Object.assign({className:w},_,{style:E,ref:n}),C):r.createElement("div",Object.assign({className:w},_,{style:E,ref:n}),b?r.createElement(r.Fragment,null,r.createElement(_t,{className:"".concat(l,"block-title"),type:"h6",align:"space-between"},r.createElement(_t.Cell,null,b),m&&r.createElement(_t.Cell,{type:"body2"},m))):null,r.createElement("div",{className:j,style:S},f||s))},St=r.forwardRef(Pt);St._typeMark="block.cell",St.defaultProps={prefix:"pro-layout-",colSpan:1,rowSpan:1};var Ct=St;function Nt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var kt=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},Tt=function e(t,n){var o,a=t.prefix,c=t.dataSource,l=t.title,u=t.strict,s=t.className,f=t.mode,p=t.background,d=t.noPadding,y=t.noBorder,b=t.children,m=t.style,h=t.rowSpan,v=t.colSpan,g=t.childTotalColumns,O=t.extra,_=t.colGap,w=t.rowGap,j=kt(t,["prefix","dataSource","title","strict","className","mode","background","noPadding","noBorder","children","style","rowSpan","colSpan","childTotalColumns","extra","colGap","rowGap"]),E=i()(s,(Nt(o={},"".concat(a,"block"),!0),Nt(o,"".concat(a,"block-").concat(f),f),Nt(o,"".concat(a,"block-").concat(p),!!p),Nt(o,"".concat(a,"block-no-padding"),d),Nt(o,"".concat(a,"block-no-border"),y),o)),x=Object.assign({gridRowEnd:"span ".concat(h),gridColumnEnd:"span ".concat(v)},m),P={gridTemplateColumns:"number"==typeof g?"repeat(".concat(g,", minmax(0px, ").concat((100/g).toFixed(5),"%)"):g,gridColumnGap:"number"==typeof _?"".concat(_,"px"):_,gridRowGap:"number"==typeof w?"".concat(w,"px"):w},S=!1;return r.Children.map(b,(function(e){e&&e.type&&"block.cell"===e.type._typeMark||(S=!(c&&c.length||!b))})),r.createElement("div",Object.assign({className:E,style:x},j,{ref:n}),l?r.createElement(r.Fragment,null,r.createElement("div",{className:"".concat(a,"block-header")},r.createElement(_t,{className:"".concat(a,"block-title"),type:"h5",align:"space-between"},r.createElement(_t.Cell,null,l),O&&r.createElement(_t.Cell,{type:"body2"},O))),"inset"===f&&r.createElement(xt,null)):null,r.createElement("div",{className:"".concat(a,"block-body"),style:P},c&&c.map((function(e,t){return r.createElement(Ct,Object.assign({key:t},e))})),!u&&S?r.createElement(Ct,null,b):b))},Rt=r.forwardRef(Tt);Rt.Cell=Ct,Rt.Divider=xt,Rt._typeMark="fusion.block",Rt.defaultProps={prefix:"pro-layout-",childTotalColumns:1,colSpan:12,rowSpan:1,noPadding:!1,noBorder:!1,mode:"inset"};var At=Rt,Bt=n(7);function Mt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Dt,It=function e(t){var n,o=t.prefix,a=void 0===o?"pro-layout-":o,c=t.style,l=t.fixed,u=t.children,s=Object(r.useRef)(null),f=i()((Mt(n={},"".concat(a,"page-footer"),!0),Mt(n,"".concat(a,"page-fixed"),l),n));return u?r.createElement("footer",{ref:s,className:f,style:c},r.createElement("div",{className:"".concat(a,"page-footer-content")},u)):null};function Ft(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Gt=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},Ht,Lt=function e(t){var n,o=t.prefix,a=void 0===o?"pro-layout-":o,c=t.style,l=t.children,u=t.background,s=t.headerDivider,f=Gt(t,["prefix","style","children","background","headerDivider"]),p=i()((Ft(n={},"".concat(a,"page-pro-header"),!0),Ft(n,"".concat(a,"page-pro-header-divider"),s),Ft(n,"".concat(a,"page-bg-").concat(u),!!u),n));return l?r.createElement("header",Object.assign({className:p,style:c},f),r.createElement("div",{className:"".concat(a,"page-pro-header-content")},l)):null},zt=function e(t){var n=t.prefix,o=t.width,a=t.children,i=t.style,c="".concat(n,"page-aside"),l=Object.assign({width:"number"==typeof o?"".concat(o,"px"):o},i);return a?r.createElement("aside",{className:c,style:l},a):null};zt.defaultProps={prefix:"pro-layout-"};var Wt=zt,Vt=function e(t){var n=t.prefix,o=t.children,a=t.width,i=t.style,c="".concat(n,"page-nav"),l=Object.assign({width:"number"==typeof a?"".concat(a,"px"):a},i);return o?r.createElement("aside",{className:c,style:l},o):null};Vt.defaultProps={prefix:"pro-layout-"};var qt=Vt;function $t(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ut=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},Yt=function e(t){var n,o,a,l=t.prefix,u=t.columns,s=t.nav,f=t.aside,p=t.main,d=t.children,y=t.contentAlignCenter,b=t.gap,m=t.background,h=t.noPadding,v=t.grid,g=t.isTab,O=t.title,_=t.key,w=t.active,j=t.presetNav,E=t.presetAside,x=t.navProps,P=t.asideProps,S=Ut(t,["prefix","columns","nav","aside","main","children","contentAlignCenter","gap","background","noPadding","grid","isTab","title","key","active","presetNav","presetAside","navProps","asideProps"]),C=g?"pro-layout-":l,N=i()(($t(n={},"".concat(C,"page-main"),!0),$t(n,"".concat(C,"display-grid"),v),n)),k=i()(($t(o={},"".concat(C,"page-content"),!0),$t(o,"".concat(C,"page-content-center"),y),$t(o,"".concat(C,"page-content-nopadding"),h),o)),T=i()(($t(a={},"".concat(C,"page-bg-").concat(m),!!m),$t(a,"".concat(C,"page-min-height-helper"),!0),a)),R={gridTemplateColumns:"repeat(".concat(u,", minmax(0px, 1fr))"),gridGap:b},A=r.createElement("div",Object.assign({className:T},S),r.createElement("section",{className:k},j?r.createElement(qt,Object.assign({},x),s):s,r.createElement("section",{className:N,style:R},p,d),E?r.createElement(Wt,Object.assign({},P),f):f));return g?r.createElement(c.Tab.Item,{title:O,key:_,active:w},A):A};Yt.defaultProps={prefix:"pro-layout-",isTab:!1};var Jt=Yt;function Kt(e){return(Kt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Qt(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Xt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Zt(e,t,n){return t&&Xt(e.prototype,t),n&&Xt(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function en(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&tn(e,t)}function tn(e,t){return(tn=Object.setPrototypeOf||function e(t,n){return t.__proto__=n,t})(e,t)}function nn(e){var t=an();return function n(){var r=cn(e),o;if(t){var a=cn(this).constructor;o=Reflect.construct(r,arguments,a)}else o=r.apply(this,arguments);return rn(this,o)}}function rn(e,t){if(t&&("object"===Kt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return on(e)}function on(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function an(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function cn(e){return(cn=Object.setPrototypeOf?Object.getPrototypeOf:function e(t){return t.__proto__||Object.getPrototypeOf(t)})(e)}function ln(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var un=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},sn=function e(t,n){var o,a=t.prefix,l=t.className,u=t.contentProps,s=void 0===u?{}:u,f=t.gap,p=t.style,d=t.main,y=t.children,b=t.contentAlignCenter,m=t.minHeight,h=t.background,v=t.columns,g=void 0===v?1:v,O=t.grid,_=t.noContentPadding,w=t.isTab,j=t.tabProps,E=t.header,x=t.footer,P=t.nav,S=t.aside,C=t.presetNav,N=t.presetAside,k=t.presetHeader,T=t.presetFooter,R=t.navProps,A=void 0===R?{}:R,B=t.headerProps,M=void 0===B?{}:B,D=t.footerProps,I=void 0===D?{}:D,F=t.asideProps,G=void 0===F?{}:F,H=un(t,["prefix","className","contentProps","gap","style","main","children","contentAlignCenter","minHeight","background","columns","grid","noContentPadding","isTab","tabProps","header","footer","nav","aside","presetNav","presetAside","presetHeader","presetFooter","navProps","headerProps","footerProps","asideProps"]),L=I.fixed,z=void 0!==L&&L,W=i()(l,(ln(o={},"".concat(a,"page"),!0),ln(o,"".concat(a,"page-centermode"),b),ln(o,"".concat(a,"page-bg-").concat(h),!!h),ln(o,"".concat(a,"page-content-tab"),!!w),o)),V=Object.assign(Object.assign({},p),{minHeight:m}),q=function e(){for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];var a=r.useRef();return r.useEffect((function(){n.forEach((function(e){e&&("function"==typeof e?e(a.current):e.current=a.current)}))}),[n]),a},$=Object(r.useRef)(null),U=Object(r.useRef)(null),Y=q(n,U);r.useLayoutEffect((function(){var e=Y.current.getBoundingClientRect();console.log("Input dimensions:",e.width,e.height)}),[n]);var J=T?r.createElement(It,Object.assign({},I),x):x,K=new Bt.a((function(){if(J){var e=U.current,t=$.current,n=e&&e.getBoundingClientRect().width||0,r=t&&t.getBoundingClientRect().height||0;Se(t,"width",n),Se(e,"paddingBottom",z?r:0)}}));Object(r.useEffect)((function(){return K.observe(U.current),function(){K.unobserve(U.current)}}),[z,J]);var Q=r.createElement(Jt,Object.assign({nav:P,aside:S,columns:g,gap:f,grid:O,presetNav:C,presetAside:N,navProps:A,asideProps:G,isTab:!1,noPadding:_,contentAlignCenter:b},s),y),X=w?r.createElement(c.Tab,Object.assign({},j,{navClassName:"pro-layout-page-tab"}),y):Q;return r.createElement("div",Object.assign({ref:Y,className:W,style:V},H),k?r.createElement(Lt,Object.assign({},M),E):E,X,J)},fn,pn;r.forwardRef?(fn=r.forwardRef(sn)).displayName="Page":fn=function(e){en(n,e);var t=nn(n);function n(){var e;return Qt(this,n),(e=t.apply(this,arguments)).state={},e}return Zt(n,[{key:"render",value:function e(){return r.createElement(sn,Object.assign({},this.props))}}]),n}(r.Component);var dn=fn;dn.defaultProps={prefix:"pro-layout-",presetHeader:!1,presetFooter:!1,grid:!1};var yn=dn;function bn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var mn=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},hn=function e(t,n){var o,a=t.prefix,c=void 0===a?"pro-layout-":a,l=t.className,u=t.children,s=t.style,f=t.mode,p=mn(t,["prefix","className","children","style","mode"]),d=i()(l,(bn(o={},"".concat(c,"block-cell-item"),!0),bn(o,"".concat(c,"block-cell-item-").concat(f),f),o)),y=Object.assign({},s);return r.createElement("div",Object.assign({className:d},p,{style:y,ref:n}),r.createElement("div",{className:"".concat(c,"block-cell-item-body")},u))},vn=r.forwardRef(hn);vn._typeMark="block.cell.item",vn.defaultProps={prefix:"pro-layout-"};var gn=vn;function On(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var _n=r.createContext(null),wn=function e(t,n){var o=t.prefix,a=t.className,c=t.children,l=t.rowGap,u=t.colGap,s=i()(a,On({},"".concat(o,"row-col-container"),!0));return r.createElement("div",{className:s,ref:n},r.createElement(_n.Provider,{value:{rowGap:l,colGap:u}},c))},jn=r.forwardRef(wn);jn._typeMark="autolayout_rowcolcontainer",jn.defaultProps={prefix:"pro-layout-",rowGap:0,colGap:0};var En=jn;function xn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Pn=function e(t,n){var o=t.prefix,a=t.className,c=t.minHeight,l=t.children,u=i()(a,xn({},"".concat(o,"row"),!0)),s,f=(Object(r.useContext)(_n)||{}).rowGap,p={minHeight:c,marginTop:"number"==typeof f?"".concat(f,"px"):f};return r.createElement("div",{className:u,style:p,ref:n},l)},Sn=r.forwardRef(Pn);Sn._typeMark="autolayout_row",Sn.defaultProps={prefix:"pro-layout-"};var Cn=Sn;function Nn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var kn=function e(t,n){var o=t.prefix,a=t.className,c=t.children,l=t.colSpan,u=t.colWidth,s=t.minHeight,f=t.justifyContent,p=i()(a,Nn({},"".concat(o,"col"),!0)),d,y=(Object(r.useContext)(_n)||{}).colGap,b={flex:u?"0 0 ".concat(u):"".concat(l," ").concat(l," 0"),minHeight:s,justifyContent:f,marginLeft:"number"==typeof y?"".concat(y,"px"):y};return r.createElement("div",{className:p,style:b,ref:n},c)},Tn=r.forwardRef(kn);Tn._typeMark="autolayout_col",Tn.defaultProps={prefix:"pro-layout-",colSpan:1};var Rn=Tn;At.Cell=Ct}])}));