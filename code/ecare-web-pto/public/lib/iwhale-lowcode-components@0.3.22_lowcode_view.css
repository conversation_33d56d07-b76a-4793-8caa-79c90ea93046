.next-slick-track {
  display: -webkit-box !important;
  display: -webkit-flex !important;
  display: -ms-flexbox !important;
  display: flex !important;
}
.spacer-basic {
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}
.cut {
  width: 100%;
  margin: 12px;
  border-top: 1px solid #47e;
}
.col {
  min-height: 60px;
  padding: 12px;
}
.col-24 {
  width: 100%;
}
.col-23 {
  width: 95.83%;
}
.col-22 {
  width: 91.67%;
}
.col-21 {
  width: 87.5%;
}
.col-20 {
  width: 83.33%;
}
.col-19 {
  width: 79.17%;
}
.col-18 {
  width: 75%;
}
.col-17 {
  width: 70.83%;
}
.col-16 {
  width: 66.67%;
}
.col-15 {
  width: 62.5%;
}
.col-14 {
  width: 58.33%;
}
.col-13 {
  width: 54.17%;
}
.col-12 {
  width: 50%;
}
.col-11 {
  width: 45.83%;
}
.col-10 {
  width: 41.67%;
}
.col-9 {
  width: 37.5%;
}
.col-5 {
  width: 20.83%;
}
.col-7 {
  width: 29.17%;
}
.col-6 {
  width: 25%;
}
.col-4 {
  width: 16.7%;
}
.col-8 {
  width: 33.33%;
}
@media screen and (max-width: 750px) {
  .col-1,
  .col-4,
  .col-6,
  .col-12 {
    width: 100% !important;
  }
  .col-8 {
    width: 100%;
  }
}
.colFixed-24 {
  width: 100%;
}
.colFixed-23 {
  width: 95.83%;
}
.colFixed-22 {
  width: 91.67%;
}
.colFixed-21 {
  width: 87.5%;
}
.colFixed-20 {
  width: 83.33%;
}
.colFixed-19 {
  width: 79.17%;
}
.colFixed-18 {
  width: 75%;
}
.colFixed-17 {
  width: 70.83%;
}
.colFixed-16 {
  width: 66.67%;
}
.colFixed-15 {
  width: 62.5%;
}
.colFixed-14 {
  width: 58.33%;
}
.colFixed-13 {
  width: 54.17%;
}
.colFixed-12 {
  width: 50%;
}
.colFixed-11 {
  width: 45.83%;
}
.colFixed-10 {
  width: 41.67%;
}
.colFixed-9 {
  width: 37.5%;
}
.colFixed-8 {
  width: 33.33%;
}
.colFixed-7 {
  width: 29.17%;
}
.colFixed-6 {
  width: 25%;
}
.colFixed-5 {
  width: 20.83%;
}
.colFixed-4 {
  width: 16.7%;
}
.layout-component,
.layout-component .row-layout-content {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  background-color: transparent;
}
.layout-component .row-layout-content {
  width: 100%;
  position: relative;
  border-top: none;
}
.padding-content {
  padding: 0 200px;
}
@media screen and (max-width: 750px) {
  .layout-component {
    -webkit-flex-wrap: nowrap;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
  }
  .padding-content {
    padding: 0;
  }
}
.row-layout {
  width: 100%;
}
.container {
  height: 100vh;
}
.lce-page {
  background-color: #ebecf0;
  height: 100vh !important;
}
.col-responsive .col-1,
.col-responsive .col-2,
.col-responsive .col-3,
.col-responsive .col-4,
.col-responsive .col-6 {
  width: 100% !important;
}
.col-responsive .col-8 {
  width: 100%;
}
.col-responsive .col-12,
.col-responsive .col-16 {
  width: 100% !important;
}
@font-face {
  font-family: Exo-2;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/exo2/v10/7cHmv4okm5zmbtYoK-4.woff2) format('woff');
  unicode-range: u+0460-052f, u+1c80-1c88, u+20b4, u+2de0-2dff, u+a640-a69f, u+fe2e-fe2f;
}
@font-face {
  font-family: Exo-2;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/exo2/v10/7cHmv4okm5zmbtYoK-4.woff2) format('woff');
  unicode-range: u+0400-045f, u+0490-0491, u+04b0-04b1, u+2116;
}
@font-face {
  font-family: Exo-2;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/exo2/v10/7cHmv4okm5zmbtYoK-4.woff2) format('woff');
  unicode-range: u+0102-0103, u+0110-0111, u+0128-0129, u+0168-0169, u+01a0-01a1, u+01af-01b0, u+1ea0-1ef9, u+20ab;
}
@font-face {
  font-family: Exo-2;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/exo2/v10/7cHmv4okm5zmbtYoK-4.woff2) format('woff');
  unicode-range: u+0100-024f, u+0259, u+1e??, u+2020, u+20a0-20ab, u+20ad-20cf, u+2113, u+2c60-2c7f, u+a720-a7ff;
}
@font-face {
  font-family: Exo-2;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/exo2/v10/7cHmv4okm5zmbtYoK-4.woff2) format('woff');
  unicode-range: u+00??, u+0131, u+0152-0153, u+02bb-02bc, u+02c6, u+02da, u+02dc, u+2000-206f, u+2074, u+20ac, u+2122,
    u+2191, u+2193, u+2212, u+2215, u+feff, u+fffd;
}
