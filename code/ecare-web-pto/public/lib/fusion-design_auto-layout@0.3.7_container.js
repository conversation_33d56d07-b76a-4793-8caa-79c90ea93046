!function e(t,n){"object"==typeof exports&&"object"==typeof module?module.exports=n(require("react")):"function"==typeof define&&define.amd?define(["react"],n):"object"==typeof exports?exports.Container=n(require("react")):t.Container=n(t.React)}(window,(function(e){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function t(){return e.default}:function t(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="./dist/",n(n.s=4)}([function(t,n){t.exports=e},function(e,t,n){var r,o;
/*!
  Copyright (c) 2018 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/!function(){"use strict";var n={}.hasOwnProperty;function i(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var o=typeof r;if("string"===o||"number"===o)e.push(r);else if(Array.isArray(r)){if(r.length){var c=i.apply(null,r);c&&e.push(c)}}else if("object"===o)if(r.toString===Object.prototype.toString)for(var a in r)n.call(r,a)&&r[a]&&e.push(a);else e.push(r.toString())}}return e.join(" ")}e.exports?(i.default=i,e.exports=i):void 0===(o=function(){return i}.apply(t,r=[]))||(e.exports=o)}()},function(e,t,n){var r,o;e.exports=n(6)()},function(e,t,n){"use strict";(function(e){var n=function(){if("undefined"!=typeof Map)return Map;function e(e,t){var n=-1;return e.some((function(e,r){return e[0]===t&&(n=r,!0)})),n}return function(){function t(){this.__entries__=[]}return Object.defineProperty(t.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),t.prototype.get=function(t){var n=e(this.__entries__,t),r=this.__entries__[n];return r&&r[1]},t.prototype.set=function(t,n){var r=e(this.__entries__,t);~r?this.__entries__[r][1]=n:this.__entries__.push([t,n])},t.prototype.delete=function(t){var n=this.__entries__,r=e(n,t);~r&&n.splice(r,1)},t.prototype.has=function(t){return!!~e(this.__entries__,t)},t.prototype.clear=function(){this.__entries__.splice(0)},t.prototype.forEach=function(e,t){void 0===t&&(t=null);for(var n=0,r=this.__entries__;n<r.length;n++){var o=r[n];e.call(t,o[1],o[0])}},t}()}(),r="undefined"!=typeof window&&"undefined"!=typeof document&&window.document===document,o=void 0!==e&&e.Math===Math?e:"undefined"!=typeof self&&self.Math===Math?self:"undefined"!=typeof window&&window.Math===Math?window:Function("return this")(),i="function"==typeof requestAnimationFrame?requestAnimationFrame.bind(o):function(e){return setTimeout((function(){return e(Date.now())}),1e3/60)},c=2;function a(e,t){var n=!1,r=!1,o=0;function c(){n&&(n=!1,e()),r&&s()}function a(){i(c)}function s(){var e=Date.now();if(n){if(e-o<2)return;r=!0}else n=!0,r=!1,setTimeout(a,t);o=e}return s}var s=20,u=["top","right","bottom","left","width","height","size","weight"],l="undefined"!=typeof MutationObserver,f=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=a(this.refresh.bind(this),20)}return e.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},e.prototype.removeObserver=function(e){var t=this.observers_,n=t.indexOf(e);~n&&t.splice(n,1),!t.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){var e;this.updateObservers_()&&this.refresh()},e.prototype.updateObservers_=function(){var e=this.observers_.filter((function(e){return e.gatherActive(),e.hasActive()}));return e.forEach((function(e){return e.broadcastActive()})),e.length>0},e.prototype.connect_=function(){r&&!this.connected_&&(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),l?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){r&&this.connected_&&(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(e){var t=e.propertyName,n=void 0===t?"":t,r;u.some((function(e){return!!~n.indexOf(e)}))&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),p=function(e,t){for(var n=0,r=Object.keys(t);n<r.length;n++){var o=r[n];Object.defineProperty(e,o,{value:t[o],enumerable:!1,writable:!1,configurable:!0})}return e},d=function(e){var t;return e&&e.ownerDocument&&e.ownerDocument.defaultView||o},h=E(0,0,0,0);function y(e){return parseFloat(e)||0}function b(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return t.reduce((function(t,n){var r;return t+y(e["border-"+n+"-width"])}),0)}function v(e){for(var t,n={},r=0,o=["top","right","bottom","left"];r<o.length;r++){var i=o[r],c=e["padding-"+i];n[i]=y(c)}return n}function m(e){var t=e.getBBox();return E(0,0,t.width,t.height)}function g(e){var t=e.clientWidth,n=e.clientHeight;if(!t&&!n)return h;var r=d(e).getComputedStyle(e),o=v(r),i=o.left+o.right,c=o.top+o.bottom,a=y(r.width),s=y(r.height);if("border-box"===r.boxSizing&&(Math.round(a+i)!==t&&(a-=b(r,"left","right")+i),Math.round(s+c)!==n&&(s-=b(r,"top","bottom")+c)),!_(e)){var u=Math.round(a+i)-t,l=Math.round(s+c)-n;1!==Math.abs(u)&&(a-=u),1!==Math.abs(l)&&(s-=l)}return E(o.left,o.top,a,s)}var O="undefined"!=typeof SVGGraphicsElement?function(e){return e instanceof d(e).SVGGraphicsElement}:function(e){return e instanceof d(e).SVGElement&&"function"==typeof e.getBBox};function _(e){return e===d(e).document.documentElement}function w(e){return r?O(e)?m(e):g(e):h}function x(e){var t=e.x,n=e.y,r=e.width,o=e.height,i="undefined"!=typeof DOMRectReadOnly?DOMRectReadOnly:Object,c=Object.create(i.prototype);return p(c,{x:t,y:n,width:r,height:o,top:n,right:t+r,bottom:o+n,left:t}),c}function E(e,t,n,r){return{x:e,y:t,width:n,height:r}}var j=function(){function e(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=E(0,0,0,0),this.target=e}return e.prototype.isActive=function(){var e=w(this.target);return this.contentRect_=e,e.width!==this.broadcastWidth||e.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},e}(),P=function(){function e(e,t){var n=x(t);p(this,{target:e,contentRect:n})}return e}(),S=function(){function e(e,t,r){if(this.activeObservations_=[],this.observations_=new n,"function"!=typeof e)throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=t,this.callbackCtx_=r}return e.prototype.observe=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof d(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)||(t.set(e,new j(e)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if("undefined"!=typeof Element&&Element instanceof Object){if(!(e instanceof d(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(e)&&(t.delete(e),t.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach((function(t){t.isActive()&&e.activeObservations_.push(t)}))},e.prototype.broadcastActive=function(){if(this.hasActive()){var e=this.callbackCtx_,t=this.activeObservations_.map((function(e){return new P(e.target,e.broadcastRect())}));this.callback_.call(e,t,e),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),k="undefined"!=typeof WeakMap?new WeakMap:new n,R=function(){function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var n=f.getInstance(),r=new S(t,n,this);k.set(this,r)}return e}();["observe","unobserve","disconnect"].forEach((function(e){R.prototype[e]=function(){var t;return(t=k.get(this))[e].apply(t,arguments)}}));var C=void 0!==o.ResizeObserver?o.ResizeObserver:R;t.a=C}).call(this,n(8))},function(e,t,n){n(5),e.exports=n(9)},function(e,t,n){},function(e,t,n){"use strict";var r=n(7);function o(){}function i(){}i.resetWarningCache=o,e.exports=function(){function e(e,t,n,o,i,c){if(c!==r){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var n={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:o};return n.PropTypes=n,n}},function(e,t,n){"use strict";var r="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";e.exports=r},function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){"use strict";n.r(t),n.d(t,"Block",(function(){return Ee})),n.d(t,"Page",(function(){return qe})),n.d(t,"P",(function(){return pe})),n.d(t,"Text",(function(){return Y}));var r=n(0),o=n.n(r),i=n(1),c=n.n(i),a=n(2),s=n.n(a);function u(e){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function e(t){return typeof t}:function e(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}var l=function e(){var t="production",n=!1;try{n=!0}catch(e){}return n},f={"body-1":"body2","body-2":"body1",subhead:"h6",title:"h5",headline:"h4","display-1":"h3","display-2":"h2","display-3":"h1"},p=function e(t){var n;return{top:"flex-start",bottom:"flex-end",middle:"center"}[t]||t};function d(e){return/-/.test(e)?e.toLowerCase().replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()})):e||""}function h(e,t,n){var r=-1===n,o=e.length,i,c=r?o-1:0;if(isArrayLike(e))for(;c<o&&c>=0&&!1!==(i=t.call(e[c],e[c],c));r?c--:c++);else for(c in e)if(e.hasOwnProperty(c)&&!1===(i=t.call(e[c],e[c],c)))break;return e}var y={cssFloat:1,styleFloat:1,float:1},b="undefined"!=typeof window&&!!window.document&&!!document.createElement,v=/margin|padding|width|height|max|min|offset|size|top/i,m=1,g=1,O=1,_=1;function w(e,t,n){if(!b||!e)return!1;"object"===u(t)&&2===arguments.length?h(t,(function(t,n){return w(e,n,t)})):(t=y[t]?"cssFloat"in e.style?"cssFloat":"styleFloat":t,"number"==typeof n&&v.test(t)&&(n="".concat(n,"px")),e.style[d(t)]=n)}var x=l,E=f,j=p,P=w;function S(e){return(S="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function e(t){return typeof t}:function e(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function k(){return(k=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function R(e,t){if(null==e)return{};var n=C(e,t),r,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)r=i[o],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function C(e,t){if(null==e)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],t.indexOf(o)>=0||(n[o]=e[o]);return n}function N(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function T(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function M(e,t,n){return t&&T(e.prototype,t),n&&T(e,n),e}function A(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&B(e,t)}function B(e,t){return(B=Object.setPrototypeOf||function e(t,n){return t.__proto__=n,t})(e,t)}function D(e){var t=I();return function n(){var r=L(e),o;if(t){var i=L(this).constructor;o=Reflect.construct(r,arguments,i)}else o=r.apply(this,arguments);return F(this,o)}}function F(e,t){return!t||"object"!==S(t)&&"function"!=typeof t?H(e):t}function H(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function I(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function L(e){return(L=Object.setPrototypeOf?Object.getPrototypeOf:function e(t){return t.__proto__||Object.getPrototypeOf(t)})(e)}var W=function(e){var t=function(t){A(r,t);var n=D(r);function r(){var t;return N(this,r),t=n.call(this),l()||console.error("Warning: Text.".concat(e.toUpperCase(),' is depracted, use <P type="').concat(e,'"> instead')),t}return M(r,[{key:"render",value:function t(){var n=this.props,r=n.prefix,i=n.className,c=R(n,["prefix","className"]);return o.a.createElement(Y,k({},c,{component:e,className:"".concat(i||""," ").concat(r,"typography-title")}))}}]),r}(r.Component);return t.propTypes={prefix:s.a.string},t.defaultProps={prefix:"next-"},t.displayName=e.toUpperCase(),t};function z(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var q=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},G=function e(t,n){var o,i=t.prefix,a=t.className,s=t.type,u=t.component,l=void 0===u?"span":u,p=t.strong,d=t.underline,h=t.delete,y=t.code,b=t.mark,v=q(t,["prefix","className","type","component","strong","underline","delete","code","mark"]),m=t.children,g=f[s]||s,O=c()(a,(z(o={},"".concat(i,"text"),!0),z(o,"".concat(i,"text-").concat(g),g),o));if("string"==typeof m&&-1!==m.indexOf("\n")){var _=m.split("\n"),w=[];_.forEach((function(e){w.push(e),w.push(r.createElement("br",null))})),w.pop(),m=w}var x=l;return p&&(m=r.createElement("strong",null,m)),d&&(m=r.createElement("u",null,m)),h&&(m=r.createElement("del",null,m)),y&&(m=r.createElement("code",null,m)),b&&(m=r.createElement("mark",null,m)),r.createElement(x,Object.assign({},v,{className:O,ref:n}),m)},U=r.forwardRef(G);U.defaultProps={prefix:"next-"};var V=function e(t,n){var o=t.prefix,i=t.className,a=t.component,s=q(t,["prefix","className","component"]),u=c()("".concat(o,"text-paragraph"),i);return l()||console.error("Warning: Text.Paragraph is depracted, use P instead"),r.createElement(G,Object.assign({},s,{className:u,component:a}))};U.H1=W("h1"),U.H2=W("h2"),U.H3=W("h3"),U.H4=W("h4"),U.H5=W("h5"),U.H6=W("h6"),U.Paragraph=V;var Y=U;function J(e){return(J="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function e(t){return typeof t}:function e(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function K(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Q(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function X(e,t,n){return t&&Q(e.prototype,t),n&&Q(e,n),e}function Z(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&$(e,t)}function $(e,t){return($=Object.setPrototypeOf||function e(t,n){return t.__proto__=n,t})(e,t)}function ee(e){var t=re();return function n(){var r=oe(e),o;if(t){var i=oe(this).constructor;o=Reflect.construct(r,arguments,i)}else o=r.apply(this,arguments);return te(this,o)}}function te(e,t){return!t||"object"!==J(t)&&"function"!=typeof t?ne(e):t}function ne(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function re(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function oe(e){return(oe=Object.setPrototypeOf?Object.getPrototypeOf:function e(t){return t.__proto__||Object.getPrototypeOf(t)})(e)}function ie(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var ce=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},ae=function e(t,n){var o,i=t.prefix,a=void 0===i?"next-":i,s=t.children,u=t.width,l=t.direction,d=void 0===l?"hoz":l,h=t.align,y=void 0===h?"inherit":h,b=t.alignSelf,v=void 0===b?"inherit":b,m=t.verAlign,g=void 0===m?"inherit":m,O=t.className,_=t.type,w=void 0===_?"body2":_,x=ce(t,["prefix","children","width","direction","align","alignSelf","verAlign","className","type"]),E=f[w]||w,j=r.Children.map(s,(function(e){return"string"==typeof e?e.trim()?r.createElement(Y,{},e):null:e})),P=c()(O,(ie(o={},"".concat(a,"p-cell"),!0),ie(o,"".concat(a,"p-cell-").concat(E),E),ie(o,"".concat(a,"p-cell-").concat(d),d),ie(o,"".concat(a,"p-cell-align-").concat(y),y),ie(o,"".concat(a,"p-cell-veralign-").concat(g),g),ie(o,"".concat(a,"p-block"),j&&j.length<2),o)),S=Object.assign({width:u,alignSelf:p(v),justifyContent:"center"},t.style);return r.createElement("div",Object.assign({},x,{className:P,style:S,ref:n}),j)},se=function e(t,n){var o,i=t.prefix,a=void 0===i?"next-":i,s=t.children,u=t.className,l=t.wrap,p=t.type,d=void 0===p?"body2":p,h=t.textSpacing,y=t.full,b=t.flex,v=ce(t,["prefix","children","className","wrap","type","textSpacing","full","flex"]),m=f[d]||d,g=v.verAlign,O=void 0===g?"baseline":g,_=v.align,w=ce(v,["verAlign","align"]),x=!1,E=[],j=-1,P="",S=0|r.Children.count(s),k=function e(t){P&&E.push(r.createElement(Y,{},t)),j=-1,P=""};r.Children.map(s,(function(e,t){"string"==typeof e?(j+1===t?(j+=1,P+=e):(j=t,P=e),t===S-1&&k(P)):(k(P),e&&e.type&&"p_cell"===e.type._typeMark?(x=!0,E.push(r.cloneElement(e,{type:e.props.type||d}))):E.push(e))}));var R=c()(u,(ie(o={},"".concat(a,"p"),!0),ie(o,"".concat(a,"p-").concat(m),m),ie(o,"".concat(a,"p-align-").concat(_),_),ie(o,"".concat(a,"p-veralign-").concat(O),O),ie(o,"".concat(a,"p-no-text-space "),!h),ie(o,"".concat(a,"p-flex"),x||b),ie(o,"".concat(a,"p-align-full"),y),ie(o,"".concat(a,"p-block"),E&&E.length<2),o)),C=Object.assign({flexWrap:l?"wrap":void 0},t.style);return r.createElement("div",Object.assign({},w,{className:R,style:C,ref:n}),E)},ue,le;r.forwardRef?(ue=r.forwardRef(se)).displayName="Paragraph":ue=function(e){Z(n,e);var t=ee(n);function n(){var e;return K(this,n),(e=t.apply(this,arguments)).state={},e}return X(n,[{key:"render",value:function e(){return r.createElement(se,Object.assign({},this.props))}}]),n}(r.Component);ue.Cell=r.forwardRef(ae),ue.Cell._typeMark="p_cell";var fe,pe=ue;function de(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var he=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},ye=function e(t,n){var o,i=t.prefix,a=void 0===i?"next-":i,s=t.direction,u=he(t,["prefix","direction"]),l=c()((de(o={},"".concat(a,"block-divider"),!0),de(o,"".concat(a,"block-divider-ver"),"ver"===s),o));return r.createElement("div",Object.assign({},u,{className:l,ref:n}))},be=r.forwardRef(ye),ve=function e(t,n){var o=t.prefix,i=void 0===o?"next-":o,a=t.className,s=t.children,u=t.content,l=t.rowSpan,f=t.style,p=t.colSpan,d=t.title,h=t.extra,y=he(t,["prefix","className","children","content","rowSpan","style","colSpan","title","extra"]),b=c()(a,de({},"".concat(i,"block-cell"),!0)),v=Object.assign({gridRowEnd:"span ".concat(l),gridColumnEnd:"span ".concat(p)},f);return r.createElement("div",Object.assign({className:b},y,{style:v,ref:n}),d?r.createElement(r.Fragment,null,r.createElement(pe,{className:"".concat(i,"block-title"),type:"h6",align:"space-between"},r.createElement(pe.Cell,null,d),h&&r.createElement(pe.Cell,{type:"body2"},h))):null,r.createElement("div",{className:"".concat(i,"block-cell-body")},u||s))},me=r.forwardRef(ve);me._typeMark="block.cell",me.defaultProps={prefix:"next-",colSpan:1,rowSpan:1};var ge=me;function Oe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var _e=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},we=function e(t,n){var o,i=t.prefix,a=t.dataSource,s=t.title,u=t.strict,l=t.className,f=t.mode,p=t.background,d=t.noPadding,h=t.noBorder,y=t.children,b=t.style,v=t.rowSpan,m=t.colSpan,g=t.childTotalColumns,O=t.extra,_=_e(t,["prefix","dataSource","title","strict","className","mode","background","noPadding","noBorder","children","style","rowSpan","colSpan","childTotalColumns","extra"]),w=c()(l,(Oe(o={},"".concat(i,"block"),!0),Oe(o,"".concat(i,"block-").concat(f),f),Oe(o,"".concat(i,"block-").concat(p),!!p),Oe(o,"".concat(i,"block-no-padding"),d),Oe(o,"".concat(i,"block-no-border"),h),o)),x=Object.assign({gridRowEnd:"span ".concat(v),gridColumnEnd:"span ".concat(m)},b),E={gridTemplateColumns:"number"==typeof g?"repeat(".concat(g,", minmax(0px, ").concat((100/g).toFixed(5),"%)"):g},j=!1;return r.Children.map(y,(function(e){e&&e.type&&"block.cell"===e.type._typeMark||(j=!(a&&a.length||!y))})),r.createElement("div",Object.assign({className:w,style:x},_,{ref:n}),s?r.createElement(r.Fragment,null,r.createElement("div",{className:"".concat(i,"block-header")},r.createElement(pe,{className:"".concat(i,"block-title"),type:"h5",align:"space-between"},r.createElement(pe.Cell,null,s),O&&r.createElement(pe.Cell,{type:"body2"},O))),"inset"===f&&r.createElement(be,null)):null,r.createElement("div",{className:"".concat(i,"block-body"),style:E},a&&a.map((function(e,t){return r.createElement(ge,Object.assign({key:t},e))})),!u&&j?r.createElement(ge,{children:y}):y))},xe=r.forwardRef(we);xe.Cell=ge,xe.Divider=be,xe._typeMark="fusion.block",xe.defaultProps={prefix:"next-",childTotalColumns:1,colSpan:12,rowSpan:1,noPadding:!1,noBorder:!1,mode:"inset"};var Ee=xe,je=n(3);function Pe(e){return(Pe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function e(t){return typeof t}:function e(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function Se(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ke(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Re(e,t,n){return t&&ke(e.prototype,t),n&&ke(e,n),e}function Ce(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Ne(e,t)}function Ne(e,t){return(Ne=Object.setPrototypeOf||function e(t,n){return t.__proto__=n,t})(e,t)}function Te(e){var t=Be();return function n(){var r=De(e),o;if(t){var i=De(this).constructor;o=Reflect.construct(r,arguments,i)}else o=r.apply(this,arguments);return Me(this,o)}}function Me(e,t){return!t||"object"!==Pe(t)&&"function"!=typeof t?Ae(e):t}function Ae(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Be(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function De(e){return(De=Object.setPrototypeOf?Object.getPrototypeOf:function e(t){return t.__proto__||Object.getPrototypeOf(t)})(e)}function Fe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var He=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n},Ie=function e(t,n){var o,i,a,s,u,l=t.prefix,f=t.className,p=t.header,d=t.headerProps,h=void 0===d?{}:d,y=t.headerDivider,b=t.contentProps,v=void 0===b?{}:b,m=t.gap,g=t.style,O=t.footer,_=t.footerProps,x=void 0===_?{}:_,E=t.nav,j=t.aside,P=t.main,S=t.children,k=t.contentAlignCenter,R=t.minHeight,C=t.background,N=t.columns,T=void 0===N?1:N,M=He(t,["prefix","className","header","headerProps","headerDivider","contentProps","gap","style","footer","footerProps","nav","aside","main","children","contentAlignCenter","minHeight","background","columns"]),A=h.background,B=void 0===A?"surface":A,D=h.style,F=void 0===D?{}:D,H=v.background,I=void 0===H?"transparent":H,L=v.noPadding,W=void 0!==L&&L,z=x.fixed,q=void 0!==z&&z,G=x.style,U=void 0===G?{}:G,V=c()(f,(Fe(o={},"".concat(l,"page"),!0),Fe(o,"".concat(l,"page-centermode"),k),Fe(o,"".concat(l,"page-bg-").concat(C),!!C),o)),Y=c()((Fe(i={},"".concat(l,"page-header"),!0),Fe(i,"".concat(l,"page-header-divider"),y),Fe(i,"".concat(l,"page-bg-").concat(B),!!B),i)),J=c()((Fe(a={},"".concat(l,"page-footer"),!0),Fe(a,"".concat(l,"page-fixed"),q),a)),K="".concat(l,"page-nav"),Q="".concat(l,"page-aside"),X="".concat(l,"page-main"),Z=c()((Fe(s={},"".concat(l,"page-content"),!0),Fe(s,"".concat(l,"page-content-center"),k),Fe(s,"".concat(l,"page-content-nopadding"),W),s)),$=c()((Fe(u={},"".concat(l,"page-bg-").concat(I),!!I),Fe(u,"".concat(l,"page-min-height-helper"),!0),u)),ee={gridTemplateColumns:"repeat(".concat(T,", minmax(0px, 1fr))"),gridGap:m},te=Object.assign(Object.assign({},g),{minHeight:R}),ne=function e(){for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];var i=r.useRef();return r.useEffect((function(){n.forEach((function(e){e&&("function"==typeof e?e(i.current):e.current=i.current)}))}),[n]),i},re=Object(r.useRef)(null),oe=Object(r.useRef)(null),ie=ne(n,oe);r.useLayoutEffect((function(){var e=ie.current.getBoundingClientRect();console.log("Input dimensions:",e.width,e.height)}),[n]);var ce=new je.a((function(){if(O){var e=oe.current,t=re.current,n=e&&e.getBoundingClientRect().width||0,r=t&&t.getBoundingClientRect().height||0;w(t,"width",n),w(e,"paddingBottom",q?r:0)}}));Object(r.useEffect)((function(){return ce.observe(oe.current),function(){ce.unobserve(oe.current)}}),[q,O]);var ae=r.createElement("section",{className:Z},E&&r.createElement("aside",{className:K},E),r.createElement("section",{className:X,style:ee},P||S),j&&r.createElement("aside",{className:Q},j));return r.createElement("div",Object.assign({ref:ie,className:V,style:te},M),p&&r.createElement("header",{className:Y,style:F},r.createElement("div",{className:"".concat(l,"page-header-content")},p)),r.createElement("div",{className:$},ae),O&&r.createElement("footer",{ref:re,className:J,style:U},r.createElement("div",{className:"".concat(l,"page-footer-content")},O)))},Le,We;r.forwardRef?(Le=r.forwardRef(Ie)).displayName="Page":Le=function(e){Ce(n,e);var t=Te(n);function n(){var e;return Se(this,n),(e=t.apply(this,arguments)).state={},e}return Re(n,[{key:"render",value:function e(){return r.createElement(Ie,Object.assign({},this.props))}}]),n}(r.Component);var ze=Le;ze.defaultProps={prefix:"next-"};var qe=ze}])}));