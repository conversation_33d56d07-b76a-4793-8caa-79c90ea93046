!function e(t, n) { "object" == typeof exports && "object" == typeof module ? module.exports = n() : "function" == typeof define && define.amd ? define([], n) : "object" == typeof exports ? exports.IwhaleLowcodeComponentsMeta = n() : t.IwhaleLowcodeComponentsMeta = n() }(window, (function () { return function (e) { var t = {}; function n(o) { if (t[o]) return t[o].exports; var r = t[o] = { i: o, l: !1, exports: {} }; return e[o].call(r.exports, r, r.exports, n), r.l = !0, r.exports } return n.m = e, n.c = t, n.d = function (e, t, o) { n.o(e, t) || Object.defineProperty(e, t, { enumerable: !0, get: o }) }, n.r = function (e) { "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(e, "__esModule", { value: !0 }) }, n.t = function (e, t) { if (1 & t && (e = n(e)), 8 & t) return e; if (4 & t && "object" == typeof e && e && e.__esModule) return e; var o = Object.create(null); if (n.r(o), Object.defineProperty(o, "default", { enumerable: !0, value: e }), 2 & t && "string" != typeof e) for (var r in e) n.d(o, r, function (t) { return e[t] }.bind(null, r)); return o }, n.n = function (e) { var t = e && e.__esModule ? function t() { return e.default } : function t() { return e }; return n.d(t, "a", t), t }, n.o = function (e, t) { return Object.prototype.hasOwnProperty.call(e, t) }, n.p = "", n(n.s = 1) }([function (e, t) { e.exports = window.React }, function (e, t, n) { e.exports = n(2) }, function (e, t, n) { "use strict"; n.r(t), n.d(t, "components", (function () { return gn })), n.d(t, "componentList", (function () { return yn })); var o = { "-": "disabled", "*": "active", "~": "hover", "": "normal" }, r, l; !function (e) { e.divider = "divider", e.node = "node", e.comment = "comment" }(r || (r = {})), function (e) { e.text = "text", e.icon = "icon" }(l || (l = {})); var a = function e() { var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : ""; return t ? t.replace(/(\[.*?\])/g, "\n$1\n").split("\n").filter((function (e) { return !!e })).map((function (e) { switch (!0) { case /^\[(.*)\]$/.test(e): return { type: "icon", value: RegExp.$1 }; default: return { type: "text", value: e } } })) : [] }, i = function e(t) { var n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : { parseContent: !1 }; if (!t) return []; var l = { type: r.node, state: "normal", value: "", children: [] }, i = [l]; return t.split("\n").filter((function (e) { return e.trim() })).forEach((function (e) { var t = /^(\t*)([#\-~*]?)(.*)$/.exec(e), l = (t[1] || "").length, p = t[2] || "", c = { type: r.node, state: "normal", value: t[3] || "", children: [] }; for ("-" === p && /^-{2,}$/.test(c.value) ? c.type = r.divider : "#" === p ? c.type = r.comment : c.state = o[p], "node" === c.type && n.parseContent && (c.value = a(c.value)); l <= i.length - 2;)i.pop(); i[i.length - 1].children.push(c), i.push(c) })), l.children }, p = i; function c(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function s(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? c(Object(n), !0).forEach((function (t) { m(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : c(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function m(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var d = function e(t) { var n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : { selected: [], expanded: {} }, o = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 0, r = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : "", l = [], a = [], i = !1, p = 0; return t.forEach((function (t) { switch (t.type) { case "node": var c = "".concat(r || o, "-").concat(p++); return t.children && t.children.length > 0 && (t.children = e(t.children, n, o + 1, c)), i ? a.push(s(s({}, t), {}, { key: c })) : l.push(s(s({}, t), {}, { key: c })), void ("active" === t.state && (t.children && t.children.length > 0 ? n.expanded.push(c) : n.selected.push(c))); case "comment": return a.length > 0 && (l.push({ type: "group", value: i, children: a, key: "".concat(r || o, "-").concat(p++) }), a = []), void (i = t.value); case "divider": a.length > 0 && (l.push({ type: "group", value: i, children: a, key: "".concat(r || o, "-").concat(p++) }), a = []), i = !1, l.push({ type: "divider", key: "".concat(r || o, "-").concat(p++) }) } })), a.length > 0 && (l.push({ type: "group", value: i, children: a, key: "".concat(r || o, "-").concat(p++) }), a = []), l }, u = function e(t) { return t.children.length > 0 ? { componentName: "SubMenu", props: { key: t.key, disabled: "disabled" === t.state, label: t.value ? t.value.filter((function (e) { var t; return "text" === e.type })).map((function (e) { var t; return e.value })).join("") : "" }, children: A(t.children) } : { componentName: "Menu.Item", props: { key: t.key, checked: "active" === t.state, disabled: "disabled" === t.state }, children: t.value.map((function (e, t) { var n = e.type, o = e.value; return "icon" === n ? { componentName: "Icon", props: { disabled: !0, key: "icon_".concat(t), type: o, size: "small", style: { marginRight: "4px" } } } : { componentName: "Typography.Text", props: { children: o, style: { color: "inherit" } } } })) } }, A = function e() { var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : []; return t.map((function (e) { return "group" === e.type && e.children.length > 0 ? { componentName: "Menu.Group", props: { key: e.key, label: e.value }, children: e.children.map((function (e) { return u(e) })) } : "divider" === e.type ? { componentName: "Menu.Divider", props: { key: e.key } } : u(e) })) }; function g(e) { return "node" !== e.type ? {} : { label: { type: "JSSlot", value: e.value.map((function (e) { var t = e.type, n = e.value; return "icon" === t ? { componentName: "Icon", props: { disabled: !0, type: n, size: "small", style: { marginRight: "4px" } } } : { componentName: "Typography.Text", props: { children: n, style: { color: "inherit" } } } })) }, disabled: "disabled" === (null == e ? void 0 : e.state) } } function h(e) { var t = { selected: [], expanded: [] }, n = i(e, { parseContent: !0 }), o, r = g(n[0] ? n[0] : { value: [] }), l = r.label, a = r.disabled, p = d(n[0] ? n[0].children : [], t), c; return { label: l, disabled: a, children: A(p), selectedKeys: t.selected } } var y = "Edit Document\n\tUndo\n\tRedo\n\tCut\n\tCopy\n\tPaste", b = h(y), f = b.label, w = b.selectedKeys, v = { prefix: "next-", type: "normal", size: "medium", label: f, defaultSelectedKeys: [], autoWidth: !0, popupTriggerType: "click", plainData: y }; w && w.length && (v.selectedKeys = w); var O = v, x, N, P = { componentName: "Button", group: "Fed", title: "Button", icon: "https://img.alicdn.com/tfs/TB1rT0gPQL0gK0jSZFAXXcA9pXa-200-200.svg", docUrl: "", screenshot: "", npm: { package: "iwhale-lowcode-components", version: "{{version}}", exportName: "Button", main: "", destructuring: !0, subName: "" }, props: [{ name: "name", title: { label: { type: "i18n", zh_CN: "Name", en_US: "Name" } }, display: "block", setter: { componentName: "OutLineSetter", props: { placeholder: "Please enter a name for this component" } } }, { name: "children", title: { label: { type: "i18n", zh_CN: "Button Label", en_US: "Button Label" } }, display: "block", propType: "string" }, { name: "linkType", title: { label: { type: "i18n", zh_CN: "Link to", en_US: "Link to" } }, display: "block", setter: { componentName: "SelectNewSetter", props: { options: [{ label: "Web address", value: "Web address" }, { label: "In-app page", value: "In-app page" }], placeholder: "Please select a link to", mode: "single" } } }, { name: "linkAddress", title: { label: { type: "i18n", zh_CN: "Link Address", en_US: "Link Address" } }, display: "block", setter: { componentName: "StringSetter" }, condition: function e(t) { return "Web address" === t.getProps().getPropValue("linkType") || "In-app page" === t.getProps().getPropValue("linkType") } }, { name: "textStyle", title: { label: { type: "i18n", zh_CN: "Text", en_US: "Text" } }, display: "block", setter: { componentName: "TextNewSetter" } }, { title: { label: { type: "i18n", "en-US": "Border", "zh-CN": "Border" } }, extraProps: { supportVariable: !1 }, name: "border", display: "block", setter: { componentName: "BorderLineTypeSetter", props: { direction: "border" } } }, { title: { label: { type: "i18n", "en-US": "Radius", "zh-CN": "Radius" } }, extraProps: { supportVariable: !1 }, name: "radius", display: "block", setter: { componentName: "AutoRadiusSetter" } }, { title: { label: { type: "i18n", "en-US": "Padding", "zh-CN": "Padding" } }, name: "paddingValue", display: "block", setter: { componentName: "PaddingNewSetter", props: { initValue: { top: 12, right: 12, bottom: 12, left: 12 }, limit: { top: { min: -1 / 0, max: 1 / 0 }, right: { min: 12, max: 40 }, bottom: { min: -1 / 0, max: 1 / 0 }, left: { min: 12, max: 40 } }, byStyle: !1 } } }, { title: { label: { type: "i18n", "en-US": "Full Width", "zh-CN": "Full Width" } }, name: "contentFullWidth", display: "block", setter: { componentName: "BooleanSetter" } }, { title: { label: { type: "i18n", "en-US": "Align", "zh-CN": "Align" } }, name: "text-align", display: "block", setter: { componentName: "TextAlignNewSetter" } }, { title: { label: { type: "i18n", "en-US": "Display Options", "zh-CN": "Display Options" } }, extraProps: { supportVariable: !1 }, name: "displayOptions", display: "block", setter: { componentName: "DisplayOptions", props: { defaultValue: "1" } } }], category: "Blocks", configure: { supports: { style: !1 }, component: { nestingRule: { parentWhitelist: ["ThreeCol", "Col", "TwoCol", "Link"] } } }, snippets: [{ title: "Button", screenshot: "https://alifd.oss-cn-hangzhou.aliyuncs.com/fusion-cool/icons/icon-light/ic_light_button.png", schema: { componentName: "Button", props: { prefix: "next-", type: "primary", size: "medium", htmlType: "button", component: "button", children: "cancel" } } }] }; function E(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function S(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? E(Object(n), !0).forEach((function (t) { C(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : E(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function C(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var q, j = [{ title: "Col", screenshot: "", schema: { componentName: "Col", props: {} } }], B = S(S({}, { componentName: "Col", group: "Fed", title: "Col", docUrl: "", screenshot: "", devMode: "procode", npm: { package: "iwhale-lowcode-components", version: "0.1.0", exportName: "Col", main: "", destructuring: !0, subName: "" }, configure: { props: [], supports: { style: !1 }, component: { isContainer: !0, nestingRule: { parentWhitelist: ["TwoCol", "ThreeCol", "Layout", "Col"], childWhitelist: ["Text", "Image", "Avatar", "NextText", "Paragraph", "H", "Link", "updownlayout", "Slider", "Button", "P"] } }, advanced: { callbacks: { onResizeStart: function e(t, n) { var o = n.parent; if (o) { var r = o.getDOMNode(); r && (n.parentRect = r.getBoundingClientRect()) } n.beforeSpan = n.getPropValue("colSpan") || 12, n.startRect = n.getRect() }, onResize: function e(t, n) { var o = t.deltaX, r, l = (n.startRect ? n.startRect.width : n.beforeSpan * (n.parentRect.width / 12)) + o; n.startRect || (n.startRect = { width: l }), l = Math.max(0, l), l = Math.min(l, n.parentRect.width); var a = Math.max(6, Math.ceil(l / n.parentRect.width * 24)); n.setPropValue("colSpan", a) }, onResizeEnd: function e(t, n) { n.setPropValue("style.width", n.getDOMNode().style.width) } }, getResizingHandlers: function e() { return ["e"] } } }, category: "Layout" }), {}, { snippets: j }); function k(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function D(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? k(Object(n), !0).forEach((function (t) { U(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : k(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function U(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var V, T = [{ title: "Button", screenshot: "https://alifd.oss-cn-hangzhou.aliyuncs.com/fusion-cool/icons/icon-light/ic_light_button.png", schema: { componentName: "EmailButton", props: {} } }], M = D(D({}, { group: "Fed", componentName: "EmailButton", title: "EmailButton", docUrl: "", screenshot: "", devMode: "procode", npm: { package: "iwhale-lowcode-components", version: "0.2.12", exportName: "EmailButton", main: "src/index.tsx", destructuring: !0, subName: "" }, configure: { props: [{ name: "name", title: { label: { type: "i18n", zh_CN: "Name", en_US: "Name" } }, display: "block", setter: { componentName: "StringSetter", props: { placeholder: "Please enter a name for this component" } } }, { name: "text", title: { label: { type: "i18n", zh_CN: "Button Label", en_US: "Button Label" } }, display: "block", setter: { componentName: "StringSetter", initialValue: "Promotion", props: { placeholder: "Please enter a name for this component" } } }, { name: "linkType", title: { label: { type: "i18n", zh_CN: "Link to", en_US: "Link to" } }, display: "block", setter: { componentName: "SelectSetter", props: { options: [{ label: "Web address", value: "Web address" }, { label: "In-app page", value: "In-app page" }], placeholder: "Please select a link to", mode: "single" } } }, { name: "href", title: { label: { type: "i18n", zh_CN: "Link Address", en_US: "Link Address" } }, display: "block", setter: { componentName: "StringSetter" }, condition: function e(t) { return "Web address" === t.getProps().getPropValue("linkType") || "In-app page" === t.getProps().getPropValue("linkType") } }, { name: "textStyle", title: { label: { type: "i18n", zh_CN: "Text", en_US: "Text" } }, display: "block", setter: { componentName: "TextNewSetter" } }, { title: { label: { type: "i18n", "en-US": "Border", "zh-CN": "Border" } }, extraProps: { supportVariable: !1 }, name: "border", display: "block", setter: { componentName: "BorderLineTypeSetter", props: { direction: "border" } } }, { title: { label: { type: "i18n", "en-US": "Radius", "zh-CN": "Radius" } }, extraProps: { supportVariable: !1 }, name: "radius", display: "block", setter: { componentName: "AutoRadiusSetter" } }, { title: { label: { type: "i18n", "en-US": "Padding", "zh-CN": "Padding" } }, name: "paddingValue", display: "block", setter: { componentName: "PaddingNewSetter", props: { initValue: { top: 12, right: 12, bottom: 12, left: 12 }, limit: { top: { min: -1 / 0, max: 1 / 0 }, right: { min: 12, max: 40 }, bottom: { min: -1 / 0, max: 1 / 0 }, left: { min: 12, max: 40 } }, byStyle: !1 } } }, { title: { label: { type: "i18n", "en-US": "Full Width", "zh-CN": "Full Width" } }, name: "contentFullWidth", display: "block", setter: { componentName: "BooleanSetter" } }, { title: { label: { type: "i18n", "en-US": "Align", "zh-CN": "Align" } }, name: "textAlign", display: "block", setter: { componentName: "TextAlignNewSetter" } }, { title: { label: { type: "i18n", "en-US": "Display Options", "zh-CN": "Display Options" } }, extraProps: { supportVariable: !1 }, name: "displayOptions", display: "block", setter: { componentName: "DisplayOptions", props: { defaultValue: "1" } } }], supports: { style: !1 }, component: { nestingRule: { parentWhitelist: ["Page", "EmailCol"] } } }, category: "Blocks" }), {}, { snippets: T }); function R(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function W(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? R(Object(n), !0).forEach((function (t) { I(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : R(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function I(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var z, Q = [{ title: "Col", screenshot: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADsAAAAYCAYAAABEHYUrAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAO6ADAAQAAAABAAAAGAAAAACfDBzTAAAAmElEQVRYCe2YsRGAMAwD5RwrsAnLMAWzUEDDSjBJdoiJOUqzgJDv0qh7fSrZth0zrKwAxv5Yr8LbMjygjh3wi5XUrUzWhZYO2I3ygoZA83YGZ8D+5gTLqlpmZZagAX1jAokpgsymtRCEMksgMUWQ2bQWglBmCSSmCDKb1kIQ/s5sjUGKQNwnwstXh5gYY3mDGe2UavBnSr0B0UYcgwtOTtsAAAAASUVORK5CYII=", schema: { componentName: "EmailCol", props: {} } }], Z = W(W({}, { group: "Fed", componentName: "EmailCol", title: "EmailCol", docUrl: "", screenshot: "", devMode: "procode", npm: { package: "iwhale-lowcode-components", version: "0.2.12", exportName: "EmailCol", main: "src/index.tsx", destructuring: !0, subName: "" }, configure: { props: [{ title: { label: { type: "i18n", "en-US": "colSpan", "zh-CN": "colSpan" } }, name: "colSpan", setter: { componentName: "NumberSetter", isRequired: !0, initialValue: 12 } }], supports: { style: !1 }, component: { isContainer: !0, nestingRule: { parentWhitelist: ["EmailRow"] } } }, category: "Layout" }), {}, { snippets: Q }); function Y(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function F(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? Y(Object(n), !0).forEach((function (t) { G(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Y(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function G(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var L, K = [{ title: "Image", screenshot: "data:image/png;base64,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", schema: { componentName: "EmailImage", props: {} } }], X = F(F({}, { group: "Fed", componentName: "EmailImage", title: "EmailImage", docUrl: "", screenshot: "", devMode: "procode", npm: { package: "iwhale-lowcode-components", version: "{{version}}", exportName: "EmailImage", main: "src/index.tsx", destructuring: !0, subName: "" }, configure: { props: [{ title: { label: { type: "i18n", "en-US": "Name", "zh-CN": "Name" } }, name: "name", display: "block", setter: { componentName: "StringSetter", isRequired: !1, initialValue: "", props: { placeholder: "Please enter a name for this component" } } }, { title: { label: { type: "i18n", "en-US": "Image Area", "zh-CN": "Image Area" } }, extraProps: { supportVariable: !1 }, name: "srcAndWidth", display: "block", setter: { componentName: "CarouselUpload", props: { multiple: !1 } } }, { title: { label: { type: "i18n", "en-US": "Dynamic Content", "zh-CN": "Dynamic Content" } }, extraProps: { supportVariable: !1 }, name: "dynamicContent", display: "block", setter: { componentName: "DynamicContentSetter" }, condition: function e(t) { var n; return (null === (n = t.getProps().getPropValue("srcAndWidth")) || void 0 === n ? void 0 : n.length) > 0 } }, { title: { label: { type: "i18n", "en-US": "Align", "zh-CN": "Align" } }, extraProps: { supportVariable: !1 }, name: "align", display: "block", setter: { componentName: "TextAlignNewSetter", props: { initValue: { top: 12, right: 12, bottom: 12, left: 12 } } } }, { title: { label: { type: "i18n", "en-US": "Style", "zh-CN": "Style" } }, extraProps: { supportVariable: !1 }, name: "border", display: "block", setter: { componentName: "BorderLineTypeSetter", props: { editType: "radius" } } }, { title: { label: { type: "i18n", "en-US": "Padding", "zh-CN": "Padding" } }, name: "paddingValue", display: "block", setter: { componentName: "PaddingNewSetter", props: { initValue: { top: 12, right: 12, bottom: 12, left: 12 }, limit: { top: { min: -1 / 0, max: 1 / 0 }, right: { min: 12, max: 40 }, bottom: { min: -1 / 0, max: 1 / 0 }, left: { min: 12, max: 40 } }, byStyle: !1 } } }, { title: { label: { type: "i18n", "en-US": "Display Options", "zh-CN": "Display Options" } }, extraProps: { supportVariable: !1 }, name: "displayOptions", display: "block", setter: { componentName: "DisplayOptions", props: { defaultValue: "1" } } }], supports: { style: !1 }, component: { nestingRule: { parentWhitelist: ["Page", "EmailCol", "Col"] } } }, category: "Blocks", priority: 3 }), {}, { snippets: K }); function H(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function J(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? H(Object(n), !0).forEach((function (t) { _(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : H(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function _(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var $, ee = [{ title: "Row", screenshot: "", schema: { componentName: "EmailRow", props: {} } }], te = J(J({}, { group: "Fed", componentName: "EmailRow", title: "EmailRow", docUrl: "", screenshot: "", devMode: "procode", npm: { package: "iwhale-lowcode-components", version: "0.2.12", exportName: "EmailRow", main: "src/index.tsx", destructuring: !0, subName: "" }, configure: { props: [{ title: { label: { type: "i18n", "en-US": "Body Color", "zh-CN": "Body Color" } }, name: "background", extraProps: { display: "block" }, setter: { componentName: "ColorSetter", initialValue: "transparent" } }, { title: { label: { type: "i18n", "en-US": "Margin Space", "zh-CN": "Margin Space" } }, name: "marginSpace", extraProps: { display: "block" }, setter: { componentName: "MarginSpaceSetter" } }], supports: { style: !1 }, component: { isContainer: !0, nestingRule: { childWhitelist: ["EmailCol"] } }, advanced: { initialChildren: [{ componentName: "EmailCol", props: { colSpan: "12" } }] } }, category: "Layout" }), {}, { snippets: ee }); function ne(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function oe(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? ne(Object(n), !0).forEach((function (t) { re(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : ne(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function re(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var le, ae = [{ title: "Spacer", screenshot: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEEAAAAYCAYAAACldpB6AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAQaADAAQAAAABAAAAGAAAAADJlA35AAAB4klEQVRYCe2YvUoDQRCAd+4sBEuRy3UatEiRX0mvEZtELX0AQaJlHiAW2ogKtorvYGdABPMAURKxM4WWMb2VJOPM4YVNDDZmZwvv4Ljb2WFm59u92bkFZeBKZldvFGLRgGmlAGrPzXppkradSRoTsYUKRfxETv4hAdd2zKlsYd/z5489Pz7z3nl9sDEeqxAogR5SAj2hwONKYcnzF9xu560uDcIahG8AVQC4pqAT9LwlIDs2QFiBMACg4Eq5cE7Blx3HrVDe/6AVUZEGIb5F6gCemvdlfelzGxgMYjXQ0zsNvotCyOeLMQ6QAw0CBhja8+mTQB1EoG8weGumM5n1JUSEcACp5UIumVnBdG5tI5RxP+uFbdPPKdMORu23WndtmvFR8VCbVwQJ2kNCgw3Rz8FgHH8yHUEgfOIQRnPCuCmUzgmiEDjb99TnSzpbuNSTow6C5dzPelK7gyiERqPWofOAI/ob3h0HIgTA/awX6OuEDL3/nqYNOdULJqoYL7DXf6SKcRP7/a0QAB2cHBhy/8OslbKZf5K4NOYSmWZhjkaVoHuWAGzzCpAEwESsQGDHAxCIe9yma9EGAHZsDQI7ZxAxP96l2mkawDmjFXDK8uiyQMBIYqTEF502G51Myp6Ttv8FZaXDULTcDGoAAAAASUVORK5CYII=", schema: { componentName: "EmailSpacer", props: {} } }], ie = oe(oe({}, { group: "Fed", componentName: "EmailSpacer", title: "EmailSpacer", docUrl: "", screenshot: "", devMode: "proCode", npm: { package: "iwhale-lowcode-components", version: "0.2.0", exportName: "EmailSpacer", main: "src/index.tsx", destructuring: !0, subName: "" }, configure: { props: [{ name: "name", title: { label: { type: "i18n", zh_CN: "Name", en_US: "Name" } }, display: "block", setter: { componentName: "OutLineSetter", props: { placeholder: "Please enter a name for this component" } } }, { title: { label: { type: "i18n", "en-US": "Style", "zh-CN": "Style" } }, extraProps: { supportVariable: !1 }, name: "Style", display: "block", setter: { componentName: "BorderLineTypeSetter" } }, { title: { label: { type: "i18n", "en-US": "Auto Width", "zh-CN": "Auto Width" } }, extraProps: { supportVariable: !1 }, name: "Auto Width", display: "block", setter: { componentName: "AutoWidthSetter" } }, { title: { label: { type: "i18n", "en-US": "Align", "zh-CN": "Align" } }, extraProps: { supportVariable: !1 }, name: "align", display: "block", setter: { componentName: "TextAlignNewSetter" } }, { title: { label: { type: "i18n", "en-US": "Padding", "zh-CN": "Padding" } }, extraProps: { supportVariable: !1 }, name: "Padding", display: "block", setter: { componentName: "PaddingNewSetter", props: { initValue: { top: 12, right: 12, bottom: 12, left: 12 }, limit: { top: { min: -1 / 0, max: 1 / 0 }, right: { min: 12, max: 40 }, bottom: { min: -1 / 0, max: 1 / 0 }, left: { min: 12, max: 40 } } } } }, { title: { label: { type: "i18n", "en-US": "Display Options", "zh-CN": "Display Options" } }, extraProps: { supportVariable: !1 }, name: "displayOptions", display: "block", setter: { componentName: "DisplayOptions", props: { defaultValue: "1" } } }], supports: { style: !1 }, component: { nestingRule: { parentWhitelist: ["Page"] } } }, category: "Blocks" }), {}, { snippets: ae }); function pe(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function ce(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? pe(Object(n), !0).forEach((function (t) { se(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : pe(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function se(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var me, de = [{ title: "Text", screenshot: "data:image/png;base64,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", schema: { componentName: "EmailText", props: {} } }], ue = ce(ce({}, { group: "Fed", componentName: "EmailText", title: "EmailText", docUrl: "", screenshot: "", devMode: "procode", npm: { package: "iwhale-lowcode-components", version: "{{version}}", exportName: "EmailText", main: "", destructuring: !0, subName: "" }, configure: { props: [{ name: "name", title: { label: { type: "i18n", zh_CN: "Name", en_US: "Name" } }, display: "block", setter: { componentName: "OutLineSetter", props: { placeholder: "Please enter a name for this component" } } }, { title: { label: { type: "i18n", "en-US": "Style", "zh-CN": "Style" } }, extraProps: { supportVariable: !1 }, name: "border", display: "block", setter: { componentName: "BorderLineTypeSetter", props: { editType: "radius" } } }, { title: { label: { type: "i18n", "en-US": "Padding", "zh-CN": "Padding" } }, extraProps: { supportVariable: !1 }, name: "padding", display: "block", setter: { componentName: "PaddingNewSetter", props: { initValue: { top: 12, right: 12, bottom: 12, left: 12 }, limit: { top: { min: -1 / 0, max: 1 / 0 }, right: { min: 12, max: 40 }, bottom: { min: -1 / 0, max: 1 / 0 }, left: { min: 12, max: 40 } } } } }, { title: { label: { type: "i18n", "en-US": "EmailText", "zh-CN": "EmailText" } }, name: "text", extraProps: { display: "block", onChange: function e(t, n) { var o; n.nodes[0].setPropValue("text", t) } }, setter: { componentName: "RichTextSetter", isRequired: !0, initialValue: "\u8fd9\u662f\u5185\u5bb9" } }, { title: { label: { type: "i18n", "en-US": "Display Options", "zh-CN": "Display Options" } }, extraProps: { supportVariable: !1 }, name: "displayOptions", display: "block", setter: { componentName: "DisplayOptions", props: { defaultValue: "1" } } }], supports: { style: !1 }, component: { nestingRule: { parentWhitelist: ["ThreeCol", "Col", "TwoCol", "EmailCol"] } } }, category: "Blocks", priority: 2 }), {}, { snippets: de }), Ae = n(0), ge = n.n(Ae); function he(e, t) { var n, o = t.nodes[0]; ye(o); for (var r = (null == o || null === (n = o.children) || void 0 === n ? void 0 : n.size) || 0, l, a = 0; a < r; a++) { var i, p, c; if (0 === (null === (p = l = null == o || null === (i = o.children) || void 0 === i ? void 0 : i.get(a)) || void 0 === p || null === (c = p.children) || void 0 === c ? void 0 : c.size)) { var s; null === (s = l) || void 0 === s || s.remove(), l = null; break } } queueMicrotask((function () { var t, n = null == o || null === (t = o.children) || void 0 === t ? void 0 : t.length; if ("1:2" === e) { for (var r, l = 2; l < n; l++) { var a, i, p; null == o || null === (a = o.children) || void 0 === a || null === (i = a.get(1)) || void 0 === i || i.insert(null == o || null === (p = o.children) || void 0 === p ? void 0 : p.get(2)) } null == o || null === (r = o.children) || void 0 === r || r.forEach((function (e, t) { var n; (e.setPropValue("colSpan", 0 === t ? 8 : 16), t > 0) && (null == e || null === (n = e.children) || void 0 === n || n.forEach((function (e) { e.setPropValue("colSpan", 24) }))) })) } else if ("2:1" === e) { for (var c, s = 2; s < n; s++) { var m, d, u; null == o || null === (m = o.children) || void 0 === m || null === (d = m.get(0)) || void 0 === d || d.insert(null == o || null === (u = o.children) || void 0 === u ? void 0 : u.get(1)) } null == o || null === (c = o.children) || void 0 === c || c.forEach((function (e, t) { var n; (e.setPropValue("colSpan", 0 === t ? 16 : 8), 0 === t) && (null == e || null === (n = e.children) || void 0 === n || n.forEach((function (e) { e.setPropValue("colSpan", 24) }))) })) } else { var A; null == o || null === (A = o.children) || void 0 === A || A.forEach((function (t) { t.setPropValue("colSpan", 24 / e) })) } })) } function ye(e) { var t, n, o, r, l = null == e || null === (t = e.children) || void 0 === t ? void 0 : t.get(0), a = null == e || null === (n = e.children) || void 0 === n ? void 0 : n.get(1); (null == l || null === (o = l.children) || void 0 === o ? void 0 : o.size) > 0 && (null == l || l.children.forEach((function (t, n) { n > 0 && queueMicrotask((function () { var n; e.insertBefore(t, null === (n = e.children) || void 0 === n ? void 0 : n.get(0)) })) }))), (null == a || null === (r = a.children) || void 0 === r ? void 0 : r.size) > 0 && (null == a || a.children.forEach((function (t, n) { n > 0 && queueMicrotask((function () { null == e || e.children.insert(t) })) }))) } function be(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function fe(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? be(Object(n), !0).forEach((function (t) { we(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : be(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function we(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var ve, Oe = [{ title: "4", screenshot: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADsAAAAYCAYAAABEHYUrAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAO6ADAAQAAAABAAAAGAAAAACfDBzTAAABx0lEQVRYCe1YsUoDQRCd2Yua0sZKG8FWsPAXxMZeBIsIiUgS8A/EwspOSFIYQQtBUtiIjeQXFARbwcZAII1l5MyOM5vcpXDikZTLLoS9eTu7M29eqof1enMP0FwAwBL/slYPyB5XKqU7SWw2b1fin+9HIljn0GRc7iNQq1wuFpK8RuPqhgB3Oc4n2ITdIsLbXG5hp1Ta/5ScWfrOOaIEDQB6mVAohQnNJg4H48jGcf/MAnYNmFOiwSBNVD54IItgzHmtdr1VrR60ZSegbbL2kIl8KVdSCDGKLNkjqcdgwR1IH1P2LWqwotlEpQCSfR7mSyQLlw3gaxZRl8mEWNl3Y2hVYtklziIqufK+1JF6Eo/W1H1n/fWSh73YA1kvZFRIBGWVoXgBBWW9kFEhEZRVhuIFFJT1QkaFRFBWGYoXUFDWCxkVEkFZZSheQEFZL2RUSIiyPTHSlLM/0CivNz6gjgXaEENsjOlfYrixk7hmLX5IhuwSOyNOv5KiznDjOuxGdVJwhr5zYo06xxAx00plg8xZqUnBKJo/ARs/ENl74EcSXNvZWGMr1bYq1WJbzsVhZCv1CQxecvivlUo8GuOs1HwxfXuGvn8BAhKqhxytHSQAAAAASUVORK5CYII=", schema: { componentName: "FourCol", props: {} } }], xe = fe(fe({}, { group: "Fed", componentName: "FourCol", title: "FourCol", docUrl: "", screenshot: "", devMode: "procode", npm: { package: "iwhale-lowcode-components", version: "{{version}}", exportName: "Layout", main: "", destructuring: !0, subName: "" }, configure: { props: [{ title: { label: { type: "i18n", "en-US": "col", "zh-CN": "col" } }, name: "column Layout", extraProps: { onChange: function e(t, n) { return he(t, n) }, display: "block" }, setter: { componentName: "IwhaleRadioGroupSetter", isRequired: !1, initialValue: "4", props: { options: [{ value: "1", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "2px" } }, ge.a.createElement("svg", { t: "1651747586003", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19150", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.246322 35.57962m52.985286 0l1430.602728 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-1430.602728 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19151" }))), 1]) }, { value: "2", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747900057", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19300", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.246322 35.57962m52.985286 0l609.330791 0q52.985286 0 52.985287 52.985286l0 503.360219q0 52.985286-52.985287 52.985286l-609.330791 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19301" }), ge.a.createElement("path", { d: "M834.518258 35.57962m52.985286 0l609.330792 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-609.330792 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19302" }))), 2]) }, { value: "3", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747930736", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19451", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.246322 35.57962m52.985286 0l344.40436 0q52.985286 0 52.985287 52.985286l0 503.360219q0 52.985286-52.985287 52.985286l-344.40436 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19452" }), ge.a.createElement("path", { d: "M556.345505 35.57962m52.985287 0l344.40436 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-344.40436 0q-52.985286 0-52.985287-52.985286l0-503.360219q0-52.985286 52.985287-52.985286Z", "p-id": "19453" }), ge.a.createElement("path", { d: "M1099.444689 35.57962m52.985287 0l344.40436 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-344.40436 0q-52.985286 0-52.985287-52.985286l0-503.360219q0-52.985286 52.985287-52.985286Z", "p-id": "19454" }))), 3]) }, { value: "4", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747957237", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19603", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.238892 36.592299m52.95557 0l211.82228 0q52.95557 0 52.955569 52.95557l0 503.077913q0 52.95557-52.955569 52.95557l-211.82228 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19604" }), ge.a.createElement("path", { d: "M1231.216998 36.592299m52.95557 0l211.822279 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-211.822279 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19605" }), ge.a.createElement("path", { d: "M825.233122 36.592299m52.95557 0l211.822279 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-211.822279 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19606" }), ge.a.createElement("path", { d: "M419.222768 36.592299m52.95557 0l211.822279 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-211.822279 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19607" }))), 4]) }, { value: "1:2", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747990437", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19756", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.238892 36.592299m52.95557 0l370.688989 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-370.688989 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19757" }), ge.a.createElement("path", { d: "M569.272375 36.592299m52.95557 0l873.766902 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-873.766902 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19758" }))), "1:2"]) }, { value: "2:1", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651748018484", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19907", width: "31", height: "13" }, ge.a.createElement("path", { d: "M1072.350289 36.592299m52.955569 0l370.688989 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-370.688989 0q-52.95557 0-52.955569-52.95557l0-503.077913q0-52.95557 52.955569-52.95557Z", "p-id": "19908" }), ge.a.createElement("path", { d: "M13.238892 36.592299m52.95557 0l873.766902 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-873.766902 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19909" }))), "2:1"]) }] } } }, { title: { label: { type: "i18n", "en-US": "Margin Space", "zh-CN": "Margin Space" } }, name: "marginSpace", extraProps: { display: "block" }, setter: { componentName: "MarginSpaceSetter", initialValue: "transparent" } }, { title: { label: { type: "i18n", "en-US": "Background Type", "zh-CN": "Background Type" } }, name: "backgroundType", extraProps: { display: "block" }, setter: { componentName: "TypeSelectType", initialValue: "content", props: { options: [{ value: "content", title: "Content Width" }, { value: "full", title: "Full Width" }] } } }, { title: { label: { type: "i18n", "en-US": "Body Color", "zh-CN": "Body Color" } }, name: "background", extraProps: { display: "block" }, condition: function e(t) { return "full" === t.getProps().getPropValue("backgroundType") }, setter: { componentName: "BackgroundColorSetter", initialValue: "transparent" } }], supports: { style: !1 }, component: { isContainer: !0, nestingRule: { childWhitelist: function e(t, n) { return !(n.getChildren().size < 4) || "Col" === t.componentName } } }, advanced: { initialChildren: [{ componentName: "Col", props: { colSpan: "6" } }, { componentName: "Col", props: { colSpan: "6" } }, { componentName: "Col", props: { colSpan: "6" } }, { componentName: "Col", props: { colSpan: "6" } }], callbacks: { onNodeAdd: function e(t, n) { for (var o = n.children.size, r = 0; r < o; r++)n.children.get(r).getPropValue("DoNotHandleNodeAdd") || n.children.get(r).setPropValue("colSpan", Math.floor(24 / o)) }, onNodeRemove: function e(t, n) { for (var o = n.children.size, r = 0; r < o; r++)n.children.get(r).setPropValue("colSpan", Math.floor(24 / o)) } } } }, category: "Layout" }), {}, { snippets: Oe }); function Ne(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function Pe(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? Ne(Object(n), !0).forEach((function (t) { Ee(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Ne(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function Ee(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var Se, Ce = [{ title: "h", screenshot: "https://img.alicdn.com/imgextra/i4/O1CN01E2PcPW1bKJV5QUVMg_!!6000000003446-55-tps-50-50.svg", schema: { componentName: "H", props: {} } }], qe = Pe(Pe({}, { group: "Fed", componentName: "H", title: "H", docUrl: "", screenshot: "", devMode: "procode", npm: { package: "iwhale-lowcode-components", version: "{{version}}", exportName: "h", main: "", destructuring: !0, subName: "" }, configure: { props: [{ title: { label: { type: "i18n", "en-US": "title", "zh-CN": "title" } }, name: "title", setter: { componentName: "StringSetter", isRequired: !1, initialValue: "\u8fd9\u662f\u6807\u9898" } }, { title: { label: { type: "i18n", "en-US": "level", "zh-CN": "level" } }, name: "level", setter: { componentName: "RadioGroupSetter", isRequired: !0, initialValue: 1, props: { options: [{ value: 1, title: "h1" }, { value: 2, title: "h2" }, { value: 3, title: "h3" }, { value: 4, title: "h4" }, { value: 5, title: "h5" }, { value: 6, title: "h6" }] } } }], supports: { style: !1 }, component: { nestingRule: { parentWhitelist: ["Col", "EmailCol"] } } }, category: "Blocks" }), {}, { snippets: Ce }); function je(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function Be(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? je(Object(n), !0).forEach((function (t) { ke(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : je(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function ke(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var De, Ue = [{ title: "Image", screenshot: "data:image/png;base64,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", schema: { componentName: "Image", props: {} } }], Ve = Be(Be({}, { group: "Fed", componentName: "Image", title: "Image", docUrl: "", screenshot: "", devMode: "procode", npm: { package: "iwhale-lowcode-components", version: "{{version}}", exportName: "Image", main: "", destructuring: !0, subName: "" }, configure: { props: [{ title: { label: { type: "i18n", "en-US": "Name", "zh-CN": "Name" } }, name: "Name", display: "block", setter: { componentName: "OutLineSetter", isRequired: !1, initialValue: "", props: { placeholder: "Please enter a name for this component" } } }, { title: { label: { type: "i18n", "en-US": "Image Area", "zh-CN": "Image Area" } }, extraProps: { supportVariable: !1 }, name: "srcAndWidth", display: "block", setter: { componentName: "CarouselUpload", props: { multiple: !1 } } }, { title: { label: { type: "i18n", "en-US": "Dynamic Content", "zh-CN": "Dynamic Content" } }, extraProps: { supportVariable: !1 }, name: "dynamicContent", display: "block", setter: { componentName: "DynamicContentSetter" }, condition: function e(t) { var n; return (null === (n = t.getProps().getPropValue("srcAndWidth")) || void 0 === n ? void 0 : n.length) > 0 } }, { title: { label: { type: "i18n", "en-US": "Align", "zh-CN": "Align" } }, extraProps: { supportVariable: !1 }, name: "align", display: "block", setter: { componentName: "TextAlignNewSetter", props: { initValue: { top: 12, right: 12, bottom: 12, left: 12 } } } }, { title: { label: { type: "i18n", "en-US": "Style", "zh-CN": "Style" } }, extraProps: { supportVariable: !1 }, name: "border", display: "block", setter: { componentName: "BorderLineTypeSetter", props: { editType: "radius" } } }, { title: { label: { type: "i18n", "en-US": "Padding", "zh-CN": "Padding" } }, name: "paddingValue", display: "block", setter: { componentName: "PaddingNewSetter", props: { initValue: { top: 12, right: 12, bottom: 12, left: 12 }, limit: { top: { min: -1 / 0, max: 1 / 0 }, right: { min: 12, max: 40 }, bottom: { min: -1 / 0, max: 1 / 0 }, left: { min: 12, max: 40 } }, byStyle: !1 } } }, { title: { label: { type: "i18n", "en-US": "Display Options", "zh-CN": "Display Options" } }, extraProps: { supportVariable: !1 }, name: "displayOptions", display: "block", setter: { componentName: "DisplayOptions", props: { defaultValue: "1" } } }], supports: { style: !1 }, component: { nestingRule: { parentWhitelist: ["Col", "Page"] } } }, category: "Blocks", priority: 3 }), {}, { snippets: Ue }); function Te(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function Me(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? Te(Object(n), !0).forEach((function (t) { Re(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Te(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function Re(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var We, Ie = [{ title: "Layout", screenshot: "", schema: { componentName: "Layout", props: {} } }], ze = Me(Me({}, { group: "Fed", componentName: "Layout", title: "Layout", docUrl: "", screenshot: "", devMode: "procode", npm: { package: "iwhale-lowcode-components", version: "{{version}}", exportName: "Layout", main: "", destructuring: !0, subName: "" }, configure: { props: [{ title: { label: { type: "i18n", "en-US": "col", "zh-CN": "col" } }, name: "col", setter: { componentName: "NumberSetter", isRequired: !0, initialValue: 0 } }], supports: { style: !1 }, component: {} }, category: "Layout" }), {}, { snippets: Ie }); function Qe(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function Ze(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? Qe(Object(n), !0).forEach((function (t) { Ye(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Qe(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function Ye(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var Fe, Ge = [{ title: "link", screenshot: "data:image/png;base64,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", schema: { componentName: "Link", props: {} } }], Le = Ze(Ze({}, { group: "Fed", componentName: "Link", title: "Link", docUrl: "", screenshot: "", devMode: "procode", npm: { package: "iwhale-lowcode-components", version: "{{version}}", exportName: "link", main: "", destructuring: !0, subName: "" }, configure: { props: [{ title: { label: { type: "i18n", "en-US": "title", "zh-CN": "title" } }, name: "title", setter: { componentName: "StringSetter", isRequired: !1, initialValue: "" } }, { title: { label: { type: "i18n", "en-US": "href", "zh-CN": "href" } }, name: "href", setter: { componentName: "StringSetter", isRequired: !1, initialValue: "#" } }], supports: { style: !1 }, component: { isContainer: !0, nestingRule: { childWhitelist: ["Button"], parentWhitelist: ["Page", "Col"] } } }, category: "Blocks" }), {}, { snippets: Ge }); function Ke(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function Xe(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? Ke(Object(n), !0).forEach((function (t) { He(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Ke(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function He(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var Je, _e = [{ title: "1", screenshot: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADsAAAAYCAYAAABEHYUrAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAO6ADAAQAAAABAAAAGAAAAACfDBzTAAAAmElEQVRYCe2YsRGAMAwD5RwrsAnLMAWzUEDDSjBJdoiJOUqzgJDv0qh7fSrZth0zrKwAxv5Yr8LbMjygjh3wi5XUrUzWhZYO2I3ygoZA83YGZ8D+5gTLqlpmZZagAX1jAokpgsymtRCEMksgMUWQ2bQWglBmCSSmCDKb1kIQ/s5sjUGKQNwnwstXh5gYY3mDGe2UavBnSr0B0UYcgwtOTtsAAAAASUVORK5CYII=", schema: { componentName: "oneCol", props: {} } }], $e = Xe(Xe({}, { group: "Fed", componentName: "oneCol", title: "oneCol", docUrl: "", screenshot: "", devMode: "procode", npm: { package: "iwhale-lowcode-components", version: "{{version}}", exportName: "Layout", main: "", destructuring: !0, subName: "" }, configure: { props: [{ title: { label: { type: "i18n", "en-US": "Column Layout", "zh-CN": "Column Layout" } }, name: "Column Layout", extraProps: { onChange: function e(t, n) { return he(t, n) }, display: "block" }, setter: { componentName: "IwhaleRadioGroupSetter", isRequired: !1, initialValue: "1", props: { options: [{ value: "1", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "2px" } }, ge.a.createElement("svg", { t: "1651747586003", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19150", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.246322 35.57962m52.985286 0l1430.602728 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-1430.602728 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19151" }))), 1]) }, { value: "2", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747900057", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19300", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.246322 35.57962m52.985286 0l609.330791 0q52.985286 0 52.985287 52.985286l0 503.360219q0 52.985286-52.985287 52.985286l-609.330791 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19301" }), ge.a.createElement("path", { d: "M834.518258 35.57962m52.985286 0l609.330792 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-609.330792 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19302" }))), 2]) }, { value: "3", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747930736", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19451", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.246322 35.57962m52.985286 0l344.40436 0q52.985286 0 52.985287 52.985286l0 503.360219q0 52.985286-52.985287 52.985286l-344.40436 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19452" }), ge.a.createElement("path", { d: "M556.345505 35.57962m52.985287 0l344.40436 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-344.40436 0q-52.985286 0-52.985287-52.985286l0-503.360219q0-52.985286 52.985287-52.985286Z", "p-id": "19453" }), ge.a.createElement("path", { d: "M1099.444689 35.57962m52.985287 0l344.40436 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-344.40436 0q-52.985286 0-52.985287-52.985286l0-503.360219q0-52.985286 52.985287-52.985286Z", "p-id": "19454" }))), 3]) }, { value: "4", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747957237", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19603", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.238892 36.592299m52.95557 0l211.82228 0q52.95557 0 52.955569 52.95557l0 503.077913q0 52.95557-52.955569 52.95557l-211.82228 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19604" }), ge.a.createElement("path", { d: "M1231.216998 36.592299m52.95557 0l211.822279 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-211.822279 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19605" }), ge.a.createElement("path", { d: "M825.233122 36.592299m52.95557 0l211.822279 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-211.822279 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19606" }), ge.a.createElement("path", { d: "M419.222768 36.592299m52.95557 0l211.822279 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-211.822279 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19607" }))), 4]) }, { value: "1:2", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747990437", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19756", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.238892 36.592299m52.95557 0l370.688989 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-370.688989 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19757" }), ge.a.createElement("path", { d: "M569.272375 36.592299m52.95557 0l873.766902 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-873.766902 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19758" }))), "1:2"]) }, { value: "2:1", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651748018484", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19907", width: "31", height: "13" }, ge.a.createElement("path", { d: "M1072.350289 36.592299m52.955569 0l370.688989 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-370.688989 0q-52.95557 0-52.955569-52.95557l0-503.077913q0-52.95557 52.955569-52.95557Z", "p-id": "19908" }), ge.a.createElement("path", { d: "M13.238892 36.592299m52.95557 0l873.766902 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-873.766902 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19909" }))), "2:1"]) }] } } }, { title: { label: { type: "i18n", "en-US": "Margin Space", "zh-CN": "Margin Space" } }, name: "marginSpace", extraProps: { display: "block" }, setter: { componentName: "MarginSpaceSetter", initialValue: "transparent" } }, { title: { label: { type: "i18n", "en-US": "Background Type", "zh-CN": "Background Type" } }, name: "backgroundType", extraProps: { display: "block" }, setter: { componentName: "TypeSelectType", initialValue: "content", props: { options: [{ value: "content", title: "Content Width" }, { value: "full", title: "Full Width" }] } } }, { title: { label: { type: "i18n", "en-US": "Body Color", "zh-CN": "Body Color" } }, name: "background", extraProps: { display: "block" }, condition: function e(t) { return "full" === t.getProps().getPropValue("backgroundType") }, setter: { componentName: "BackgroundColorSetter", initialValue: "transparent" } }], supports: { style: !1 }, component: { isContainer: !0, nestingRule: { childWhitelist: function e(t, n) { return n.getChildren().size < 4 && "Col" === t.componentName } } }, advanced: { initialChildren: [{ componentName: "Col", props: { colSpan: "24" } }], callbacks: { onNodeAdd: function e(t, n) { for (var o = n.children.size, r = 0; r < o; r++)n.children.get(r).getPropValue("DoNotHandleNodeAdd") || n.children.get(r).setPropValue("colSpan", Math.floor(24 / o)) }, onNodeRemove: function e(t, n) { for (var o = n.children.size, r = 0; r < o; r++)n.children.get(r).setPropValue("colSpan", Math.floor(24 / o)) } } } }, category: "Layout" }), {}, { snippets: _e }); function et(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function tt(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? et(Object(n), !0).forEach((function (t) { nt(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : et(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function nt(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var ot, rt = [{ title: "1/3:2/3", screenshot: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADsAAAAYCAYAAABEHYUrAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAO6ADAAQAAAABAAAAGAAAAACfDBzTAAAA3UlEQVRYCe2YsRGDMAxFJR8rZJMskwFSUTBLCtJkgCxDJvEOCEvEHIUKn+1KyHccRmd96+tRCef58wAMLwC4pad2RaB1GsfnlwU6adbWouVJfYMYJXgD0E87VRIjDHfcGyZme2iW3Ft6JtcXUkIiWm+UL0Ral12Hv2Q1a2ahHu9cH5u9zHKzVlE7WSdroAP+GxuAqFpwsmpbDASdrAGIqgUnq7bFQNDJGoCoWnCyalsMBC9HNvJAqgXcPz+eNJo1T1rN21zfwCNQmQwiVo9SEUhGlUdVHTQPrQ6bXN8GwHs7W16DlrEAAAAASUVORK5CYII=", schema: { componentName: "OneTwoCol", props: {} } }], lt = tt(tt({}, { group: "Fed", componentName: "OneTwoCol", title: "OneTwoCol", docUrl: "", screenshot: "", devMode: "procode", npm: { package: "iwhale-lowcode-components", version: "{{version}}", exportName: "Layout", main: "", destructuring: !0, subName: "" }, configure: { props: [{ title: { label: { type: "i18n", "en-US": "Column Layout", "zh-CN": "Column Layout" } }, name: "Column Layout", extraProps: { onChange: function e(t, n) { return controlColWidth(t, n) }, display: "block" }, setter: { componentName: "IwhaleRadioGroupSetter", isRequired: !1, initialValue: "1:2", props: { options: [{ value: "1", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "2px" } }, ge.a.createElement("svg", { t: "1651747586003", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19150", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.246322 35.57962m52.985286 0l1430.602728 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-1430.602728 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19151" }))), 1]) }, { value: "2", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747900057", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19300", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.246322 35.57962m52.985286 0l609.330791 0q52.985286 0 52.985287 52.985286l0 503.360219q0 52.985286-52.985287 52.985286l-609.330791 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19301" }), ge.a.createElement("path", { d: "M834.518258 35.57962m52.985286 0l609.330792 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-609.330792 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19302" }))), 2]) }, { value: "3", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747930736", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19451", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.246322 35.57962m52.985286 0l344.40436 0q52.985286 0 52.985287 52.985286l0 503.360219q0 52.985286-52.985287 52.985286l-344.40436 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19452" }), ge.a.createElement("path", { d: "M556.345505 35.57962m52.985287 0l344.40436 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-344.40436 0q-52.985286 0-52.985287-52.985286l0-503.360219q0-52.985286 52.985287-52.985286Z", "p-id": "19453" }), ge.a.createElement("path", { d: "M1099.444689 35.57962m52.985287 0l344.40436 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-344.40436 0q-52.985286 0-52.985287-52.985286l0-503.360219q0-52.985286 52.985287-52.985286Z", "p-id": "19454" }))), 3]) }, { value: "4", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747957237", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19603", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.238892 36.592299m52.95557 0l211.82228 0q52.95557 0 52.955569 52.95557l0 503.077913q0 52.95557-52.955569 52.95557l-211.82228 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19604" }), ge.a.createElement("path", { d: "M1231.216998 36.592299m52.95557 0l211.822279 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-211.822279 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19605" }), ge.a.createElement("path", { d: "M825.233122 36.592299m52.95557 0l211.822279 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-211.822279 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19606" }), ge.a.createElement("path", { d: "M419.222768 36.592299m52.95557 0l211.822279 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-211.822279 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19607" }))), 4]) }, { value: "1:2", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747990437", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19756", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.238892 36.592299m52.95557 0l370.688989 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-370.688989 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19757" }), ge.a.createElement("path", { d: "M569.272375 36.592299m52.95557 0l873.766902 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-873.766902 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19758" }))), "1:2"]) }, { value: "2:1", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651748018484", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19907", width: "31", height: "13" }, ge.a.createElement("path", { d: "M1072.350289 36.592299m52.955569 0l370.688989 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-370.688989 0q-52.95557 0-52.955569-52.95557l0-503.077913q0-52.95557 52.955569-52.95557Z", "p-id": "19908" }), ge.a.createElement("path", { d: "M13.238892 36.592299m52.95557 0l873.766902 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-873.766902 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19909" }))), "2:1"]) }] } } }, { title: { label: { type: "i18n", "en-US": "Adjust Column Ratio", "zh-CN": "Adjust Column Ratio" } }, name: "columnRatio", condition: function e(t) { return 1 !== t.getProps().getPropValue("Column Layout") }, extraProps: { display: "block" }, setter: function e(t) { return { componentName: "AdjustColumnSetter", props: { colType: t.getProps().getPropValue("Column Layout") } } } }, { title: { label: { type: "i18n", "en-US": "Margin Space", "zh-CN": "Margin Space" } }, name: "marginSpace", extraProps: { display: "block" }, setter: { componentName: "MarginSpaceSetter", initialValue: "transparent" } }, { title: { label: { type: "i18n", "en-US": "Background Type", "zh-CN": "Background Type" } }, name: "backgroundType", extraProps: { display: "block" }, setter: { componentName: "TypeSelectType", initialValue: "content", props: { options: [{ value: "content", title: "Content Width" }, { value: "full", title: "Full Width" }] } } }, { title: { label: { type: "i18n", "en-US": "Body Color", "zh-CN": "Body Color" } }, name: "background", extraProps: { display: "block" }, condition: function e(t) { return "full" === t.getProps().getPropValue("backgroundType") }, setter: { componentName: "BackgroundColorSetter", initialValue: "transparent" } }], supports: { style: !1 }, component: { isContainer: !0, nestingRule: { childWhitelist: function e(t, n) { return n.getChildren().size < 4 && "Col" === t.componentName } } }, advanced: { initialChildren: [{ componentName: "Col", props: { colSpan: "8" } }, { componentName: "Col", props: { colSpan: "16" } }], callbacks: { onNodeAdd: function e(t, n) { for (var o = n.children.size, r = 0; r < o; r++)n.children.get(r).getPropValue("DoNotHandleNodeAdd") || n.children.get(r).setPropValue("colSpan", Math.floor(24 / o)) }, onNodeRemove: function e(t, n) { for (var o = n.children.size, r = 0; r < o; r++)n.children.get(r).setPropValue("colSpan", Math.floor(24 / o)) } } } }, category: "Layout" }), {}, { snippets: rt }); function at(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function it(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? at(Object(n), !0).forEach((function (t) { pt(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : at(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function pt(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var ct, st = [{ title: "p", screenshot: "", schema: { componentName: "P", props: {} } }], mt = it(it({}, { group: "Fed", componentName: "P", title: "P", docUrl: "", screenshot: "", devMode: "procode", npm: { package: "iwhale-lowcode-components", version: "{{version}}", exportName: "p", main: "", destructuring: !0, subName: "" }, configure: { props: [{ title: { label: { type: "i18n", "en-US": "content", "zh-CN": "content" } }, name: "content", setter: { componentName: "TextAreaSetter", isRequired: !1, initialValue: "\u8fd9\u662f\u5185\u5bb9" } }], supports: { style: !1 }, component: { nestingRule: { parentWhitelist: ["Col"] } } }, category: "Blocks" }), {}, { snippets: st }); function dt(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function ut(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? dt(Object(n), !0).forEach((function (t) { At(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : dt(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function At(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var gt, ht = [{ title: "row", screenshot: "", schema: { componentName: "Row", props: {} } }], yt = ut(ut({}, { group: "Fed", componentName: "Row", title: "Row", docUrl: "", screenshot: "", devMode: "procode", npm: { package: "iwhale-lowcode-components", version: "{{version}}", exportName: "Row", main: "", destructuring: !0, subName: "" }, category: "Layout", configure: { props: [{ title: { label: { type: "i18n", "en-US": "Background Type", "zh-CN": "Background Type" } }, name: "backgroundType", extraProps: { display: "block" }, setter: { componentName: "RadioGroupSetter", initialValue: "2", props: { options: [{ value: "1", title: "Content Width" }, { value: "2", title: "Full Width" }] } } }, { title: { label: { type: "i18n", "en-US": "Body Color", "zh-CN": "Body Color" } }, name: "background", extraProps: { display: "block" }, setter: { componentName: "ColorSetter", initialValue: "transparent" } }, { title: { label: { type: "i18n", "en-US": "Margin Space", "zh-CN": "Margin Space" } }, name: "marginSpace", extraProps: { display: "block" }, setter: { componentName: "MarginSpaceSetter" } }], supports: { style: !1 }, component: { isContainer: !0, nestingRule: { parentWhitelist: ["Page"] } }, advanced: { initialChildren: [{ componentName: "TwoCol", props: { col: "2" } }] } } }), {}, { snippets: ht }), bt, ft = { group: "Fed", componentName: "Slider", title: "Carousel", docUrl: "", screenshot: "", npm: { package: "iwhale-lowcode-components", version: "{{version}}", exportName: "Slider", main: "", destructuring: !0, subName: "" }, configure: { component: { isContainer: !0, nestingRule: { parentWhitelist: ["Col"] } }, props: [{ name: "name", title: { label: { type: "i18n", zh_CN: "Name", en_US: "Name" } }, display: "block", setter: { componentName: "OutLineSetter", props: { placeholder: "Please enter a name for this component" } } }, { title: { label: { type: "i18n", "en-US": "Image Area", "zh-CN": "Image Area" } }, extraProps: { supportVariable: !1 }, name: "children", display: "block", setter: { componentName: "CarouselUpload" } }, { title: { label: { type: "i18n", "en-US": "Align", "zh-CN": "Align" } }, extraProps: { supportVariable: !1 }, name: "align", display: "block", setter: { componentName: "TextAlignNewSetter", props: { initValue: { top: 12, right: 12, bottom: 12, left: 12 } } } }, { title: { label: { type: "i18n", "en-US": "Style", "zh-CN": "Style" } }, extraProps: { supportVariable: !1 }, name: "border", display: "block", setter: { componentName: "BorderLineTypeSetter", props: { editType: "radius" } } }, { title: { label: { type: "i18n", "en-US": "Padding", "zh-CN": "Padding" } }, name: "paddingValue", display: "block", setter: { componentName: "PaddingNewSetter", props: { initValue: { top: 12, right: 12, bottom: 12, left: 12 }, limit: { top: { min: -1 / 0, max: 1 / 0 }, right: { min: 12, max: 40 }, bottom: { min: -1 / 0, max: 1 / 0 }, left: { min: 12, max: 40 } }, byStyle: !1 } } }, { title: { label: { type: "i18n", "en-US": "Play Setting", "zh-CN": "Play Setting" } }, name: "sliderPlay", display: "accordion", setter: { componentName: "DelayTime" } }, { title: { label: { type: "i18n", "en-US": "Display Options", "zh-CN": "Display Options" } }, extraProps: { supportVariable: !1 }, name: "displayOptions", display: "block", setter: { componentName: "DisplayOptions", props: { defaultValue: "1" } } }], supports: { style: !1 } }, icon: "", category: "Blocks", snippets: [{ title: "Carousel", screenshot: "https://alifd.oss-cn-hangzhou.aliyuncs.com/fusion-cool/icons/icon-light/ic_light_slider.png", schema: { componentName: "Slider", props: { prefix: "next-", animation: "slide", arrows: !0, arrowSize: "medium", arrowPosition: "inner", arrowDirection: "hoz", autoplaySpeed: 3e3, dots: !0, dotsDirection: "hoz", draggable: !0, infinite: !0, slide: "div", slideDirection: "hoz", slidesToShow: 1, slidesToScroll: 1, speed: 600, triggerType: "click", centerPadding: "50px", cssEase: "ease", edgeFriction: .35, swipe: !0, touchMove: !0, touchThreshold: 5, useCSS: !0, waitForAnimate: !0 }, children: [] } }] }; function wt(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function vt(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? wt(Object(n), !0).forEach((function (t) { Ot(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : wt(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function Ot(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var xt, Nt = [{ title: "Spacer", screenshot: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEEAAAAYCAYAAACldpB6AAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAQaADAAQAAAABAAAAGAAAAADJlA35AAAB4klEQVRYCe2YvUoDQRCAd+4sBEuRy3UatEiRX0mvEZtELX0AQaJlHiAW2ogKtorvYGdABPMAURKxM4WWMb2VJOPM4YVNDDZmZwvv4Ljb2WFm59u92bkFZeBKZldvFGLRgGmlAGrPzXppkradSRoTsYUKRfxETv4hAdd2zKlsYd/z5489Pz7z3nl9sDEeqxAogR5SAj2hwONKYcnzF9xu560uDcIahG8AVQC4pqAT9LwlIDs2QFiBMACg4Eq5cE7Blx3HrVDe/6AVUZEGIb5F6gCemvdlfelzGxgMYjXQ0zsNvotCyOeLMQ6QAw0CBhja8+mTQB1EoG8weGumM5n1JUSEcACp5UIumVnBdG5tI5RxP+uFbdPPKdMORu23WndtmvFR8VCbVwQJ2kNCgw3Rz8FgHH8yHUEgfOIQRnPCuCmUzgmiEDjb99TnSzpbuNSTow6C5dzPelK7gyiERqPWofOAI/ob3h0HIgTA/awX6OuEDL3/nqYNOdULJqoYL7DXf6SKcRP7/a0QAB2cHBhy/8OslbKZf5K4NOYSmWZhjkaVoHuWAGzzCpAEwESsQGDHAxCIe9yma9EGAHZsDQI7ZxAxP96l2mkawDmjFXDK8uiyQMBIYqTEF502G51Myp6Ttv8FZaXDULTcDGoAAAAASUVORK5CYII=", schema: { componentName: "Spacer", props: {} } }], Pt = vt(vt({}, { group: "Fed", componentName: "Spacer", title: "Spacer", docUrl: "", screenshot: "", devMode: "proCode", npm: { package: "iwhale-lowcode-components", version: "0.2.0", exportName: "Spacer", main: "src/index.tsx", destructuring: !0, subName: "" }, configure: { props: [{ name: "name", title: { label: { type: "i18n", zh_CN: "Name", en_US: "Name" } }, display: "block", setter: { componentName: "OutLineSetter", props: { placeholder: "Please enter a name for this component" } } }, { title: { label: { type: "i18n", "en-US": "Style", "zh-CN": "Style" } }, extraProps: { supportVariable: !1 }, name: "Style", display: "block", setter: { componentName: "BorderLineTypeSetter" } }, { title: { label: { type: "i18n", "en-US": "Auto Width", "zh-CN": "Auto Width" } }, extraProps: { supportVariable: !1 }, name: "Auto Width", display: "block", setter: { componentName: "AutoWidthSetter" } }, { title: { label: { type: "i18n", "en-US": "Align", "zh-CN": "Align" } }, extraProps: { supportVariable: !1 }, name: "align", display: "block", setter: { componentName: "TextAlignNewSetter" } }, { title: { label: { type: "i18n", "en-US": "Padding", "zh-CN": "Padding" } }, extraProps: { supportVariable: !1 }, name: "Padding", display: "block", setter: { componentName: "PaddingNewSetter", props: { initValue: { top: 12, right: 12, bottom: 12, left: 12 }, limit: { top: { min: -1 / 0, max: 1 / 0 }, right: { min: 12, max: 40 }, bottom: { min: -1 / 0, max: 1 / 0 }, left: { min: 12, max: 40 } } } } }, { title: { label: { type: "i18n", "en-US": "Display Options", "zh-CN": "Display Options" } }, extraProps: { supportVariable: !1 }, name: "displayOptions", display: "block", setter: { componentName: "DisplayOptions", props: { defaultValue: "1" } } }], supports: { style: !1 }, component: { nestingRule: { parentWhitelist: ["Page"] } } }, category: "Blocks" }), {}, { snippets: Nt }); function Et(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function St(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? Et(Object(n), !0).forEach((function (t) { Ct(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Et(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function Ct(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var qt, jt = [{ title: "Text", screenshot: "data:image/png;base64,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", schema: { componentName: "Text", props: {} } }], Bt = St(St({}, { group: "Fed", componentName: "Text", title: "Text", docUrl: "", screenshot: "", devMode: "procode", npm: { package: "iwhale-lowcode-components", version: "{{version}}", exportName: "Text", main: "", destructuring: !0, subName: "" }, configure: { props: [{ name: "name", title: { label: { type: "i18n", zh_CN: "Name", en_US: "Name" } }, display: "block", setter: { componentName: "OutLineSetter", props: { placeholder: "Please enter a name for this component" } } }, { title: { label: { type: "i18n", "en-US": "Style", "zh-CN": "Style" } }, extraProps: { supportVariable: !1 }, name: "border", display: "block", setter: { componentName: "BorderLineTypeSetter", props: { editType: "radius" } } }, { title: { label: { type: "i18n", "en-US": "Padding", "zh-CN": "Padding" } }, extraProps: { supportVariable: !1 }, name: "padding", display: "block", setter: { componentName: "PaddingNewSetter", props: { initValue: { top: 12, right: 12, bottom: 12, left: 12 }, limit: { top: { min: -1 / 0, max: 1 / 0 }, right: { min: 12, max: 40 }, bottom: { min: -1 / 0, max: 1 / 0 }, left: { min: 12, max: 40 } } } } }, { title: { label: { type: "i18n", "en-US": "Text", "zh-CN": "Text" } }, name: "text", extraProps: { display: "block", onChange: function e(t, n) { var o; n.nodes[0].setPropValue("text", t) } }, setter: { componentName: "RichTextSetter", isRequired: !0, initialValue: "\u8fd9\u662f\u5185\u5bb9" } }, { title: { label: { type: "i18n", "en-US": "Display Options", "zh-CN": "Display Options" } }, extraProps: { supportVariable: !1 }, name: "displayOptions", display: "block", setter: { componentName: "DisplayOptions", props: { defaultValue: "1" } } }], supports: { style: !1 }, component: { nestingRule: { parentWhitelist: ["ThreeCol", "Col", "TwoCol", "EmailCol"] } } }, category: "Blocks", priority: 2 }), {}, { snippets: jt }); function kt(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function Dt(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? kt(Object(n), !0).forEach((function (t) { Ut(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : kt(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function Ut(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var Vt, Tt = [{ title: "3", screenshot: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHYAAAAyCAYAAACJbi9rAAAAAXNSR0IArs4c6QAAAuNJREFUeF7tnLGPDFEcx7+/IeLkosFSCYlGRyca/gKSI1u7W5Ezk9MqNFtJtC4zLmLv1Bsk/AU0oqPTSIiKRYM4EeYnu3fLmrXm/d7NXPLkO+3+3pvffj7z3vvuFiPg9V8SkOG36na7U73ex+PR1mj/Zn7T/Hv+utHY+bjZbK6W3Zc9TiZU5DgQm2WdBQWuApgug1vH5wJ8AnAljluLk+Znj+XkRzlKmq7MSKR3VFUEeAHo+/IpqqyQ3QocEhHVXM4myey94uzs0YX3nxwlzW49BeQIBLeR6xhUlyk3XBPJDBTnAH2WxOePjollj26IRzhKmi1/AXRKoE1VfHWbodoqEWxXSBeQ1SSe2zEulj26EB/lKGnW0cEg1VMug2urEXnQnzuJW78C3fBe7NFAfZ0jxRqYIaCHj2Ip1kLAozag1RDCccEVa3kGA3r4KJZiLQQ8agNaDdyKLX4p1kJrci1/7nhwDOjh4xlr8UuxFlrrtQFB4xlr8UuxFlo8Y6uhFd6uwjPWYj6gXYViKdZCwKM2oNXA8GTxS7EWWgxP1dBiePLnyBXrz250JP9S9OAY0MPHVGzxS7EWWuGdX0zFFr8BrQaKpVgLgWpqGZ48OAa0qzA8WfxSrIUWw5MHLf7zVAc0hicL1YC2OYqlWAuBamqZij04BrSrMBVb/FKshRZTsQctpuI6oDE8WagGtM1RLMVaCFRTy1TswTGgXYWp2OKXYi20mIo9aDml4h8AIgFOq+raq4E2+ZL+a9mA+wDyJG5tKd4+zTrs0cHJKEfJbiy/VNUDULkM5M8dxtdQEh2G6DUReRVfnDtYvAF7dEX+m6Nk2cp1Rb4A4IMCd0Xknes0VdSp6h4BzgDYJYgW43j20phY9liKushR2u3utsbez49U9Vjp6BoLRORJ7+30iXa7+a14G/boDn7IcfB6u/6bT5eWOhcAOakq+9yn2XiliL4B9OH8fOvm2lH794s9/pt1keNP0JxmrDR3ebYAAAAASUVORK5CYII=", schema: { componentName: "ThreeCol", props: {} } }], Mt = Dt(Dt({}, { group: "Fed", componentName: "ThreeCol", title: "ThreeCol", docUrl: "", screenshot: "", devMode: "procode", npm: { package: "iwhale-lowcode-components", version: "{{version}}", exportName: "Layout", main: "", destructuring: !0, subName: "" }, configure: { props: [{ title: { label: { type: "i18n", "en-US": "Column Layout", "zh-CN": "Column Layout" } }, name: "Column Layout", extraProps: { onChange: function e(t, n) { return he(t, n) }, display: "block" }, setter: { componentName: "IwhaleRadioGroupSetter", isRequired: !1, initialValue: "3", props: { options: [{ value: "1", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "2px" } }, ge.a.createElement("svg", { t: "1651747586003", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19150", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.246322 35.57962m52.985286 0l1430.602728 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-1430.602728 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19151" }))), 1]) }, { value: "2", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747900057", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19300", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.246322 35.57962m52.985286 0l609.330791 0q52.985286 0 52.985287 52.985286l0 503.360219q0 52.985286-52.985287 52.985286l-609.330791 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19301" }), ge.a.createElement("path", { d: "M834.518258 35.57962m52.985286 0l609.330792 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-609.330792 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19302" }))), 2]) }, { value: "3", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747930736", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19451", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.246322 35.57962m52.985286 0l344.40436 0q52.985286 0 52.985287 52.985286l0 503.360219q0 52.985286-52.985287 52.985286l-344.40436 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19452" }), ge.a.createElement("path", { d: "M556.345505 35.57962m52.985287 0l344.40436 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-344.40436 0q-52.985286 0-52.985287-52.985286l0-503.360219q0-52.985286 52.985287-52.985286Z", "p-id": "19453" }), ge.a.createElement("path", { d: "M1099.444689 35.57962m52.985287 0l344.40436 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-344.40436 0q-52.985286 0-52.985287-52.985286l0-503.360219q0-52.985286 52.985287-52.985286Z", "p-id": "19454" }))), 3]) }, { value: "4", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747957237", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19603", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.238892 36.592299m52.95557 0l211.82228 0q52.95557 0 52.955569 52.95557l0 503.077913q0 52.95557-52.955569 52.95557l-211.82228 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19604" }), ge.a.createElement("path", { d: "M1231.216998 36.592299m52.95557 0l211.822279 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-211.822279 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19605" }), ge.a.createElement("path", { d: "M825.233122 36.592299m52.95557 0l211.822279 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-211.822279 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19606" }), ge.a.createElement("path", { d: "M419.222768 36.592299m52.95557 0l211.822279 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-211.822279 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19607" }))), 4]) }, { value: "1:2", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747990437", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19756", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.238892 36.592299m52.95557 0l370.688989 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-370.688989 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19757" }), ge.a.createElement("path", { d: "M569.272375 36.592299m52.95557 0l873.766902 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-873.766902 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19758" }))), "1:2"]) }, { value: "2:1", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651748018484", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19907", width: "31", height: "13" }, ge.a.createElement("path", { d: "M1072.350289 36.592299m52.955569 0l370.688989 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-370.688989 0q-52.95557 0-52.955569-52.95557l0-503.077913q0-52.95557 52.955569-52.95557Z", "p-id": "19908" }), ge.a.createElement("path", { d: "M13.238892 36.592299m52.95557 0l873.766902 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-873.766902 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19909" }))), "2:1"]) }] } } }, { title: { label: { type: "i18n", "en-US": "Margin Space", "zh-CN": "Margin Space" } }, name: "marginSpace", extraProps: { display: "block" }, setter: { componentName: "MarginSpaceSetter", initialValue: "transparent" } }, { title: { label: { type: "i18n", "en-US": "Background Type", "zh-CN": "Background Type" } }, name: "backgroundType", extraProps: { display: "block" }, setter: { componentName: "TypeSelectType", initialValue: "content", props: { options: [{ value: "content", title: "Content Width" }, { value: "full", title: "Full Width" }] } } }, { title: { label: { type: "i18n", "en-US": "Body Color", "zh-CN": "Body Color" } }, name: "background", extraProps: { display: "block" }, condition: function e(t) { return "full" === t.getProps().getPropValue("backgroundType") }, setter: { componentName: "BackgroundColorSetter", initialValue: "transparent" } }], supports: { style: !1 }, component: { isContainer: !0, nestingRule: { childWhitelist: function e(t, n) { return n.getChildren().size < 4 && "Col" === t.componentName } } }, advanced: { initialChildren: [{ componentName: "Col", props: { colSpan: "8" } }, { componentName: "Col", props: { colSpan: "8" } }, { componentName: "Col", props: { colSpan: "8" } }], callbacks: { onNodeAdd: function e(t, n) { for (var o = n.children.size, r = 0; r < o; r++)n.children.get(r).getPropValue("DoNotHandleNodeAdd") || n.children.get(r).setPropValue("colSpan", Math.floor(24 / o)) }, onNodeRemove: function e(t, n) { for (var o = n.children.size, r = 0; r < o; r++)n.children.get(r).setPropValue("colSpan", Math.floor(24 / o)) } } } }, category: "Layout" }), {}, { snippets: Tt }); function Rt(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function Wt(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? Rt(Object(n), !0).forEach((function (t) { It(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Rt(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function It(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var zt, Qt = [{ title: "2", screenshot: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHYAAAAyCAYAAACJbi9rAAAAAXNSR0IArs4c6QAAAmVJREFUeF7tXL+PDVEYPWeIWNlo8KiERKOjEw1/AcmSV9t9Imsmq1VoXiXR2szYiLerfkHCX0AjOjqNhKh4aBArwhx5P2xEZteWe7+cW71iiu/c891zzn1z5xIeIWeAf1D1+/2pweDzyWx7djBlpPXP+m2rtftpu91e3QyOqLhHxFZVb0HAdQDTm5mMrf4MgS8AruV5Z3GjWiPjZlmuzDDTPUkk8ArQx61O3Mb1ca+AIySlmueLYvZB0/PRcbOs7jwHeAzEXdRqnITkiM44A+ECoBdFfvF4I7HBcbOslr8BmiLUlvA9ORIbCiaxU2Af4GqRz+1qJjY2bpZVTyPg0pkIpK5hIB8Nfxd5Zy0g/o0vOm4TG7ShTayJTUyoLcX22MRa9j+7vXG2sBRbihPra0uxpTixlrUUex/rPyjiLNqJBTk8OTwl1tQOTw5PibWsw5PDk8NTnEXr8BTbgpyKnYoTUyun4tiS5BMUQSXJxJrYxLxmnXKdimNbkFNxUKUysSY2MQvydie21zgVB5UkE2tiE/Mab3f87U6Mlp2gcHhyeIrY0N7HBs0WJtbEJiZY9lh7bGIt61OKPqXoU4pxFq3fx8a2IKfiwKn4F4CMwFlJ46uBEh8cXssGPARQF3lnWxOcsuqFxs3q1vJrSYcgXgXql4lzOik/OwrqBsk3+eW5w02YouNmVa3cFOoFAJ8E3Cf5IWVyJe0jcA7AHiJbzPPZK43EBsfNbre/o7X/6xNJJ1Im9N/aST4bvJ8+1e22fzThio579EprePPp0lLvEsDTEg+kTDCpd4Aez893bo+tdv0RGfdv6WrbjoODFXwAAAAASUVORK5CYII=", schema: { componentName: "TwoCol", props: {} } }], Zt = Wt(Wt({}, { group: "Fed", componentName: "TwoCol", title: "TwoCol", docUrl: "", screenshot: "", devMode: "procode", npm: { package: "iwhale-lowcode-components", version: "{{version}}", exportName: "Layout", main: "", destructuring: !0, subName: "" }, configure: { props: [{ title: { label: { type: "i18n", "en-US": "col", "zh-CN": "col" } }, name: "column Layout", extraProps: { onChange: function e(t, n) { return he(t, n) }, display: "block" }, setter: { componentName: "IwhaleRadioGroupSetter", isRequired: !1, initialValue: "2", props: { options: [{ value: "1", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747586003", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19150", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.246322 35.57962m52.985286 0l1430.602728 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-1430.602728 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19151" }))), 1]) }, { value: "2", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747900057", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19300", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.246322 35.57962m52.985286 0l609.330791 0q52.985286 0 52.985287 52.985286l0 503.360219q0 52.985286-52.985287 52.985286l-609.330791 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19301" }), ge.a.createElement("path", { d: "M834.518258 35.57962m52.985286 0l609.330792 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-609.330792 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19302" }))), 2]) }, { value: "3", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747930736", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19451", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.246322 35.57962m52.985286 0l344.40436 0q52.985286 0 52.985287 52.985286l0 503.360219q0 52.985286-52.985287 52.985286l-344.40436 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19452" }), ge.a.createElement("path", { d: "M556.345505 35.57962m52.985287 0l344.40436 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-344.40436 0q-52.985286 0-52.985287-52.985286l0-503.360219q0-52.985286 52.985287-52.985286Z", "p-id": "19453" }), ge.a.createElement("path", { d: "M1099.444689 35.57962m52.985287 0l344.40436 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-344.40436 0q-52.985286 0-52.985287-52.985286l0-503.360219q0-52.985286 52.985287-52.985286Z", "p-id": "19454" }))), 3]) }, { value: "4", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747957237", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19603", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.238892 36.592299m52.95557 0l211.82228 0q52.95557 0 52.955569 52.95557l0 503.077913q0 52.95557-52.955569 52.95557l-211.82228 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19604" }), ge.a.createElement("path", { d: "M1231.216998 36.592299m52.95557 0l211.822279 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-211.822279 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19605" }), ge.a.createElement("path", { d: "M825.233122 36.592299m52.95557 0l211.822279 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-211.822279 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19606" }), ge.a.createElement("path", { d: "M419.222768 36.592299m52.95557 0l211.822279 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-211.822279 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19607" }))), 4]) }, { value: "1:2", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747990437", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19756", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.238892 36.592299m52.95557 0l370.688989 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-370.688989 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19757" }), ge.a.createElement("path", { d: "M569.272375 36.592299m52.95557 0l873.766902 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-873.766902 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19758" }))), "1:2"]) }, { value: "2:1", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651748018484", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19907", width: "31", height: "13" }, ge.a.createElement("path", { d: "M1072.350289 36.592299m52.955569 0l370.688989 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-370.688989 0q-52.95557 0-52.955569-52.95557l0-503.077913q0-52.95557 52.955569-52.95557Z", "p-id": "19908" }), ge.a.createElement("path", { d: "M13.238892 36.592299m52.95557 0l873.766902 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-873.766902 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19909" }))), "2:1"]) }] } } }, { title: { label: { type: "i18n", "en-US": "Margin Space", "zh-CN": "Margin Space" } }, name: "marginSpace", extraProps: { display: "block" }, setter: { componentName: "MarginSpaceSetter", initialValue: "transparent" } }, { title: { label: { type: "i18n", "en-US": "Background Type", "zh-CN": "Background Type" } }, name: "backgroundType", extraProps: { display: "block" }, setter: { componentName: "TypeSelectType", initialValue: "content", props: { options: [{ value: "content", title: "Content Width" }, { value: "full", title: "Full Width" }] } } }, { title: { label: { type: "i18n", "en-US": "Body Color", "zh-CN": "Body Color" } }, name: "background", extraProps: { display: "block" }, condition: function e(t) { return "full" === t.getProps().getPropValue("backgroundType") }, setter: { componentName: "BackgroundColorSetter", initialValue: "transparent" } }], supports: { style: !1 }, component: { isContainer: !0, nestingRule: { childWhitelist: function e(t, n) { return !(n.getChildren().size < 4) || "Col" === t.componentName } } }, advanced: { initialChildren: [{ componentName: "Col", props: { colSpan: "12" } }, { componentName: "Col", props: { colSpan: "12" } }], callbacks: {} } }, category: "Layout" }), {}, { snippets: Qt }); function Yt(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function Ft(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? Yt(Object(n), !0).forEach((function (t) { Gt(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : Yt(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function Gt(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } var Lt, Kt = [{ title: "2/3:1/3", screenshot: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADsAAAAYCAYAAABEHYUrAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAO6ADAAQAAAABAAAAGAAAAACfDBzTAAAAxklEQVRYCe2YsRGDMAxFJR8rsAnLZIBUFMxCQZoMkGXIJN4BYYmYS6EOFzoh3VGg4t///7kSLsv7AZhmAOjLZ2Ey0DaN4/PDZhr5E81OghK8AOhrISlhGvAoX8K28Fc1UwlYiNoIymUjbevhif9kLvurmhz2NhNhvaIOskHWQQPxjB1AVCMEWbUWB8sg6wCiGiHIqrU4WAZZBxDVCEFWrcXB8nZkMx+krID7ecl/fi77q5odny3lmodo4pSKQHL2PMM28Fc1d5fUO1uzM2MNAAAAAElFTkSuQmCC", schema: { componentName: "TwoOneCol", props: {} } }], Xt = Ft(Ft({}, { group: "Fed", componentName: "TwoOneCol", title: "TwoOneCol", docUrl: "", screenshot: "", devMode: "procode", npm: { package: "iwhale-lowcode-components", version: "{{version}}", exportName: "Layout", main: "", destructuring: !0, subName: "" }, configure: { props: [{ title: { label: { type: "i18n", "en-US": "Column Layout", "zh-CN": "Column Layout" } }, name: "Column Layout", extraProps: { onChange: function e(t, n) { return he(t, n) }, display: "block" }, setter: { componentName: "IwhaleRadioGroupSetter", isRequired: !1, initialValue: "2:1", props: { options: [{ value: "1", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "2px" } }, ge.a.createElement("svg", { t: "1651747586003", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19150", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.246322 35.57962m52.985286 0l1430.602728 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-1430.602728 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19151" }))), 1]) }, { value: "2", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747900057", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19300", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.246322 35.57962m52.985286 0l609.330791 0q52.985286 0 52.985287 52.985286l0 503.360219q0 52.985286-52.985287 52.985286l-609.330791 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19301" }), ge.a.createElement("path", { d: "M834.518258 35.57962m52.985286 0l609.330792 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-609.330792 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19302" }))), 2]) }, { value: "3", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747930736", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19451", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.246322 35.57962m52.985286 0l344.40436 0q52.985286 0 52.985287 52.985286l0 503.360219q0 52.985286-52.985287 52.985286l-344.40436 0q-52.985286 0-52.985286-52.985286l0-503.360219q0-52.985286 52.985286-52.985286Z", "p-id": "19452" }), ge.a.createElement("path", { d: "M556.345505 35.57962m52.985287 0l344.40436 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-344.40436 0q-52.985286 0-52.985287-52.985286l0-503.360219q0-52.985286 52.985287-52.985286Z", "p-id": "19453" }), ge.a.createElement("path", { d: "M1099.444689 35.57962m52.985287 0l344.40436 0q52.985286 0 52.985286 52.985286l0 503.360219q0 52.985286-52.985286 52.985286l-344.40436 0q-52.985286 0-52.985287-52.985286l0-503.360219q0-52.985286 52.985287-52.985286Z", "p-id": "19454" }))), 3]) }, { value: "4", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747957237", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19603", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.238892 36.592299m52.95557 0l211.82228 0q52.95557 0 52.955569 52.95557l0 503.077913q0 52.95557-52.955569 52.95557l-211.82228 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19604" }), ge.a.createElement("path", { d: "M1231.216998 36.592299m52.95557 0l211.822279 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-211.822279 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19605" }), ge.a.createElement("path", { d: "M825.233122 36.592299m52.95557 0l211.822279 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-211.822279 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19606" }), ge.a.createElement("path", { d: "M419.222768 36.592299m52.95557 0l211.822279 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-211.822279 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19607" }))), 4]) }, { value: "1:2", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651747990437", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19756", width: "31", height: "13" }, ge.a.createElement("path", { d: "M13.238892 36.592299m52.95557 0l370.688989 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-370.688989 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19757" }), ge.a.createElement("path", { d: "M569.272375 36.592299m52.95557 0l873.766902 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-873.766902 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19758" }))), "1:2"]) }, { value: "2:1", title: ge.a.createElement("div", { style: { display: "flex", flexDirection: "column" } }, [ge.a.createElement("span", { style: { display: "block", width: "31px", height: "13px", marginTop: "8px", marginBottom: "8px" } }, ge.a.createElement("svg", { t: "1651748018484", class: "icon", viewBox: "0 0 2396 1024", version: "1.1", xmlns: "http://www.w3.org/2000/svg", "p-id": "19907", width: "31", height: "13" }, ge.a.createElement("path", { d: "M1072.350289 36.592299m52.955569 0l370.688989 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-370.688989 0q-52.95557 0-52.955569-52.95557l0-503.077913q0-52.95557 52.955569-52.95557Z", "p-id": "19908" }), ge.a.createElement("path", { d: "M13.238892 36.592299m52.95557 0l873.766902 0q52.95557 0 52.95557 52.95557l0 503.077913q0 52.95557-52.95557 52.95557l-873.766902 0q-52.95557 0-52.95557-52.95557l0-503.077913q0-52.95557 52.95557-52.95557Z", "p-id": "19909" }))), "2:1"]) }] } } }, { title: { label: { type: "i18n", "en-US": "Margin Space", "zh-CN": "Margin Space" } }, name: "marginSpace", extraProps: { display: "block" }, setter: { componentName: "MarginSpaceSetter", initialValue: "transparent" } }, { title: { label: { type: "i18n", "en-US": "Background Type", "zh-CN": "Background Type" } }, name: "backgroundType", extraProps: { display: "block" }, setter: { componentName: "TypeSelectType", initialValue: "content", props: { options: [{ value: "content", title: "Content Width" }, { value: "full", title: "Full Width" }] } } }, { title: { label: { type: "i18n", "en-US": "Body Color", "zh-CN": "Body Color" } }, name: "background", extraProps: { display: "block" }, condition: function e(t) { return "full" === t.getProps().getPropValue("backgroundType") }, setter: { componentName: "BackgroundColorSetter", initialValue: "transparent" } }], supports: { style: !1 }, component: { isContainer: !0, nestingRule: { childWhitelist: function e(t, n) { return n.getChildren().size < 4 && "Col" === t.componentName } } }, advanced: { initialChildren: [{ componentName: "Col", props: { colSpan: "16" } }, { componentName: "Col", props: { colSpan: "8" } }], callbacks: { onNodeAdd: function e(t, n) { for (var o = n.children.size, r = 0; r < o; r++)n.children.get(r).getPropValue("DoNotHandleNodeAdd") || n.children.get(r).setPropValue("colSpan", Math.floor(24 / o)) }, onNodeRemove: function e(t, n) { for (var o = n.children.size, r = 0; r < o; r++)n.children.get(r).setPropValue("colSpan", Math.floor(24 / o)) } } } }, category: "Layout" }), {}, { snippets: Kt }); function Ht(e, t) { return $t(e) || _t(e, t) || nn(e, t) || Jt() } function Jt() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") } function _t(e, t) { var n = null == e ? null : "undefined" != typeof Symbol && e[Symbol.iterator] || e["@@iterator"]; if (null != n) { var o = [], r = !0, l = !1, a, i; try { for (n = n.call(e); !(r = (a = n.next()).done) && (o.push(a.value), !t || o.length !== t); r = !0); } catch (e) { l = !0, i = e } finally { try { r || null == n.return || n.return() } finally { if (l) throw i } } return o } } function $t(e) { if (Array.isArray(e)) return e } function en(e) { return rn(e) || on(e) || nn(e) || tn() } function tn() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.") } function nn(e, t) { if (e) { if ("string" == typeof e) return ln(e, t); var n = Object.prototype.toString.call(e).slice(8, -1); return "Object" === n && e.constructor && (n = e.constructor.name), "Map" === n || "Set" === n ? Array.from(e) : "Arguments" === n || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n) ? ln(e, t) : void 0 } } function on(e) { if ("undefined" != typeof Symbol && null != e[Symbol.iterator] || null != e["@@iterator"]) return Array.from(e) } function rn(e) { if (Array.isArray(e)) return ln(e) } function ln(e, t) { (null == t || t > e.length) && (t = e.length); for (var n = 0, o = new Array(t); n < t; n++)o[n] = e[n]; return o } function an(e, t) { var n = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); t && (o = o.filter((function (t) { return Object.getOwnPropertyDescriptor(e, t).enumerable }))), n.push.apply(n, o) } return n } function pn(e) { for (var t = 1; t < arguments.length; t++) { var n = null != arguments[t] ? arguments[t] : {}; t % 2 ? an(Object(n), !0).forEach((function (t) { cn(e, t, n[t]) })) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : an(Object(n)).forEach((function (t) { Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t)) })) } return e } function cn(e, t, n) { return t in e ? Object.defineProperty(e, t, { value: n, enumerable: !0, configurable: !0, writable: !0 }) : e[t] = n, e } function sn(e) { return (sn = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (e) { return typeof e } : function (e) { return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : typeof e })(e) } var mn = {}; function dn(e) { var t = [{ title: "\u5e38\u7528", icon: "", children: [] }, { title: "\u5bb9\u5668", icon: "", children: [] }, { title: "\u5bfc\u822a", icon: "", children: [] }, { title: "\u5185\u5bb9", icon: "", children: [] }, { title: "Feedback \u53cd\u9988", icon: "", children: [] }], n = { "\u539f\u5b50\u7ec4\u4ef6": !0 }, o = {}; return e.forEach((function (e) { var r = e.category || "\u5176\u4ed6"; e.group && !o[e.componentName] && (o[e.componentName] = e.group), e.group && !n[e.group] && (n[e.group] = !0); var l = t.find((function (e) { return e.title === r })); l || (l = { title: r, icon: "", children: [] }, t.push(l)), e.snippets && e.snippets.length && l.children.push({ componentName: e.componentName, title: e.title || e.componentName, sort: { category: l.title, group: o[e.componentName] || "\u539f\u5b50\u7ec4\u4ef6", priority: mn[l.title] || 0 }, icon: "", package: e.npm.pkg, snippets: e.snippets || [] }) })), t } function un(e) { var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "iwhale-lowcode-components", n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : "0.3.20", o = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : { "@alifd/next": "1.25.23", "@alifd/meet": "2.6.3", antd: "4.17.3" }; if (!e || !n) return e; var r = e.npm; return r ? ("object" === sn(o) && o[r.package] ? e.npm = pn(pn({}, r), {}, { version: o[r.package] }) : r.package === t && (e.npm = pn(pn({}, r), {}, { version: n })), e) : e } ["\u57fa\u7840\u5143\u7d20", "\u5e03\u5c40\u5bb9\u5668\u7c7b", "\u8868\u683c\u7c7b", "\u8868\u5355\u8be6\u60c5\u7c7b", "\u5e2e\u52a9\u7c7b", "\u5bf9\u8bdd\u6846\u7c7b", "\u4e1a\u52a1\u7c7b", "\u901a\u7528", "\u5f15\u5bfc", "\u4fe1\u606f\u8f93\u5165", "\u4fe1\u606f\u5c55\u793a", "\u4fe1\u606f\u53cd\u9988"].reverse().forEach((function (e, t) { mn[e] = ++t })); var An, gn = [], hn = {};[P, B, M, Z, X, te, ie, ue, xe, qe, Ve, ze, Le, $e, lt, mt, yt, ft, Pt, Bt, Mt, Zt, Xt].forEach((function (e) { if (Array.isArray(e)) gn.push.apply(gn, en(e.map((function (e) { if (!e.npm) { var t = e.componentName, n = t.split("."), o = Ht(n, 2), r = o[0], l = o[1]; e.npm = { exportName: r, main: "", destructuring: !0, subName: n.length > 1 ? t.slice(t.indexOf(".") + 1) : l } } return e.npm = pn(pn({}, hn), e.npm || {}), un(e) })))); else if (e.components) gn.push.apply(gn, en(e.components.map((function (e) { if (!e.npm) { var t = e.componentName, n = t.split("."), o = Ht(n, 2), r = o[0], l = o[1]; e.npm = { exportName: r, main: "", destructuring: !0, subName: n.length > 1 ? t.slice(t.indexOf(".") + 1) : l } } return e.npm = pn(pn({}, hn), e.npm || {}), un(e) })))); else { if (!e.npm) { var t = e.componentName, n = t.split("."), o = Ht(n, 2), r = o[0], l = o[1]; e.npm = { exportName: r, main: "", destructuring: !0, subName: n.length > 1 ? t.slice(t.indexOf(".") + 1) : l } } e.npm = pn(pn({}, hn), e.npm || {}), gn.push(un(e)) } })); var yn = dn(gn), bn = !0 }]) }));