const ENV = '133';
const HOST_MAP = {
  133: 'http://*************:8588',
  co3: 'https://test-digital.dito.ph:8088',
  co37: 'https://pre-digital.dito.ph:8093',
  co3T: 'https://pretest-digital.dito.ph:7093',
  co3ST: 'https://preprod-digital.dito.ph:9093',
  co51T: 'https://co51t-digital.dito.ph:9093',
};
const PROMOTION_HOST_MAP = {
  133: 'http://*************:8010',
  co3: 'https://test-digital.dito.ph:8088',
  co37: 'https://pre-digital.dito.ph:8093',
  co3T: 'https://pretest-digital.dito.ph:7093',
};

const commonConfigs = {
  target: `${HOST_MAP[ENV]}/ecare/`,
  changeOrigin: true,
  secure: false, // 不进行证书验证
};

export default {
  dev: {
    '/ecare/mktAd': {
      ...commonConfigs,
      pathRewrite: { '^/ecare': '/' },
    },
    '/ecare/webs/': {
      ...commonConfigs,
      pathRewrite: { '^/ecare/webs': '/webs' },
    },
    '/dmc/mccm/': {
      ...commonConfigs,
      target: ENV === '133' ? 'http://*************' : HOST_MAP[ENV],
    },
    '/ecare/ads/': {
      ...commonConfigs,
      pathRewrite: { '^/ecare/ads': '/ads' },
    },
    '/ecare/shop/': {
      ...commonConfigs,
      pathRewrite: { '^/ecare/shop': '/shop' },
    },
    '/ecare/ceeOffer/': {
      ...commonConfigs,
      pathRewrite: { '^/ecare/ceeOffer': '/ceeOffer' },
    },
    '/promotion-rest-boot': {
      target: PROMOTION_HOST_MAP[ENV],
      secure: false,
      // changeOrigin: true,
    },
    '/eshopweb/api/': {
      ...commonConfigs,
      target: `${HOST_MAP[ENV]}/eshopweb/`,
      pathRewrite: { '^/eshopweb/api': '/api' },
    },
    '/portal-web/drm/': {
      ...commonConfigs,
      target: 'https://digding.iwhalecloud.com/',
      // https://digding.iwhalecloud.com/portal-web/drm/selfserv/openserv/property/config/value/INTENTION_PARTNER_CHANNEL
      // pathRewrite: { '^/drm': '/api' },
    },
    '/get/resource/': {
      ...commonConfigs,
      target: `${HOST_MAP[ENV]}//`,
    },
    '/ecare/order/': {
      ...commonConfigs,
      pathRewrite: { '^/ecare/order': '/order' },
    },
    '/ecare/content/': {
      ...commonConfigs,
      pathRewrite: { '^/ecare/content': '/content' },
    },
    '/ecare/common/': {
      ...commonConfigs,
      pathRewrite: { '^/ecare/common': '/common' },
    },
    '/ecare/pto/': {
      ...commonConfigs,
      pathRewrite: { '^/ecare/pto': '/pto' },
    },
    '/ecare/user/': {
      ...commonConfigs,
      pathRewrite: { '^/ecare/user': '/user' },
    },
    '/ecare/accNbr/': {
      ...commonConfigs,
      pathRewrite: { '^/ecare/accNbr': '/accNbr' },
    },
    '/ecare/cust/': {
      ...commonConfigs,
      pathRewrite: { '^/ecare/cust': '/cust' },
    },
    '/ecare/survey/': {
      ...commonConfigs,
      pathRewrite: { '^/ecare/survey': '/survey' },
    },
    '/i18n': {
      ...commonConfigs,
      target: HOST_MAP[ENV],
    },
  },
};
