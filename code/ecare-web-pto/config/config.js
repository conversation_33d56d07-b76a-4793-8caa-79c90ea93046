import { defineConfig } from 'umi';
import proxy from './proxy';
import routerConfig from './router.config';

const { TRACK_LOG } = process.env;

export default defineConfig({
  // title: `MyDITO-${location.pathname}`,
  // 模块热更新
  fastRefresh: {},
  // layout: {},
  routes: routerConfig,
  // history: 'browser',
  hash: true,
  // mock: false,
  // publicPath: '/pto/',
  // base: '/pto/',
  theme: {
    background: '#fff',
    'primary-color': '#F88B00',
    'brand-primary': '#F88B00',
    'font-size-base': '12px',
  },
  targets: { chrome: 49, firefox: 45, safari: 10, edge: 11, ie: 11, ios: 10 },
  nodeModulesTransform: {
    type: 'none',
  },
  locale: {
    default: 'en-US',
    antd: false,
    title: false,
    baseNavigator: true,
    baseSeparator: '-',
  },
  dynamicImport: {
    loading: '@/components/PageLoading',
  },
  dva: {
    immer: true,
    hmr: true,
  },
  proxy: proxy.dev,
  chainWebpack(memo, { webpack }) {
    memo.optimization.splitChunks({
      chunks: 'all',
      automaticNameDelimiter: '_',
      name: true,
      minSize: 30000,
      minChunks: 1,
      cacheGroups: {
        // 只有dynamicRender页会用到，抽取出来
        alilc: {
          name: 'alilc',
          chunks: 'all',
          test({ resource }) {
            return /(@alilc)|(@alifd)/.test(resource);
          },
          minChunks: 1,
          priority: 20,
        },
        antdesign: {
          name: 'antdesign',
          chunks: 'all',
          test: ({ resource }) => /[\\/]node_modules[\\/](antd|@ant-design)/.test(resource),
          priority: 10,
        },
        vendors: {
          name: 'vendors',
          chunks: 'all',
          test: /[\\/]node_modules[\\/](lodash|moment|react|dva)/,
          priority: 10,
        },
        common: {
          name: 'common',
          chunks: 'all',
          minChunks: 5,
          priority: 1,
          enforce: true,
        },
      },
    });
    memo.optimization
      .minimizer('banner-plugin')
      .use(webpack.BannerPlugin, [{ banner: new Date().toLocaleString(), entryOnly: false }]);
  },
  chunks: ['antdesign', 'vendors', 'common', 'umi'],
  sass: {}, // 低代码渲染器使用, 需要先安装插件 @umijs/plugin-sass
  define: {
    ENV_TRACK_LOG: TRACK_LOG, // 是否开启埋点日志的环境变量
  },
  scripts: [{ src: '/lib/DXPNudges.umd.js', defer: true }],
  links: [{ href: '/lib/DXPNudges.css', rel: 'stylesheet' }],
});
