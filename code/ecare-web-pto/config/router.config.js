export default [
  {
    path: '/',
    component: '@/layouts/BlankLayout',
    routes: [
      {
        path: '/invitationCode',
        // component: '@/layouts/LoginLayout',
        routes: [
          {
            exact: true,
            path: '/invitationCode',
            name: 'invitationCode',
            component: '@/pages/InvitationCode',
          },
        ],
      },
      {
        exact: true,
        path: '/claimFreebies',
        component: '@/pages/ClaimFreebies',
      },
      {
        exact: true,
        path: '/nonDisclosureAgreement',
        component: '@/pages/NonDisclosureAgreement',
      },
      {
        exact: true,
        path: '/nonDisclosureAgreement/result',
        component: '@/pages/NonDisclosureAgreement/Result',
      },

      {
        path: '/pto/download',
        routes: [
          {
            exact: true,
            path: '/pto/download',
          },
          {
            exact: true,
            path: '/pto/download/app',
            component: '@/pages/DownloadApp',
          },
          {
            exact: true,
            path: '/pto/download/IOSapp',
            component: '@/pages/DownloadApp',
          },
        ],
      },
      {
        path: '/download',
        routes: [
          {
            exact: true,
            path: '/download',
          },
          {
            exact: true,
            path: '/download/buyPromoApp',
            component: '@/pages/BuyPromoApp',
          },
          {
            exact: true,
            path: '/download/buyPromoSuccess',
            component: '@/pages/BuyPromoApp/BuyPromoAppSuccess',
          },
          {
            exact: true,
            path: '/download/submitAppForm',
            component: '@/pages/SubmitAppForm',
          },
          {
            exact: true,
            path: '/download/specialpromo',
            component: '@/pages/SpecialPromo',
          },
          {
            exact: true,
            path: '/download/specialpromo/success',
            component: '@/pages/SpecialPromo/BuyPromoAppSuccess',
          },
        ],
      },
      {
        path: '/pointshop',
        routes: [
          {
            exact: true,
            path: '/pointshop/home',
            component: '@/pages/PointShop/PointShopHome',
          },
          {
            exact: true,
            path: '/pointshop/allcategories',
            component: '@/pages/PointShop/PointShopHome/AllCategories',
          },
          {
            exact: true,
            path: '/pointshop/allfeatured',
            component: '@/pages/PointShop/PointShopHome/AllFeatured',
          },
          {
            exact: true,
            path: '/pointshop/shoplist',
            component: '@/pages/PointShop/ShopList',
          },
          {
            exact: true,
            path: '/pointshop/details',
            component: '@/pages/PointShop/OfferDetails',
          },
          {
            exact: true,
            path: '/pointshop/search',
            component: '@/pages/PointShop/Search',
          },
          {
            exact: true,
            path: '/pointshop/smsotp',
            component: '@/pages/PointShop/TransactionOTP',
          },
          {
            exact: true,
            path: '/pointshop/earnMorePoints',
            component: '@/pages/PointShop/EarnMorePoints',
          },
          {
            exact: true,
            path: '/pointshop/addEmailAddress',
            component: '@/pages/PointShop/AddEmailAddress',
          },
          {
            exact: true,
            path: '/pointshop/faild',
            component: '@/pages/PointShop/TransactionFaild',
          },
          {
            exact: true,
            path: '/pointshop/successful',
            component: '@/pages/PointShop/SuccessfulPage',
          },
          {
            exact: true,
            path: '/pointshop/shareSuccessful',
            component: '@/pages/PointShop/ShareSuccessPage',
          },
          // payc 定制的成功页
          {
            exact: true,
            path: '/pointshop/paycsuccessful',
            component: '@/pages/PaycSuccess',
          },
          // payc 定制的失败页
          {
            exact: true,
            path: '/pointshop/paycFail',
            component: '@/pages/PaycFail',
          },
        ],
      },
      {
        path: '/h5',
        routes: [
          {
            exact: true,
            path: '/h5/guest/buyPromo',
            name: 'Buy & Manage Promo',
            component: '@/pages/h5/Guest/BuyPromo',
          },
          {
            exact: true,
            path: '/h5/buyPromo',
            name: 'Buy & Manage Promo',
            component: '@/pages/h5/BuyPromo',
          },
          {
            exact: true,
            path: '/h5/autoPayPromo',
            name: 'Auto Pay Promo',
            component: '@/pages/h5/AutoPayPromo',
          },
          {
            exact: true,
            path: '/h5/promo/detail',
            name: 'Promo Detail',
            component: '@/pages/h5/AutoPayPromoDetail',
          },
          {
            exact: true,
            path: '/h5/autoPayPromo/detail',
            name: 'Auto Pay Promo Detail',
            component: '@/pages/h5/AutoPayPromoDetail',
          },
          {
            exact: true,
            path: '/h5/OTT/subscriptionHistory',
            component: '@/pages/h5/OTT/SubscriptionHistory',
          },
          {
            exact: true,
            path: '/h5/OTT/detail',
            name: 'Subscription Detail',
            component: '@/pages/h5/OTT/Detail',
          },
          {
            exact: true,
            path: '/h5/OTT/successful',
            component: '@/pages/h5/OTT/Successful',
          },
          {
            exact: true,
            path: '/h5/RoamingOffer',
            name: 'Roaming Offer',
            component: '@/pages/h5/RoamingOffer',
          },
          {
            exact: true,
            path: '/h5/RoamingOffer/history',
            name: 'Roaming Offer history',
            component: '@/pages/h5/RoamingOffer/history',
          },
          {
            path: '/h5/RoamingOffer/RoamingSuccess',
            name: 'Roaming Success',
            component: '@/pages/h5/RoamingOffer/components/RoamingSuccess',
          },
          {
            exact: true,
            path: '/h5/RoamingOffer/historyDetail',
            name: 'Roaming Offer history detail',
            component: '@/pages/h5/RoamingOffer/historyDetail',
          },
          {
            path: '/h5/RoamingOffer/PromoDetails',
            name: 'Promo Details',
            component: '@/pages/h5/RoamingOffer/PromoDetails',
          },
          {
            path: '/h5/RoamingOffer/PdfError',
            name: 'Pdf error',
            component: '@/pages/h5/RoamingOffer/PdfError',
          },
          {
            path: '/h5/RoamingOffer/OopsError',
            name: 'Oops Error',
            component: '@/pages/h5/RoamingOffer/OopsError',
          },
          {
            path: '/h5/ReNaVerification',
            name: 'Pdf error',
            component: '@/pages/h5/ReNaVerification',
          },
          {
            path: '/h5/ReNaVerification/Success',
            name: 'Pdf error',
            component: '@/pages/h5/ReNaVerification/Success',
          },
          {
            path: '/h5/ReNaVerification/NetError',
            name: 'Net error',
            component: '@/pages/h5/ReNaVerification/NetError',
          },
          {
            path: '/h5/success',
            name: 'Success',
            component: '@/pages/h5/Success',
          },
          {
            path: '/h5/MyVouchers',
            name: 'MyVouchers',
            component: '@/pages/h5/MyVouchers/History',
          },
          {
            path: '/h5/MyVouchers/OopsError',
            name: 'Oops Error',
            component: '@/pages/h5/MyVouchers/OopsError',
          },
          {
            path: '/h5/MyVouchers/VochersDetail',
            name: 'VochersDetail',
            component: '@/pages/h5/MyVouchers/VochersDetail',
          },
          {
            path: '/h5/SIMRegistration',
            name: 'SIMRegistration',
            component: '@/pages/h5/SIMRegistration/Welcome',
          },
          {
            path: '/h5/SIMRegistration/sentry',
            name: 'SIMRegistrationSentry',
            component: '@/pages/h5/SIMRegistration/Sentry',
          },
          {
            path: '/h5/SIMRegistration/welcome',
            name: 'SIMRegistrationWelcome',
            component: '@/pages/h5/SIMRegistration/Welcome',
          },
          {
            path: '/h5/SIMRegistration/entrance',
            name: 'SIMRegistrationEntrance',
            component: '@/pages/h5/SIMRegistration/Entrance',
          },
          {
            path: '/h5/SIMRegistration/guard',
            name: 'SIMRegistrationGuard',
            component: '@/pages/h5/SIMRegistration/Guard',
          },
          {
            path: '/h5/SIMRegistration/attachment/:type',
            name: 'SIMRegistrationAttachment',
            component: '@/pages/h5/SIMRegistration/Attachment',
          },
          {
            path: '/h5/SIMRegistration/ocr',
            name: 'SIMRegistrationOCR',
            component: '@/pages/h5/SIMRegistration/OCR',
          },
          {
            path: '/h5/SIMRegistration/PerfectInfo/:type',
            name: 'PerfectInfo',
            component: '@/pages/h5/SIMRegistration/PerfectInfo',
          },
          {
            path: '/h5/SIMRegistration/PreviewInfo/:type',
            name: 'PerfectInfo',
            component: '@/pages/h5/SIMRegistration/PreviewInfo',
          },
          {
            path: '/h5/SIMRegistration/Success/:type',
            name: 'Success',
            component: '@/pages/h5/SIMRegistration/Success',
          },
          {
            path: '/h5/SIMRegistration/query',
            name: 'SRACheckRegistration',
            component: '@/pages/h5/SIMRegistration/CheckRegistration',
          },
          {
            path: '/h5/SIMRegistration/query/result',
            name: 'SRACheckRegistrationResult',
            component: '@/pages/h5/SIMRegistration/CheckRegistrationResult',
          },
          {
            path: '/h5/SIMRegistration/activation',
            name: 'SRACheckRegistrationActivation',
            component: '@/pages/h5/SIMRegistration/Activation',
          },
          {
            path: '/h5/SIMRegistration/LivenessHome',
            name: 'SRACheckRegistrationLivenessHome',
            component: '@/pages/h5/SIMRegistration/LivenessHome',
          },
          {
            path: '/h5/SIMRegistration/LivenessDetection',
            name: 'SRACheckRegistrationLivenessDetection',
            component: '@/pages/h5/SIMRegistration/LivenessDetection',
          },
          {
            path: '/h5/SIMRegistration/activation/success',
            name: 'SRACheckRegistrationActivationSuccess',
            component: '@/pages/h5/SIMRegistration/ActivationSuccess',
          },
          {
            path: '/h5/Zone',
            name: 'Zone',
            component: '@/pages/h5/Zone/Zone',
          },
          {
            path: '/h5/Zone/Detail',
            name: 'ZoneDetail',
            component: '@/pages/h5/Zone/ZoneDetail',
          },
          {
            path: '/h5/SIMRegistration/RegError',
            name: 'RegError',
            component: '@/pages/h5/SIMRegistration/RegError',
          },
          {
            path: '/h5/FWA/:type',
            name: 'FWA',
            component: '@/pages/h5/FWA',
          },
          // h5桥接 demo页
          {
            path: '/h5/Demo',
            name: 'Demo',
            component: '@/pages/h5/Demo',
          },
          {
            path: '/h5/italk',
            name: 'ITalk',
            component: '@/pages/h5/Italk',
          },
          {
            path: '/h5/ijoin/direct/fwa',
            name: 'FWAEntry',
            component: '@/pages/h5/Ijoin/DirectLink/FWAEntry',
          },
          {
            path: '/h5/ijoin/direct/:business',
            name: 'SpringBoard',
            component: '@/pages/h5/Ijoin/SpringBoard',
          },
          {
            path: '/h5/ijoin/fwapostpaid',
            name: 'FWAPostpaidEntry',
            component: '@/pages/h5/Ijoin/DirectLink/FWAPostpaidEntry',
          },
          {
            path: '/h5/ijoin/fwaprepaid',
            name: 'FWAPostpaidEntry',
            component: '@/pages/h5/Ijoin/DirectLink/FWAPrepaidEntry',
          },
          {
            path: '/h5/ijoin/fwa4pos',
            name: 'FWA4Pos',
            component: '@/pages/h5/Ijoin/DirectLink/FWA4Pos',
          },
          {
            path: '/h5/ijoin/mobile/postpaid/esaf',
            name: 'IJoinMobilePostpaidESAF',
            component: '@/pages/h5/Ijoin/LandingPage/MobilePostpaid4DRP',
          },
          {
            path: '/h5/ijoin/pre2post',
            name: 'ChangePlan',
            component: '@/pages/h5/Ijoin/Pre2Post',
          },
          {
            path: '/h5/ijoin/plan',
            name: 'IJoinPostpaidHome',
            component: '@/pages/h5/Ijoin/Postpaid/Home',
          },
          {
            path: '/h5/ijoin/offers',
            name: 'IJoinSelectPlan',
            component: '@/pages/h5/Ijoin/SelectPlan',
          },
          {
            path: '/h5/ijoin/goods/detail',
            name: 'GoodsDetail',
            component: '@/pages/h5/Ijoin/GoodsDetail',
          },
          {
            path: '/h5/ijoin/number',
            name: 'IJoinSelectPlan',
            component: '@/pages/h5/Ijoin/SeleteNumber',
          },
          {
            path: '/h5/ijoin/odaCheck',
            name: 'Network Coverage Check',
            component: '@/pages/h5/Ijoin/NetworkCoverageCheck',
          },
          {
            path: '/h5/ijoin/goodNews',
            name: 'Good News',
            component: '@/pages/h5/Ijoin/GoodNews',
          },
          {
            path: '/h5/ijoin/attachment',
            name: 'IJoinAttachment',
            component: '@/pages/h5/Ijoin/Attachment',
          },
          {
            path: '/h5/ijoin/ocr',
            name: 'IJoinOCR',
            component: '@/pages/h5/Ijoin/OCR',
          },
          {
            path: '/h5/ijoin/invitation',
            name: 'IJoinInvitation',
            component: '@/pages/h5/Ijoin/Invitation',
          },
          {
            path: '/h5/ijoin/profile',
            name: 'IJoinProfile',
            component: '@/pages/h5/Ijoin/Profile',
          },
          {
            path: '/h5/ijoin/contact',
            name: 'IJoinContact',
            component: '@/pages/h5/Ijoin/Contact',
          },
          {
            path: '/h5/ijoin/employment',
            name: 'IJoinEmployment',
            component: '@/pages/h5/Ijoin/Employment',
          },
          {
            path: '/h5/ijoin/preview',
            name: 'IJoinPreview',
            component: '@/pages/h5/Ijoin/Preview',
          },
          {
            path: '/h5/ijoin/payment',
            name: 'IJoinPayment',
            component: '@/pages/h5/Ijoin/Payment',
          },
          {
            path: '/h5/ijoin/oops',
            name: 'IJoinOops',
            component: '@/pages/h5/Ijoin/Oops',
          },
          {
            path: '/h5/ijoin/custDeliveryAddr',
            name: 'IJoinOops',
            component: '@/pages/h5/Ijoin/CustomerDeliveryAddress',
          },
          {
            path: '/h5/ijoin/success',
            name: 'IJoinSuccess',
            component: '@/pages/h5/Ijoin/Success',
          },
          {
            path: '/h5/ijoin/fail',
            name: 'IJoinFail',
            component: '@/pages/h5/Ijoin/Fail',
          },
          {
            path: '/h5/ijoin/order/detail',
            name: 'IJoinOrderDetail',
            component: '@/pages/h5/Ijoin/OrderDetail',
          },
          {
            path: '/h5/ijoin/delivery/detail',
            name: 'IJoinDeliveryDetail',
            component: '@/pages/h5/Ijoin/DeliveryDetail',
          },
          {
            path: '/h5/ijoin/preference',
            name: 'Preference',
            component: '@/pages/h5/Ijoin/Preference',
          },
          {
            path: '/h5/ijoin/map',
            name: 'IJoinMap',
            component: '@/pages/h5/Ijoin/Map',
          },
          {
            path: '/h5/privacy',
            name: 'H5Privacy',
            component: '@/pages/h5/Privacy',
          },
          {
            path: '/h5/ijoin/ftth/coverage',
            name: 'IJoinFTTHCoverage',
            component: '@/pages/h5/Ijoin/FTTHCoverage',
          },
          {
            path: '/h5/ijoin/ftth/address',
            name: 'IJoinFTTHAddress',
            component: '@/pages/h5/Ijoin/FTTHAddress',
          },
          {
            path: '/h5/ijoin/fwa',
            name: 'IJoinFWAHome',
            component: '@/pages/h5/Ijoin/FWAHome',
          },
          {
            path: '/h5/ijoin/fwa/plan',
            name: 'IJoinFWAPlan',
            component: '@/pages/h5/Ijoin/new/FWAPlan',
          },
          {
            path: '/h5/ijoin/fwa/plan/detail',
            name: 'IJoinFWAPlan',
            component: '@/pages/h5/Ijoin/new/FWAPlanDetail',
          },
          {
            path: '/h5/ijoin/fwa/address',
            name: 'IJoinFWAAddress',
            component: '@/pages/h5/Ijoin/InstallationAddress',
          },
          {
            path: '/h5/ijoin/new/installation',
            name: 'IJoinFWAAddress',
            component: '@/pages/h5/Ijoin/new/Installation',
          },
          {
            path: '/h5/ijoin/new/coverage',
            name: 'Network Coverage Check',
            component: '@/pages/h5/Ijoin/new/Coverage',
          },
          {
            path: '/h5/ijoin/new/profile',
            name: 'Personal Details',
            component: '@/pages/h5/Ijoin/new/Personal',
          },
          {
            path: '/h5/ijoin/new/employment',
            name: 'IJoinEmploymentV2',
            component: '@/pages/h5/Ijoin/new/Employment',
          },
          {
            path: '/h5/ijoin/new/summary',
            name: 'IJoinPreviewV2',
            component: '@/pages/h5/Ijoin/new/Summary',
          },
          {
            path: '/h5/ijoin/new/success',
            name: 'IJoinSuccessV2',
            component: '@/pages/h5/Ijoin/new/Success',
          },
          {
            path: '/h5/ijoin/new/docs',
            name: 'IJoinNewDocs',
            component: '@/pages/h5/Ijoin/new/NewDocs',
          },
          {
            path: '/h5/ijoin/mobile/postpaid/application',
            name: 'IJoinPostpaidApplication',
            component: '@/pages/h5/Ijoin/Postpaid/ApplicationForm',
          },
          {
            path: '/h5/ijoin/mobile/postpaid/contact',
            name: 'IJoinPostpaidContact',
            component: '@/pages/h5/Ijoin/Postpaid/ContactInformation',
          },
          {
            path: '/h5/ijoin/mobile/postpaid/employment',
            name: 'IJoinPostpaidEmployment',
            component: '@/pages/h5/Ijoin/Postpaid/Employment',
          },
          {
            path: '/h5/AutoPayAndAdvancePay',
            name: 'AutoPayAndAdvancePay',
            component: '@/pages/h5/AutoPayAndAdvancePay',
          },
          {
            path: '/h5/survey',
            name: 'Survey',
            component: '@/pages/h5/Survey',
          },
          {
            path: '/h5/promo/country',
            name: 'CountryPromo',
            component: '@/pages/h5/CountryPromo',
          },
          {
            path: '/h5/buyLoad',
            name: 'BuyLoad',
            component: '@/pages/h5/BuyLoad',
          },
          {
            path: '/h5/buyLoad/payment',
            name: 'BuyLoadPayment',
            component: '@/pages/h5/BuyLoad/PublicPayment',
          },
          {
            path: '/h5/buyLoad/success',
            name: 'BuyLoadSuccess',
            component: '@/pages/h5/BuyLoad/Success',
          },
          {
            path: '/h5/buyLoad/fail',
            name: 'BuyLoadFail',
            component: '@/pages/h5/BuyLoad/Fail',
          },
          {
            path: '/h5/justForYou',
            name: 'JustForYou',
            component: '@/pages/h5/JustForYou',
          },
          {
            path: '/h5/offer/home',
            name: 'OfferHome',
            component: '@/pages/h5/Offer/Home',
          },
          {
            path: '/h5/offer/list',
            name: 'OfferList',
            component: '@/pages/h5/Offer/List',
          },

          // #21244471 BRT2024xxxxxx-FWA同城寄送，phase1-新表单采集页面 临时演示需要
          {
            path: '/h5/ijoin/fwa/FWAPlanDemonstration',
            name: 'FWAPlan Demonstration',
            component: '@/pages/h5/Ijoin/new/FWAPlanDemonstration',
          },
          {
            path: '/h5/ijoin/new/coverageDemonstration',
            name: 'Network Coverage Check Demonstration',
            component: '@/pages/h5/Ijoin/new/CoverageDemonstration',
          },
          {
            path: '/h5/ijoin/new/profileDemonstration',
            name: 'Personal Details Demonstration',
            component: '@/pages/h5/Ijoin/new/PersonalDemonstration',
          },
          {
            path: '/h5/ijoin/new/successDemonstration',
            name: 'IJoinSuccessV2 Demonstration',
            component: '@/pages/h5/Ijoin/new/SuccessDemonstration',
          },
          {
            path: '/h5/ijoin/mapDemonstration',
            name: 'IJoinMap',
            component: '@/pages/h5/Ijoin/MapDemonstration',
          },
          {
            path: '/h5/ijoin/new/summaryDemonstration',
            name: 'SummaryDemonstration',
            component: '@/pages/h5/Ijoin/new/SummaryDemonstration',
          },
          // ESIM
          { path: '/h5/esim/home', name: 'ESIMHome', component: '@/pages/h5/esim/Home' },
          { path: '/h5/esim/compatibility', name: 'ESIMCompatibility', component: '@/pages/h5/esim/Compatibility' },
          { path: '/h5/esim/detail', name: 'ESIMDetail', component: '@/pages/h5/esim/Detail' },
          { path: '/h5/esim/contact', name: 'ESIMContact', component: '@/pages/h5/esim/Contact' },
          { path: '/h5/esim/payment', name: 'ESIMPayment', component: '@/pages/h5/esim/Payment' },
          { path: '/h5/esim/success', name: 'ESIMSuccess', component: '@/pages/h5/esim/Success' },
          { path: '/h5/esim/qrcode', name: 'ESIMQRCode', component: '@/pages/h5/esim/QRCode' },
          { path: '/h5/esim/order', name: 'ESIMOrder', component: '@/pages/h5/esim/Order' },
        ],
      },
      {
        path: '/dynamicRender',
        component: '@/pages/DynamicRender',
      },
      {
        path: '/portal',
        component: '@/layouts/PortalLayout',
        routes: [
          { exact: true, path: '/portal/contact', component: '@/pages/Portal/ContactUs' },
          { exact: true, path: '/portal/partner', component: '@/pages/Portal/PartnerWithUs' },
          { exact: true, path: '/portal/support', component: '@/pages/Portal/ContactPartnerSupport' },
          { exact: true, path: '/portal/success', component: '@/pages/Portal/ParntersPortalSuccess' },
          { exact: true, path: '/portal/fail', component: '@/pages/Portal/ParntersPortalFail' },
        ],
      },
      {
        path: '/help',
        component: '@/layouts/LoginLayout',
        routes: [
          {
            path: '/help',
            name: 'help',
            component: '@/pages/Help',
            routes: [
              {
                path: '/help',
                name: 'help',
                component: '@/pages/Help/components/HelpHome',
              },
              {
                path: '/help/:id',
                name: 'help',
                component: '@/pages/Help/components/SearchInfo',
              },
            ],
          },
        ],
      },
      {
        path: '/pto',
        component: '@/layouts/BaseLayout',
        routes: [
          {
            exact: true,
            path: '/pto',
            name: 'home',
            component: '@/pages/Home',
          },
          { exact: true, path: '/pto/home', component: '@/pages/Home', title: 'MyDITO - Home' },
          { exact: true, path: '/pto/BuyLoad', component: '@/pages/BuyLoad', title: 'MyDITO - Buy Load' },
          { exact: true, path: '/pto/promo', component: '@/pages/Promo', title: 'MyDITO - Buy Promo' },
          { exact: true, path: '/pto/Profile', component: '@/pages/Profile', title: 'MyDITO - Profile' },
          { exact: true, path: '/pto/share', component: '@/pages/Share', title: 'MyDITO - Share Load' },
          { exact: true, path: '/pto/helpTicket', component: '@/pages/HelpTicket', title: 'MyDITO - HelpTicket' },
          {
            exact: true,
            path: '/pto/notifications',
            component: '@/pages/Notification',
            title: 'MyDITO - Notifications',
          },
          { exact: true, path: '/pto/history', component: '@/pages/History' },
        ],
      },
      {
        path: '/error',
        component: '@/pages/Error',
      },
      {
        path: '/',
        component: '@/layouts/LoginLayout',
        routes: [
          {
            exact: true,
            path: '/',
            name: 'login',
            component: '@/pages/Login',
            title: 'MyDITO',
          },
        ],
      },
    ],
  },
  // {
  //   component: './404',
  // },
];
